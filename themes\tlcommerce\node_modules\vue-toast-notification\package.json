{"_args": [["vue-toast-notification@3.1.1", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue-toast-notification@3.1.1", "_id": "vue-toast-notification@3.1.1", "_inBundle": false, "_integrity": "sha512-DXEE38NxXFCNinKI+MyZBMWsaqE9Lwxg9GkMVkzKXY1ACCeNztBXuUiSvKMe36dnnYd1qnlP+cWNSvHlkt5xNA==", "_location": "/vue-toast-notification", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-toast-notification@3.1.1", "name": "vue-toast-notification", "escapedName": "vue-toast-notification", "rawSpec": "3.1.1", "saveSpec": null, "fetchSpec": "3.1.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue-toast-notification/-/vue-toast-notification-3.1.1.tgz", "_spec": "3.1.1", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "ankurk91"}, "bugs": {"url": "https://github.com/ankurk91/vue-toast-notification/issues"}, "description": "Vue.js toast notification plugin", "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@vue/compiler-sfc": "^3.2.45", "@vue/test-utils": "^2.2.7", "@vue/vue3-jest": "^29.2.2", "babel-jest": "^29.3.1", "babel-loader": "^9.1.2", "bootstrap": "^4.6.2", "cross-env": "^7.0.3", "css-loader": "^6.7.3", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.5.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "mini-css-extract-plugin": "^2.7.2", "mitt": "^3.0.0", "sass": "~1.57.1", "sass-loader": "^13.2.0", "style-loader": "^3.3.1", "svg-url-loader": "^8.0.0", "vue": "^3.2.45", "vue-loader": "^17.0.1", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1", "webpack-remove-empty-scripts": "^1.0.1"}, "engines": {"node": ">=12.15.0"}, "files": ["src", "dist", "types"], "homepage": "https://github.com/ankurk91/vue-toast-notification", "keywords": ["vue", "toast", "growl", "notice", "notification"], "license": "MIT", "main": "dist/index.js", "name": "vue-toast-notification", "peerDependencies": {"vue": "^3.0"}, "repository": {"type": "git", "url": "git+https://github.com/ankurk91/vue-toast-notification.git"}, "scripts": {"build": "cross-env NODE_ENV=production webpack --progress --mode=production", "dev": "cross-env NODE_ENV=development webpack serve --hot --config=webpack.config.dev.js", "docs": "cross-env NODE_ENV=production webpack --config=webpack.config.dev.js --progress --mode=production", "prepublishOnly": "npm run test && npm run build", "start": "npm run dev", "test": "jest", "test:watch": "npm run test --watch --notify"}, "style": "dist/theme-bootstrap.css", "types": "./types/toast.d.ts", "version": "3.1.1"}