var t=require("vue"),o=function(t,o,e){if(!o.hasOwnProperty(e)){var r=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(o,e,r)}},e={props:{template:String,parent:Object,templateProps:{type:Object,default:function(){return{}}}},render:function(){if(this.template){var e=this.parent||this.$parent,r=e.$data;void 0===r&&(r={});var n=e.$props;void 0===n&&(n={});var a=e.$options;void 0===a&&(a={});var p=a.components;void 0===p&&(p={});var i=a.computed;void 0===i&&(i={});var c=a.methods;void 0===c&&(c={});var s=this.$data;void 0===s&&(s={});var d=this.$props;void 0===d&&(d={});var v=this.$options;void 0===v&&(v={});var u=v.methods;void 0===u&&(u={});var h=v.computed;void 0===h&&(h={});var m=v.components;void 0===m&&(m={});var f={$data:{},$props:{},$options:{},components:{},computed:{},methods:{}};Object.keys(r).forEach(function(t){void 0===s[t]&&(f.$data[t]=r[t])}),Object.keys(n).forEach(function(t){void 0===d[t]&&(f.$props[t]=n[t])}),Object.keys(c).forEach(function(t){void 0===u[t]&&(f.methods[t]=c[t])}),Object.keys(i).forEach(function(t){void 0===h[t]&&(f.computed[t]=i[t])}),Object.keys(p).forEach(function(t){void 0===m[t]&&(f.components[t]=p[t])});var $=Object.keys(f.methods||{}),O=Object.keys(f.$data||{}),b=Object.keys(f.$props||{}),j=Object.keys(this.templateProps),y=O.concat(b).concat($).concat(j),k=(E=e,P={},$.forEach(function(t){return o(E,P,t)}),P),l=function(t){var e={};return t.forEach(function(t){t&&Object.getOwnPropertyNames(t).forEach(function(r){return o(t,e,r)})}),e}([f.$data,f.$props,k,this.templateProps]);return t.h({template:this.template||"<div></div>",props:y,computed:f.computed,components:f.components,provide:this.$parent.$.provides?this.$parent.$.provides:{}},Object.assign({},l))}var E,P}};module.exports=e;
//# sourceMappingURL=vue3-runtime-template.js.map
