{"version": 3, "sources": ["webpack://Lightbox/webpack/universalModuleDefinition", "webpack://Lightbox/./node_modules/hammerjs/hammer.js", "webpack://Lightbox/webpack/bootstrap", "webpack://Lightbox/webpack/runtime/define property getters", "webpack://Lightbox/webpack/runtime/hasOwnProperty shorthand", "webpack://Lightbox/webpack/runtime/make namespace object", "webpack://Lightbox/./src/components/LightBox.vue?48e2", "webpack://Lightbox/./src/components/LeftArrowIcon.vue?e4d8", "webpack://Lightbox/./node_modules/vue-loader/lib/runtime/componentNormalizer.js", "webpack://Lightbox/./src/components/LeftArrowIcon.vue", "webpack://Lightbox/./src/components/RightArrowIcon.vue?8787", "webpack://Lightbox/./src/components/RightArrowIcon.vue", "webpack://Lightbox/./src/components/CloseIcon.vue?125c", "webpack://Lightbox/./src/components/CloseIcon.vue", "webpack://Lightbox/./src/components/VideoIcon.vue?9968", "webpack://Lightbox/./src/components/VideoIcon.vue", "webpack://Lightbox/src/components/LightBox.vue", "webpack://Lightbox/./src/components/LightBox.vue"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "window", "document", "exportName", "undefined", "assign", "VENDOR_PREFIXES", "TEST_ELEMENT", "createElement", "round", "Math", "abs", "now", "Date", "setTimeoutContext", "fn", "timeout", "context", "setTimeout", "bindFn", "invokeArrayArg", "arg", "Array", "isArray", "each", "obj", "iterator", "i", "for<PERSON>ach", "length", "call", "hasOwnProperty", "deprecate", "method", "name", "message", "deprecationMessage", "e", "Error", "stack", "replace", "log", "console", "warn", "apply", "this", "arguments", "Object", "target", "TypeError", "output", "index", "source", "<PERSON><PERSON><PERSON>", "extend", "dest", "src", "merge", "keys", "inherit", "child", "base", "properties", "childP", "baseP", "prototype", "create", "constructor", "_super", "boolOrFn", "val", "args", "ifUndefined", "val1", "val2", "addEventListeners", "types", "handler", "splitStr", "type", "addEventListener", "removeEventListeners", "removeEventListener", "hasParent", "node", "parent", "parentNode", "inStr", "str", "find", "indexOf", "trim", "split", "inArray", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toArray", "slice", "uniqueArray", "key", "sort", "results", "values", "push", "a", "b", "prefixed", "property", "prefix", "prop", "camelProp", "toUpperCase", "_uniqueId", "getWindowForElement", "element", "doc", "ownerDocument", "defaultView", "parentWindow", "SUPPORT_TOUCH", "SUPPORT_POINTER_EVENTS", "SUPPORT_ONLY_TOUCH", "test", "navigator", "userAgent", "INPUT_TYPE_TOUCH", "INPUT_TYPE_MOUSE", "DIRECTION_VERTICAL", "DIRECTION_UP", "PROPS_XY", "PROPS_CLIENT_XY", "Input", "manager", "callback", "options", "inputTarget", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ev", "enable", "init", "inputHandler", "eventType", "input", "pointersLen", "pointers", "changedPointersLen", "changedPointers", "<PERSON><PERSON><PERSON><PERSON>", "isFinal", "session", "pointers<PERSON><PERSON><PERSON>", "firstInput", "simpleCloneInputData", "firstMultiple", "offsetCenter", "center", "getCenter", "timeStamp", "deltaTime", "angle", "getAngle", "distance", "getDistance", "offset", "offsetDelta", "prevDel<PERSON>", "prevInput", "x", "deltaX", "y", "deltaY", "computeDeltaXY", "offsetDirection", "getDirection", "overallVelocity", "getVelocity", "overallVelocityX", "overallVelocityY", "scale", "start", "end", "rotation", "getRotation", "maxPointers", "velocity", "velocityX", "velocityY", "direction", "last", "lastInterval", "v", "computeIntervalInputData", "srcEvent", "computeInputData", "emit", "recognize", "clientX", "clientY", "p1", "p2", "props", "sqrt", "atan2", "PI", "evEl", "ev<PERSON><PERSON><PERSON>", "evWin", "destroy", "MOUSE_INPUT_MAP", "mousedown", "mousemove", "mouseup", "MOUSE_ELEMENT_EVENTS", "MOUSE_WINDOW_EVENTS", "MouseInput", "pressed", "button", "which", "pointerType", "POINTER_INPUT_MAP", "pointerdown", "pointermove", "pointerup", "pointercancel", "pointerout", "IE10_POINTER_TYPE_ENUM", "2", "3", "4", "5", "POINTER_ELEMENT_EVENTS", "POINTER_WINDOW_EVENTS", "PointerEventInput", "store", "pointerEvents", "MSPointerEvent", "PointerEvent", "removePointer", "eventTypeNormalized", "toLowerCase", "is<PERSON><PERSON>ch", "storeIndex", "pointerId", "splice", "SINGLE_TOUCH_INPUT_MAP", "touchstart", "touchmove", "touchend", "touchcancel", "SINGLE_TOUCH_TARGET_EVENTS", "SINGLE_TOUCH_WINDOW_EVENTS", "SingleTouchInput", "started", "normalizeSingleTouches", "all", "touches", "changed", "changedTouches", "concat", "TOUCH_INPUT_MAP", "TOUCH_TARGET_EVENTS", "TouchInput", "targetIds", "getTouches", "allTouches", "identifier", "targetTouches", "changedTargetTouches", "filter", "touch", "TouchMouseInput", "mouse", "primaryTouch", "lastTouches", "recordTouches", "eventData", "setLastTouch", "lastTouch", "lts", "isSyntheticEvent", "t", "dx", "dy", "inputEvent", "inputData", "isMouse", "sourceCapabilities", "firesTouchEvents", "PREFIXED_TOUCH_ACTION", "style", "NATIVE_TOUCH_ACTION", "TOUCH_ACTION_COMPUTE", "TOUCH_ACTION_AUTO", "TOUCH_ACTION_MANIPULATION", "TOUCH_ACTION_NONE", "TOUCH_ACTION_PAN_X", "TOUCH_ACTION_PAN_Y", "TOUCH_ACTION_MAP", "touchMap", "cssSupports", "CSS", "supports", "getTouchActionProps", "TouchAction", "value", "set", "compute", "actions", "update", "touchAction", "recognizers", "recognizer", "getTouchAction", "hasPanX", "hasPanY", "cleanTouchActions", "join", "preventDefaults", "prevented", "preventDefault", "hasNone", "isTapPointer", "isTapMovement", "isTapTouchTime", "DIRECTION_LEFT", "preventSrc", "STATE_FAILED", "Recognizer", "defaults", "id", "state", "simultaneous", "requireFail", "stateStr", "directionStr", "getRecognizerByNameIfManager", "otherRecognizer", "get", "AttrRecognizer", "PanRecognizer", "pX", "pY", "PinchRecognizer", "PressRecognizer", "_timer", "_input", "RotateRecognizer", "SwipeRecognizer", "TapRecognizer", "pTime", "pCenter", "count", "Hammer", "preset", "Manager", "recognizeWith", "dropRecognizeWith", "requireFailure", "dropRequireFailure", "hasRequireFailures", "canRecognizeWith", "event", "additionalEvent", "tryEmit", "canEmit", "inputDataClone", "reset", "process", "attrTest", "optionPointers", "isRecognized", "<PERSON><PERSON><PERSON><PERSON>", "threshold", "DIRECTION_HORIZONTAL", "directionTest", "hasMoved", "inOut", "time", "validPointers", "validMovement", "validTime", "clearTimeout", "taps", "interval", "pos<PERSON><PERSON><PERSON><PERSON>", "validTouchTime", "failTimeout", "validInterval", "validMultiTap", "tapCount", "VERSION", "domEvents", "inputClass", "cssProps", "userSelect", "touchSelect", "touchCallout", "contentZooming", "userDrag", "tapHighlightColor", "handlers", "oldCssProps", "toggleCssProps", "item", "add", "stop", "force", "stopped", "cur<PERSON><PERSON><PERSON><PERSON><PERSON>", "existing", "remove", "on", "events", "off", "data", "gestureEvent", "createEvent", "initEvent", "gesture", "dispatchEvent", "triggerDomEvent", "INPUT_START", "INPUT_MOVE", "INPUT_END", "INPUT_CANCEL", "STATE_POSSIBLE", "STATE_BEGAN", "STATE_CHANGED", "STATE_ENDED", "STATE_RECOGNIZED", "STATE_CANCELLED", "DIRECTION_NONE", "DIRECTION_RIGHT", "DIRECTION_DOWN", "DIRECTION_ALL", "Tap", "Pan", "Swipe", "Pinch", "Rotate", "Press", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "d", "definition", "o", "defineProperty", "enumerable", "r", "Symbol", "toStringTag", "render", "_vm", "_h", "$createElement", "_c", "_self", "click", "$event", "stopPropagation", "closeLightBox", "attrs", "mode", "afterEnter", "enableImageTransition", "beforeLeave", "disableImageTransition", "media", "directives", "rawName", "lightBoxShown", "expression", "ref", "staticClass", "imageTransitionName", "currentMedia", "srcset", "alt", "caption", "select", "width", "height", "frameborder", "allowfullscreen", "sources", "controls", "autoplay", "_l", "_e", "_v", "showThumbs", "class", "controlsHidden", "mouseover", "interfaceHovered", "mouseleave", "imagesThumb", "image", "thumbIndex", "begin", "thumb", "backgroundImage", "showImage", "_t", "showCaption", "domProps", "innerHTML", "_s", "current", "total", "closable", "title", "closeText", "previousText", "previousImage", "nextText", "nextImage", "_withStripped", "fill", "viewBox", "normalizeComponent", "scriptExports", "staticRenderFns", "functionalTemplate", "injectStyles", "scopeId", "moduleIdentifier", "shadowMode", "hook", "_compiled", "functional", "_scopeId", "$vnode", "ssrContext", "__VUE_SSR_CONTEXT__", "_registeredComponents", "_ssrRegister", "$root", "$options", "shadowRoot", "_injectStyles", "originalRender", "h", "beforeCreate", "component", "__file", "staticStyle", "margin", "components", "LeftArrowIcon", "RightArrowIcon", "CloseIcon", "VideoIcon", "required", "disableScroll", "Boolean", "default", "showLightBox", "startAt", "Number", "nThumbs", "autoPlay", "autoPlayTime", "interfaceHideTime", "lengthToLoadMore", "String", "timer", "interactionTimer", "computed", "halfDown", "map", "watch", "onToggleLightBox", "$emit", "mounted", "setInterval", "$refs", "container", "hammer", "handleMouseActivity", "<PERSON><PERSON><PERSON><PERSON>", "addKeyEvent", "clearInterval", "methods", "onLightBoxOpen", "querySelector", "classList", "video", "play", "onLightBoxClose", "pause", "currentTime", "keyCode", "$set", "stopInteractionTimer", "startInteractionTimer"], "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAkB,SAAID,IAEtBD,EAAe,SAAIC,IARrB,CASGK,MAAM,WACT,M,2BCVA,OAKA,SAAUC,EAAQC,EAAUC,EAAYC,GACtC,aAEF,IA+FIC,EA/FAC,EAAkB,CAAC,GAAI,SAAU,MAAO,KAAM,KAAM,KACpDC,EAAeL,EAASM,cAAc,OAItCC,EAAQC,KAAKD,MACbE,EAAMD,KAAKC,IACXC,EAAMC,KAAKD,IASf,SAASE,EAAkBC,EAAIC,EAASC,GACpC,OAAOC,WAAWC,EAAOJ,EAAIE,GAAUD,GAY3C,SAASI,EAAeC,EAAKN,EAAIE,GAC7B,QAAIK,MAAMC,QAAQF,KACdG,EAAKH,EAAKJ,EAAQF,GAAKE,IAChB,GAWf,SAASO,EAAKC,EAAKC,EAAUT,GACzB,IAAIU,EAEJ,GAAKF,EAIL,GAAIA,EAAIG,QACJH,EAAIG,QAAQF,EAAUT,QACnB,GAAIQ,EAAII,SAAWzB,EAEtB,IADAuB,EAAI,EACGA,EAAIF,EAAII,QACXH,EAASI,KAAKb,EAASQ,EAAIE,GAAIA,EAAGF,GAClCE,SAGJ,IAAKA,KAAKF,EACNA,EAAIM,eAAeJ,IAAMD,EAASI,KAAKb,EAASQ,EAAIE,GAAIA,EAAGF,GAYvE,SAASO,EAAUC,EAAQC,EAAMC,GAC7B,IAAIC,EAAqB,sBAAwBF,EAAO,KAAOC,EAAU,SACzE,OAAO,WACH,IAAIE,EAAI,IAAIC,MAAM,mBACdC,EAAQF,GAAKA,EAAEE,MAAQF,EAAEE,MAAMC,QAAQ,kBAAmB,IACzDA,QAAQ,cAAe,IACvBA,QAAQ,6BAA8B,kBAAoB,sBAE3DC,EAAMxC,EAAOyC,UAAYzC,EAAOyC,QAAQC,MAAQ1C,EAAOyC,QAAQD,KAInE,OAHIA,GACAA,EAAIX,KAAK7B,EAAOyC,QAASN,EAAoBG,GAE1CN,EAAOW,MAAMC,KAAMC,YAa9BzC,EADyB,mBAAlB0C,OAAO1C,OACL,SAAgB2C,GACrB,GAAIA,IAAW5C,GAAwB,OAAX4C,EACxB,MAAM,IAAIC,UAAU,8CAIxB,IADA,IAAIC,EAASH,OAAOC,GACXG,EAAQ,EAAGA,EAAQL,UAAUjB,OAAQsB,IAAS,CACnD,IAAIC,EAASN,UAAUK,GACvB,GAAIC,IAAWhD,GAAwB,OAAXgD,EACxB,IAAK,IAAIC,KAAWD,EACZA,EAAOrB,eAAesB,KACtBH,EAAOG,GAAWD,EAAOC,IAKzC,OAAOH,GAGFH,OAAO1C,OAWpB,IAAIiD,EAAStB,GAAU,SAAgBuB,EAAMC,EAAKC,GAG9C,IAFA,IAAIC,EAAOX,OAAOW,KAAKF,GACnB7B,EAAI,EACDA,EAAI+B,EAAK7B,UACP4B,GAAUA,GAASF,EAAKG,EAAK/B,MAAQvB,KACtCmD,EAAKG,EAAK/B,IAAM6B,EAAIE,EAAK/B,KAE7BA,IAEJ,OAAO4B,IACR,SAAU,iBASTE,EAAQzB,GAAU,SAAeuB,EAAMC,GACvC,OAAOF,EAAOC,EAAMC,GAAK,KAC1B,QAAS,iBAQZ,SAASG,EAAQC,EAAOC,EAAMC,GAC1B,IACIC,EADAC,EAAQH,EAAKI,WAGjBF,EAASH,EAAMK,UAAYlB,OAAOmB,OAAOF,IAClCG,YAAcP,EACrBG,EAAOK,OAASJ,EAEZF,GACAzD,EAAO0D,EAAQD,GAUvB,SAAS3C,EAAOJ,EAAIE,GAChB,OAAO,WACH,OAAOF,EAAG6B,MAAM3B,EAAS6B,YAWjC,SAASuB,EAASC,EAAKC,GACnB,MA1LgB,mBA0LLD,EACAA,EAAI1B,MAAM2B,GAAOA,EAAK,IAAkBnE,EAAWmE,GAEvDD,EASX,SAASE,EAAYC,EAAMC,GACvB,OAAQD,IAASrE,EAAasE,EAAOD,EASzC,SAASE,EAAkB3B,EAAQ4B,EAAOC,GACtCrD,EAAKsD,EAASF,IAAQ,SAASG,GAC3B/B,EAAOgC,iBAAiBD,EAAMF,GAAS,MAU/C,SAASI,EAAqBjC,EAAQ4B,EAAOC,GACzCrD,EAAKsD,EAASF,IAAQ,SAASG,GAC3B/B,EAAOkC,oBAAoBH,EAAMF,GAAS,MAWlD,SAASM,EAAUC,EAAMC,GACrB,KAAOD,GAAM,CACT,GAAIA,GAAQC,EACR,OAAO,EAEXD,EAAOA,EAAKE,WAEhB,OAAO,EASX,SAASC,EAAMC,EAAKC,GAChB,OAAOD,EAAIE,QAAQD,IAAS,EAQhC,SAASX,EAASU,GACd,OAAOA,EAAIG,OAAOC,MAAM,QAU5B,SAASC,EAAQrC,EAAKiC,EAAMK,GACxB,GAAItC,EAAIkC,UAAYI,EAChB,OAAOtC,EAAIkC,QAAQD,GAGnB,IADA,IAAI9D,EAAI,EACDA,EAAI6B,EAAI3B,QAAQ,CACnB,GAAKiE,GAAatC,EAAI7B,GAAGmE,IAAcL,IAAWK,GAAatC,EAAI7B,KAAO8D,EACtE,OAAO9D,EAEXA,IAEJ,OAAQ,EAShB,SAASoE,EAAQtE,GACb,OAAOH,MAAM2C,UAAU+B,MAAMlE,KAAKL,EAAK,GAU3C,SAASwE,EAAYzC,EAAK0C,EAAKC,GAK3B,IAJA,IAAIC,EAAU,GACVC,EAAS,GACT1E,EAAI,EAEDA,EAAI6B,EAAI3B,QAAQ,CACnB,IAAIyC,EAAM4B,EAAM1C,EAAI7B,GAAGuE,GAAO1C,EAAI7B,GAC9BkE,EAAQQ,EAAQ/B,GAAO,GACvB8B,EAAQE,KAAK9C,EAAI7B,IAErB0E,EAAO1E,GAAK2C,EACZ3C,IAaJ,OAVIwE,IAIIC,EAHCF,EAGSE,EAAQD,MAAK,SAAyBI,EAAGC,GAC/C,OAAOD,EAAEL,GAAOM,EAAEN,MAHZE,EAAQD,QAQnBC,EASX,SAASK,EAAShF,EAAKiF,GAKnB,IAJA,IAAIC,EAAQC,EACRC,EAAYH,EAAS,GAAGI,cAAgBJ,EAASV,MAAM,GAEvDrE,EAAI,EACDA,EAAIrB,EAAgBuB,QAAQ,CAI/B,IAFA+E,GADAD,EAASrG,EAAgBqB,IACPgF,EAASE,EAAYH,KAE3BjF,EACR,OAAOmF,EAEXjF,IAEJ,OAAOvB,EAOX,IAAI2G,EAAY,EAUhB,SAASC,EAAoBC,GACzB,IAAIC,EAAMD,EAAQE,eAAiBF,EACnC,OAAQC,EAAIE,aAAeF,EAAIG,cAAgBpH,EAGnD,IAEIqH,EAAiB,iBAAkBrH,EACnCsH,EAAyBd,EAASxG,EAAQ,kBAAoBG,EAC9DoH,EAAqBF,GAJN,wCAIoCG,KAAKC,UAAUC,WAElEC,EAAmB,QAEnBC,EAAmB,QAiBnBC,EAAqBC,GAGrBC,EAAW,CAAC,IAAK,KACjBC,EAAkB,CAAC,UAAW,WASlC,SAASC,EAAMC,EAASC,GACpB,IAAIpI,EAAO6C,KACXA,KAAKsF,QAAUA,EACftF,KAAKuF,SAAWA,EAChBvF,KAAKoE,QAAUkB,EAAQlB,QACvBpE,KAAKG,OAASmF,EAAQE,QAAQC,YAI9BzF,KAAK0F,WAAa,SAASC,GACnBnE,EAAS8D,EAAQE,QAAQI,OAAQ,CAACN,KAClCnI,EAAK6E,QAAQ2D,IAIrB3F,KAAK6F,OA4DT,SAASC,EAAaR,EAASS,EAAWC,GACtC,IAAIC,EAAcD,EAAME,SAASlH,OAC7BmH,EAAqBH,EAAMI,gBAAgBpH,OAC3CqH,EAvGU,EAuGCN,GAA4BE,EAAcE,GAAuB,EAC5EG,EAAuB,GAAZP,GAA2CE,EAAcE,GAAuB,EAE/FH,EAAMK,UAAYA,EAClBL,EAAMM,UAAYA,EAEdD,IACAf,EAAQiB,QAAU,IAKtBP,EAAMD,UAAYA,EAiBtB,SAA0BT,EAASU,GAC/B,IAAIO,EAAUjB,EAAQiB,QAClBL,EAAWF,EAAME,SACjBM,EAAiBN,EAASlH,OAGzBuH,EAAQE,aACTF,EAAQE,WAAaC,EAAqBV,IAI1CQ,EAAiB,IAAMD,EAAQI,cAC/BJ,EAAQI,cAAgBD,EAAqBV,GACnB,IAAnBQ,IACPD,EAAQI,eAAgB,GAG5B,IAAIF,EAAaF,EAAQE,WACrBE,EAAgBJ,EAAQI,cACxBC,EAAeD,EAAgBA,EAAcE,OAASJ,EAAWI,OAEjEA,EAASb,EAAMa,OAASC,EAAUZ,GACtCF,EAAMe,UAAYhJ,IAClBiI,EAAMgB,UAAYhB,EAAMe,UAAYN,EAAWM,UAE/Cf,EAAMiB,MAAQC,EAASN,EAAcC,GACrCb,EAAMmB,SAAWC,EAAYR,EAAcC,GA0B/C,SAAwBN,EAASP,GAC7B,IAAIa,EAASb,EAAMa,OACfQ,EAASd,EAAQe,aAAe,GAChCC,EAAYhB,EAAQgB,WAAa,GACjCC,EAAYjB,EAAQiB,WAAa,GA5LvB,IA8LVxB,EAAMD,WA5LE,IA4L2ByB,EAAUzB,YAC7CwB,EAAYhB,EAAQgB,UAAY,CAC5BE,EAAGD,EAAUE,QAAU,EACvBC,EAAGH,EAAUI,QAAU,GAG3BP,EAASd,EAAQe,YAAc,CAC3BG,EAAGZ,EAAOY,EACVE,EAAGd,EAAOc,IAIlB3B,EAAM0B,OAASH,EAAUE,GAAKZ,EAAOY,EAAIJ,EAAOI,GAChDzB,EAAM4B,OAASL,EAAUI,GAAKd,EAAOc,EAAIN,EAAOM,GA3ChDE,CAAetB,EAASP,GACxBA,EAAM8B,gBAAkBC,EAAa/B,EAAM0B,OAAQ1B,EAAM4B,QAEzD,IAAII,EAAkBC,EAAYjC,EAAMgB,UAAWhB,EAAM0B,OAAQ1B,EAAM4B,QACvE5B,EAAMkC,iBAAmBF,EAAgBP,EACzCzB,EAAMmC,iBAAmBH,EAAgBL,EACzC3B,EAAMgC,gBAAmBlK,EAAIkK,EAAgBP,GAAK3J,EAAIkK,EAAgBL,GAAMK,EAAgBP,EAAIO,EAAgBL,EAEhH3B,EAAMoC,MAAQzB,GAkNA0B,EAlNyB1B,EAAcT,SAkNhCoC,EAlN0CpC,EAmNxDkB,EAAYkB,EAAI,GAAIA,EAAI,GAAIlD,GAAmBgC,EAAYiB,EAAM,GAAIA,EAAM,GAAIjD,IAnNX,EAC3EY,EAAMuC,SAAW5B,EAsMrB,SAAqB0B,EAAOC,GACxB,OAAOpB,EAASoB,EAAI,GAAIA,EAAI,GAAIlD,GAAmB8B,EAASmB,EAAM,GAAIA,EAAM,GAAIjD,GAvM/CoD,CAAY7B,EAAcT,SAAUA,GAAY,EAEjFF,EAAMyC,YAAelC,EAAQiB,UAAsCxB,EAAME,SAASlH,OAC9EuH,EAAQiB,UAAUiB,YAAezC,EAAME,SAASlH,OAASuH,EAAQiB,UAAUiB,YADtCzC,EAAME,SAASlH,OAwC5D,SAAkCuH,EAASP,GACvC,IAEI0C,EAAUC,EAAWC,EAAWC,EAFhCC,EAAOvC,EAAQwC,cAAgB/C,EAC/BgB,EAAYhB,EAAMe,UAAY+B,EAAK/B,UAGvC,GArNe,GAqNXf,EAAMD,YAA8BiB,EA1NrB,IA0NqD8B,EAAKJ,WAAanL,GAAY,CAClG,IAAImK,EAAS1B,EAAM0B,OAASoB,EAAKpB,OAC7BE,EAAS5B,EAAM4B,OAASkB,EAAKlB,OAE7BoB,EAAIf,EAAYjB,EAAWU,EAAQE,GACvCe,EAAYK,EAAEvB,EACdmB,EAAYI,EAAErB,EACde,EAAY5K,EAAIkL,EAAEvB,GAAK3J,EAAIkL,EAAErB,GAAMqB,EAAEvB,EAAIuB,EAAErB,EAC3CkB,EAAYd,EAAaL,EAAQE,GAEjCrB,EAAQwC,aAAe/C,OAGvB0C,EAAWI,EAAKJ,SAChBC,EAAYG,EAAKH,UACjBC,EAAYE,EAAKF,UACjBC,EAAYC,EAAKD,UAGrB7C,EAAM0C,SAAWA,EACjB1C,EAAM2C,UAAYA,EAClB3C,EAAM4C,UAAYA,EAClB5C,EAAM6C,UAAYA,EAhElBI,CAAyB1C,EAASP,GA4MtC,IAAkBqC,EAAOC,EAzMrB,IAAInI,EAASmF,EAAQlB,QACjB9B,EAAU0D,EAAMkD,SAAS/I,OAAQA,KACjCA,EAAS6F,EAAMkD,SAAS/I,QAE5B6F,EAAM7F,OAASA,EA/DfgJ,CAAiB7D,EAASU,GAG1BV,EAAQ8D,KAAK,eAAgBpD,GAE7BV,EAAQ+D,UAAUrD,GAClBV,EAAQiB,QAAQiB,UAAYxB,EA0HhC,SAASU,EAAqBV,GAK1B,IAFA,IAAIE,EAAW,GACXpH,EAAI,EACDA,EAAIkH,EAAME,SAASlH,QACtBkH,EAASpH,GAAK,CACVwK,QAAS1L,EAAMoI,EAAME,SAASpH,GAAGwK,SACjCC,QAAS3L,EAAMoI,EAAME,SAASpH,GAAGyK,UAErCzK,IAGJ,MAAO,CACHiI,UAAWhJ,IACXmI,SAAUA,EACVW,OAAQC,EAAUZ,GAClBwB,OAAQ1B,EAAM0B,OACdE,OAAQ5B,EAAM4B,QAStB,SAASd,EAAUZ,GACf,IAAIM,EAAiBN,EAASlH,OAG9B,GAAuB,IAAnBwH,EACA,MAAO,CACHiB,EAAG7J,EAAMsI,EAAS,GAAGoD,SACrB3B,EAAG/J,EAAMsI,EAAS,GAAGqD,UAK7B,IADA,IAAI9B,EAAI,EAAGE,EAAI,EAAG7I,EAAI,EACfA,EAAI0H,GACPiB,GAAKvB,EAASpH,GAAGwK,QACjB3B,GAAKzB,EAASpH,GAAGyK,QACjBzK,IAGJ,MAAO,CACH2I,EAAG7J,EAAM6J,EAAIjB,GACbmB,EAAG/J,EAAM+J,EAAInB,IAWrB,SAASyB,EAAYjB,EAAWS,EAAGE,GAC/B,MAAO,CACHF,EAAGA,EAAIT,GAAa,EACpBW,EAAGA,EAAIX,GAAa,GAU5B,SAASe,EAAaN,EAAGE,GACrB,OAAIF,IAAME,EAzTO,EA6Tb7J,EAAI2J,IAAM3J,EAAI6J,GACPF,EAAI,EA7TE,EACC,EA8TXE,EAAI,EA7TI,EACE,GAsUrB,SAASP,EAAYoC,EAAIC,EAAIC,GACpBA,IACDA,EAAQvE,GAEZ,IAAIsC,EAAIgC,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAC5B/B,EAAI8B,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAEhC,OAAO7L,KAAK8L,KAAMlC,EAAIA,EAAME,EAAIA,GAUpC,SAAST,EAASsC,EAAIC,EAAIC,GACjBA,IACDA,EAAQvE,GAEZ,IAAIsC,EAAIgC,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAC5B/B,EAAI8B,EAAGC,EAAM,IAAMF,EAAGE,EAAM,IAChC,OAA0B,IAAnB7L,KAAK+L,MAAMjC,EAAGF,GAAW5J,KAAKgM,GA1TzCxE,EAAMjE,UAAY,CAKdY,QAAS,aAKT6D,KAAM,WACF7F,KAAK8J,MAAQhI,EAAkB9B,KAAKoE,QAASpE,KAAK8J,KAAM9J,KAAK0F,YAC7D1F,KAAK+J,UAAYjI,EAAkB9B,KAAKG,OAAQH,KAAK+J,SAAU/J,KAAK0F,YACpE1F,KAAKgK,OAASlI,EAAkBqC,EAAoBnE,KAAKoE,SAAUpE,KAAKgK,MAAOhK,KAAK0F,aAMxFuE,QAAS,WACLjK,KAAK8J,MAAQ1H,EAAqBpC,KAAKoE,QAASpE,KAAK8J,KAAM9J,KAAK0F,YAChE1F,KAAK+J,UAAY3H,EAAqBpC,KAAKG,OAAQH,KAAK+J,SAAU/J,KAAK0F,YACvE1F,KAAKgK,OAAS5H,EAAqB+B,EAAoBnE,KAAKoE,SAAUpE,KAAKgK,MAAOhK,KAAK0F,cA4T/F,IAAIwE,EAAkB,CAClBC,UA/Xc,EAgYdC,UA/Xa,EAgYbC,QA/XY,GAkYZC,EAAuB,YACvBC,EAAsB,oBAO1B,SAASC,KACLxK,KAAK8J,KAAOQ,EACZtK,KAAKgK,MAAQO,EAEbvK,KAAKyK,SAAU,EAEfpF,EAAMtF,MAAMC,KAAMC,WAGtBa,EAAQ0J,GAAYnF,EAAO,CAKvBrD,QAAS,SAAmB2D,GACxB,IAAII,EAAYmE,EAAgBvE,EAAGzD,MA3ZzB,EA8ZN6D,GAAyC,IAAdJ,EAAG+E,SAC9B1K,KAAKyK,SAAU,GA9ZV,EAiaL1E,GAAuC,IAAbJ,EAAGgF,QAC7B5E,EAjaI,GAqaH/F,KAAKyK,UAraF,EAyaJ1E,IACA/F,KAAKyK,SAAU,GAGnBzK,KAAKuF,SAASvF,KAAKsF,QAASS,EAAW,CACnCG,SAAU,CAACP,GACXS,gBAAiB,CAACT,GAClBiF,YAAa5F,EACbkE,SAAUvD,QAKtB,IAAIkF,GAAoB,CACpBC,YAzbc,EA0bdC,YAzba,EA0bbC,UAzbY,EA0bZC,cAzbe,EA0bfC,WA1be,GA8bfC,GAAyB,CACzBC,EAAGrG,EACHsG,EAzciB,MA0cjBC,EAAGtG,EACHuG,EAzcoB,UA4cpBC,GAAyB,cACzBC,GAAwB,sCAa5B,SAASC,KACL1L,KAAK8J,KAAO0B,GACZxL,KAAKgK,MAAQyB,GAEbpG,EAAMtF,MAAMC,KAAMC,WAElBD,KAAK2L,MAAS3L,KAAKsF,QAAQiB,QAAQqF,cAAgB,GAhBnDxO,EAAOyO,iBAAmBzO,EAAO0O,eACjCN,GAAyB,gBACzBC,GAAwB,6CAiB5B3K,EAAQ4K,GAAmBrG,EAAO,CAK9BrD,QAAS,SAAmB2D,GACxB,IAAIgG,EAAQ3L,KAAK2L,MACbI,GAAgB,EAEhBC,EAAsBrG,EAAGzD,KAAK+J,cAActM,QAAQ,KAAM,IAC1DoG,EAAY8E,GAAkBmB,GAC9BpB,EAAcO,GAAuBxF,EAAGiF,cAAgBjF,EAAGiF,YAE3DsB,EAAWtB,GAAe7F,EAG1BoH,EAAanJ,EAAQ2I,EAAOhG,EAAGyG,UAAW,aA/epC,EAkfNrG,IAA0C,IAAdJ,EAAG+E,QAAgBwB,GAC3CC,EAAa,IACbR,EAAMlI,KAAKkC,GACXwG,EAAaR,EAAM3M,OAAS,GAEb,GAAZ+G,IACPgG,GAAgB,GAIhBI,EAAa,IAKjBR,EAAMQ,GAAcxG,EAEpB3F,KAAKuF,SAASvF,KAAKsF,QAASS,EAAW,CACnCG,SAAUyF,EACVvF,gBAAiB,CAACT,GAClBiF,YAAaA,EACb1B,SAAUvD,IAGVoG,GAEAJ,EAAMU,OAAOF,EAAY,OAKrC,IAAIG,GAAyB,CACzBC,WAlhBc,EAmhBdC,UAlhBa,EAmhBbC,SAlhBY,EAmhBZC,YAlhBe,GAqhBfC,GAA6B,aAC7BC,GAA6B,4CAOjC,SAASC,KACL7M,KAAK+J,SAAW4C,GAChB3M,KAAKgK,MAAQ4C,GACb5M,KAAK8M,SAAU,EAEfzH,EAAMtF,MAAMC,KAAMC,WAsCtB,SAAS8M,GAAuBpH,EAAIzD,GAChC,IAAI8K,EAAM9J,EAAQyC,EAAGsH,SACjBC,EAAUhK,EAAQyC,EAAGwH,gBAMzB,OAJW,GAAPjL,IACA8K,EAAM5J,EAAY4J,EAAII,OAAOF,GAAU,cAAc,IAGlD,CAACF,EAAKE,GA3CjBpM,EAAQ+L,GAAkBxH,EAAO,CAC7BrD,QAAS,SAAmB2D,GACxB,IAAIzD,EAAOoK,GAAuB3G,EAAGzD,MAOrC,GAjjBU,IA6iBNA,IACAlC,KAAK8M,SAAU,GAGd9M,KAAK8M,QAAV,CAIA,IAAIG,EAAUF,GAAuB9N,KAAKe,KAAM2F,EAAIzD,GAGzC,GAAPA,GAAqC+K,EAAQ,GAAGjO,OAASiO,EAAQ,GAAGjO,QAAW,IAC/EgB,KAAK8M,SAAU,GAGnB9M,KAAKuF,SAASvF,KAAKsF,QAASpD,EAAM,CAC9BgE,SAAU+G,EAAQ,GAClB7G,gBAAiB6G,EAAQ,GACzBrC,YAAa7F,EACbmE,SAAUvD,QAsBtB,IAAI0H,GAAkB,CAClBd,WAvlBc,EAwlBdC,UAvlBa,EAwlBbC,SAvlBY,EAwlBZC,YAvlBe,GA0lBfY,GAAsB,4CAO1B,SAASC,KACLvN,KAAK+J,SAAWuD,GAChBtN,KAAKwN,UAAY,GAEjBnI,EAAMtF,MAAMC,KAAMC,WA0BtB,SAASwN,GAAW9H,EAAIzD,GACpB,IAAIwL,EAAaxK,EAAQyC,EAAGsH,SACxBO,EAAYxN,KAAKwN,UAGrB,GAAW,EAAPtL,GAA2D,IAAtBwL,EAAW1O,OAEhD,OADAwO,EAAUE,EAAW,GAAGC,aAAc,EAC/B,CAACD,EAAYA,GAGxB,IAAI5O,EACA8O,EACAT,EAAiBjK,EAAQyC,EAAGwH,gBAC5BU,EAAuB,GACvB1N,EAASH,KAAKG,OAQlB,GALAyN,EAAgBF,EAAWI,QAAO,SAASC,GACvC,OAAOzL,EAAUyL,EAAM5N,OAAQA,MAppBrB,IAwpBV+B,EAEA,IADApD,EAAI,EACGA,EAAI8O,EAAc5O,QACrBwO,EAAUI,EAAc9O,GAAG6O,aAAc,EACzC7O,IAMR,IADAA,EAAI,EACGA,EAAIqO,EAAenO,QAClBwO,EAAUL,EAAerO,GAAG6O,aAC5BE,EAAqBpK,KAAK0J,EAAerO,IAIlC,GAAPoD,UACOsL,EAAUL,EAAerO,GAAG6O,YAEvC7O,IAGJ,OAAK+O,EAAqB7O,OAInB,CAEHoE,EAAYwK,EAAcR,OAAOS,GAAuB,cAAc,GACtEA,QAPJ,EAnEJ/M,EAAQyM,GAAYlI,EAAO,CACvBrD,QAAS,SAAoB2D,GACzB,IAAIzD,EAAOmL,GAAgB1H,EAAGzD,MAC1B+K,EAAUQ,GAAWxO,KAAKe,KAAM2F,EAAIzD,GACnC+K,GAILjN,KAAKuF,SAASvF,KAAKsF,QAASpD,EAAM,CAC9BgE,SAAU+G,EAAQ,GAClB7G,gBAAiB6G,EAAQ,GACzBrC,YAAa7F,EACbmE,SAAUvD,OA+EtB,SAASqI,KACL3I,EAAMtF,MAAMC,KAAMC,WAElB,IAAI+B,EAAU1D,EAAO0B,KAAKgC,QAAShC,MACnCA,KAAK+N,MAAQ,IAAIR,GAAWvN,KAAKsF,QAAStD,GAC1ChC,KAAKiO,MAAQ,IAAIzD,GAAWxK,KAAKsF,QAAStD,GAE1ChC,KAAKkO,aAAe,KACpBlO,KAAKmO,YAAc,GAqCvB,SAASC,GAAcrI,EAAWsI,GAnvBhB,EAovBVtI,GACA/F,KAAKkO,aAAeG,EAAUjI,gBAAgB,GAAGuH,WACjDW,GAAarP,KAAKe,KAAMqO,IACL,GAAZtI,GACPuI,GAAarP,KAAKe,KAAMqO,GAIhC,SAASC,GAAaD,GAClB,IAAIN,EAAQM,EAAUjI,gBAAgB,GAEtC,GAAI2H,EAAMJ,aAAe3N,KAAKkO,aAAc,CACxC,IAAIK,EAAY,CAAC9G,EAAGsG,EAAMzE,QAAS3B,EAAGoG,EAAMxE,SAC5CvJ,KAAKmO,YAAY1K,KAAK8K,GACtB,IAAIC,EAAMxO,KAAKmO,YAOf9P,YANsB,WAClB,IAAIS,EAAI0P,EAAI3L,QAAQ0L,GAChBzP,GAAK,GACL0P,EAAInC,OAAOvN,EAAG,KAnEV,OA0EpB,SAAS2P,GAAiBJ,GAEtB,IADA,IAAI5G,EAAI4G,EAAUnF,SAASI,QAAS3B,EAAI0G,EAAUnF,SAASK,QAClDzK,EAAI,EAAGA,EAAIkB,KAAKmO,YAAYnP,OAAQF,IAAK,CAC9C,IAAI4P,EAAI1O,KAAKmO,YAAYrP,GACrB6P,EAAK9Q,KAAKC,IAAI2J,EAAIiH,EAAEjH,GAAImH,EAAK/Q,KAAKC,IAAI6J,EAAI+G,EAAE/G,GAChD,GAAIgH,GA9ES,IA8EeC,GA9Ef,GA+ET,OAAO,EAGf,OAAO,EArEX9N,EAAQkN,GAAiB3I,EAAO,CAO5BrD,QAAS,SAAoBsD,EAASuJ,EAAYC,GAC9C,IAAI5C,EAAW4C,EAAUlE,aAAe7F,EACpCgK,EAAWD,EAAUlE,aAAe5F,EAExC,KAAI+J,GAAWD,EAAUE,oBAAsBF,EAAUE,mBAAmBC,kBAA5E,CAKA,GAAI/C,EACAkC,GAAcnP,KAAKe,KAAM6O,EAAYC,QAClC,GAAIC,GAAWN,GAAiBxP,KAAKe,KAAM8O,GAC9C,OAGJ9O,KAAKuF,SAASD,EAASuJ,EAAYC,KAMvC7E,QAAS,WACLjK,KAAK+N,MAAM9D,UACXjK,KAAKiO,MAAMhE,aA0CnB,IAAIiF,GAAwBtL,EAASlG,EAAayR,MAAO,eACrDC,GAAsBF,KAA0B3R,EAGhD8R,GAAuB,UACvBC,GAAoB,OACpBC,GAA4B,eAC5BC,GAAoB,OACpBC,GAAqB,QACrBC,GAAqB,QACrBC,GA4IJ,WACI,IAAKP,GACD,OAAO,EAEX,IAAIQ,EAAW,GACXC,EAAczS,EAAO0S,KAAO1S,EAAO0S,IAAIC,SAO3C,MANA,CAAC,OAAQ,eAAgB,QAAS,QAAS,cAAe,QAAQhR,SAAQ,SAAS0C,GAI/EmO,EAASnO,IAAOoO,GAAczS,EAAO0S,IAAIC,SAAS,eAAgBtO,MAE/DmO,EAxJYI,GASvB,SAASC,GAAY3K,EAAS4K,GAC1BlQ,KAAKsF,QAAUA,EACftF,KAAKmQ,IAAID,GAGbD,GAAY7O,UAAY,CAKpB+O,IAAK,SAASD,GAENA,GAASb,KACTa,EAAQlQ,KAAKoQ,WAGbhB,IAAuBpP,KAAKsF,QAAQlB,QAAQ+K,OAASQ,GAAiBO,KACtElQ,KAAKsF,QAAQlB,QAAQ+K,MAAMD,IAAyBgB,GAExDlQ,KAAKqQ,QAAUH,EAAMjE,cAAcnJ,QAMvCwN,OAAQ,WACJtQ,KAAKmQ,IAAInQ,KAAKsF,QAAQE,QAAQ+K,cAOlCH,QAAS,WACL,IAAIC,EAAU,GAMd,OALA1R,EAAKqB,KAAKsF,QAAQkL,aAAa,SAASC,GAChCjP,EAASiP,EAAWjL,QAAQI,OAAQ,CAAC6K,MACrCJ,EAAUA,EAAQjD,OAAOqD,EAAWC,sBAgEpD,SAA2BL,GAEvB,GAAI3N,EAAM2N,EAASb,IACf,OAAOA,GAGX,IAAImB,EAAUjO,EAAM2N,EAASZ,IACzBmB,EAAUlO,EAAM2N,EAASX,IAM7B,GAAIiB,GAAWC,EACX,OAAOpB,GAIX,GAAImB,GAAWC,EACX,OAAOD,EAAUlB,GAAqBC,GAI1C,GAAIhN,EAAM2N,EAASd,IACf,OAAOA,GAGX,OAAOD,GAxFIuB,CAAkBR,EAAQS,KAAK,OAO1CC,gBAAiB,SAAS/K,GACtB,IAAIkD,EAAWlD,EAAMkD,SACjBL,EAAY7C,EAAM8B,gBAGtB,GAAI9H,KAAKsF,QAAQiB,QAAQyK,UACrB9H,EAAS+H,qBADb,CAKA,IAAIZ,EAAUrQ,KAAKqQ,QACfa,EAAUxO,EAAM2N,EAASb,MAAuBG,GAAkC,KAClFiB,EAAUlO,EAAM2N,EAASX,MAAwBC,GAAiBD,SAClEiB,EAAUjO,EAAM2N,EAASZ,MAAwBE,GAAiBF,SAEtE,GAAIyB,EAAS,CAGT,IAAIC,EAAyC,IAA1BnL,EAAME,SAASlH,OAC9BoS,EAAgBpL,EAAMmB,SAAW,EACjCkK,EAAiBrL,EAAMgB,UAAY,IAEvC,GAAImK,GAAgBC,GAAiBC,EACjC,OAIR,IAAIV,IAAWC,EAKf,OAAIM,GACCN,GAj3BcU,EAi3BHzI,GACX8H,GAAW9H,EAAY5D,EACjBjF,KAAKuR,WAAWrI,QAH3B,IAWJqI,WAAY,SAASrI,GACjBlJ,KAAKsF,QAAQiB,QAAQyK,WAAY,EACjC9H,EAAS+H,mBAiFjB,IAMIO,GAAe,GAQnB,SAASC,GAAWjM,GAChBxF,KAAKwF,QAAUhI,EAAO,GAAIwC,KAAK0R,SAAUlM,GAAW,IAEpDxF,KAAK2R,GApgCEzN,IAsgCPlE,KAAKsF,QAAU,KAGftF,KAAKwF,QAAQI,OAASjE,EAAY3B,KAAKwF,QAAQI,QAAQ,GAEvD5F,KAAK4R,MAxBY,EA0BjB5R,KAAK6R,aAAe,GACpB7R,KAAK8R,YAAc,GAqOvB,SAASC,GAASH,GACd,OA5PkB,GA4PdA,EACO,SA/PG,EAgQHA,EACA,MAlQK,EAmQLA,EACA,OArQG,EAsQHA,EACA,QAEJ,GAQX,SAASI,GAAanJ,GAClB,OAnuCiB,IAmuCbA,EACO,OAruCI,GAsuCJA,EACA,KAzuCM,GA0uCNA,EACA,OA1uCO,GA2uCPA,EACA,QAEJ,GASX,SAASoJ,GAA6BC,EAAiBzB,GACnD,IAAInL,EAAUmL,EAAWnL,QACzB,OAAIA,EACOA,EAAQ6M,IAAID,GAEhBA,EAQX,SAASE,KACLX,GAAW1R,MAAMC,KAAMC,WA6D3B,SAASoS,KACLD,GAAerS,MAAMC,KAAMC,WAE3BD,KAAKsS,GAAK,KACVtS,KAAKuS,GAAK,KA4Ed,SAASC,KACLJ,GAAerS,MAAMC,KAAMC,WAsC/B,SAASwS,KACLhB,GAAW1R,MAAMC,KAAMC,WAEvBD,KAAK0S,OAAS,KACd1S,KAAK2S,OAAS,KAmElB,SAASC,KACLR,GAAerS,MAAMC,KAAMC,WA8B/B,SAAS4S,KACLT,GAAerS,MAAMC,KAAMC,WA2D/B,SAAS6S,KACLrB,GAAW1R,MAAMC,KAAMC,WAIvBD,KAAK+S,OAAQ,EACb/S,KAAKgT,SAAU,EAEfhT,KAAK0S,OAAS,KACd1S,KAAK2S,OAAS,KACd3S,KAAKiT,MAAQ,EAqGjB,SAASC,GAAO9O,EAASoB,GAGrB,OAFAA,EAAUA,GAAW,IACbgL,YAAc7O,EAAY6D,EAAQgL,YAAa0C,GAAOxB,SAASyB,QAChE,IAAIC,GAAQhP,EAASoB,GA7tBhCiM,GAAWrQ,UAAY,CAKnBsQ,SAAU,GAOVvB,IAAK,SAAS3K,GAKV,OAJAhI,EAAOwC,KAAKwF,QAASA,GAGrBxF,KAAKsF,SAAWtF,KAAKsF,QAAQiL,YAAYD,SAClCtQ,MAQXqT,cAAe,SAASnB,GACpB,GAAI3T,EAAe2T,EAAiB,gBAAiBlS,MACjD,OAAOA,KAGX,IAAI6R,EAAe7R,KAAK6R,aAMxB,OAJKA,GADLK,EAAkBD,GAA6BC,EAAiBlS,OAC9B2R,MAC9BE,EAAaK,EAAgBP,IAAMO,EACnCA,EAAgBmB,cAAcrT,OAE3BA,MAQXsT,kBAAmB,SAASpB,GACxB,OAAI3T,EAAe2T,EAAiB,oBAAqBlS,QAIzDkS,EAAkBD,GAA6BC,EAAiBlS,aACzDA,KAAK6R,aAAaK,EAAgBP,KAJ9B3R,MAafuT,eAAgB,SAASrB,GACrB,GAAI3T,EAAe2T,EAAiB,iBAAkBlS,MAClD,OAAOA,KAGX,IAAI8R,EAAc9R,KAAK8R,YAMvB,OAJ+C,IAA3C9O,EAAQ8O,EADZI,EAAkBD,GAA6BC,EAAiBlS,SAE5D8R,EAAYrO,KAAKyO,GACjBA,EAAgBqB,eAAevT,OAE5BA,MAQXwT,mBAAoB,SAAStB,GACzB,GAAI3T,EAAe2T,EAAiB,qBAAsBlS,MACtD,OAAOA,KAGXkS,EAAkBD,GAA6BC,EAAiBlS,MAChE,IAAIM,EAAQ0C,EAAQhD,KAAK8R,YAAaI,GAItC,OAHI5R,GAAS,GACTN,KAAK8R,YAAYzF,OAAO/L,EAAO,GAE5BN,MAOXyT,mBAAoB,WAChB,OAAOzT,KAAK8R,YAAY9S,OAAS,GAQrC0U,iBAAkB,SAASxB,GACvB,QAASlS,KAAK6R,aAAaK,EAAgBP,KAQ/CvI,KAAM,SAASpD,GACX,IAAI7I,EAAO6C,KACP4R,EAAQ5R,KAAK4R,MAEjB,SAASxI,EAAKuK,GACVxW,EAAKmI,QAAQ8D,KAAKuK,EAAO3N,GAIzB4L,EArJM,GAsJNxI,EAAKjM,EAAKqI,QAAQmO,MAAQ5B,GAASH,IAGvCxI,EAAKjM,EAAKqI,QAAQmO,OAEd3N,EAAM4N,iBACNxK,EAAKpD,EAAM4N,iBAIXhC,GAhKM,GAiKNxI,EAAKjM,EAAKqI,QAAQmO,MAAQ5B,GAASH,KAU3CiC,QAAS,SAAS7N,GACd,GAAIhG,KAAK8T,UACL,OAAO9T,KAAKoJ,KAAKpD,GAGrBhG,KAAK4R,MAAQJ,IAOjBsC,QAAS,WAEL,IADA,IAAIhV,EAAI,EACDA,EAAIkB,KAAK8R,YAAY9S,QAAQ,CAChC,KAAkC,GAA5BgB,KAAK8R,YAAYhT,GAAG8S,OACtB,OAAO,EAEX9S,IAEJ,OAAO,GAOXuK,UAAW,SAASyF,GAGhB,IAAIiF,EAAiBvW,EAAO,GAAIsR,GAGhC,IAAKtN,EAASxB,KAAKwF,QAAQI,OAAQ,CAAC5F,KAAM+T,IAGtC,OAFA/T,KAAKgU,aACLhU,KAAK4R,MAAQJ,IAKA,GAAbxR,KAAK4R,QACL5R,KAAK4R,MAvNI,GA0Nb5R,KAAK4R,MAAQ5R,KAAKiU,QAAQF,GAIT,GAAb/T,KAAK4R,OACL5R,KAAK6T,QAAQE,IAWrBE,QAAS,SAASnF,KAOlB4B,eAAgB,aAOhBsD,MAAO,cA8DXlT,EAAQsR,GAAgBX,GAAY,CAKhCC,SAAU,CAKNxL,SAAU,GASdgO,SAAU,SAASlO,GACf,IAAImO,EAAiBnU,KAAKwF,QAAQU,SAClC,OAA0B,IAAnBiO,GAAwBnO,EAAME,SAASlH,SAAWmV,GAS7DF,QAAS,SAASjO,GACd,IAAI4L,EAAQ5R,KAAK4R,MACb7L,EAAYC,EAAMD,UAElBqO,EAAuB,EAARxC,EACfyC,EAAUrU,KAAKkU,SAASlO,GAG5B,OAAIoO,IAlzCO,EAkzCUrO,IAA6BsO,GAvVpC,GAwVHzC,EACAwC,GAAgBC,EArzCnB,EAszCAtO,EA5VE,EA6VK6L,EA/VL,EAgWOA,EA/VL,EAkWDA,EAnWD,EAqWHJ,MAiBf1Q,EAAQuR,GAAeD,GAAgB,CAKnCV,SAAU,CACNiC,MAAO,MACPW,UAAW,GACXpO,SAAU,EACV2C,UA50CY0L,IA+0ChB7D,eAAgB,WACZ,IAAI7H,EAAY7I,KAAKwF,QAAQqD,UACzBwH,EAAU,GAOd,OA11CmBiB,EAo1CfzI,GACAwH,EAAQ5M,KAAKiM,IAEb7G,EAAY5D,GACZoL,EAAQ5M,KAAKgM,IAEVY,GAGXmE,cAAe,SAASxO,GACpB,IAAIR,EAAUxF,KAAKwF,QACfiP,GAAW,EACXtN,EAAWnB,EAAMmB,SACjB0B,EAAY7C,EAAM6C,UAClBpB,EAAIzB,EAAM0B,OACVC,EAAI3B,EAAM4B,OAed,OAZMiB,EAAYrD,EAAQqD,YAt2CPyI,EAu2CX9L,EAAQqD,WACRA,EAAmB,IAANpB,EA92CR,EA82CqCA,EAAI,EA72CzC,EACC,EA62CNgN,EAAWhN,GAAKzH,KAAKsS,GACrBnL,EAAWtJ,KAAKC,IAAIkI,EAAM0B,UAE1BmB,EAAmB,IAANlB,EAl3CR,EAk3CqCA,EAAI,EA/2C3C,EACE,GA+2CL8M,EAAW9M,GAAK3H,KAAKuS,GACrBpL,EAAWtJ,KAAKC,IAAIkI,EAAM4B,UAGlC5B,EAAM6C,UAAYA,EACX4L,GAAYtN,EAAW3B,EAAQ8O,WAAazL,EAAYrD,EAAQqD,WAG3EqL,SAAU,SAASlO,GACf,OAAOoM,GAAehR,UAAU8S,SAASjV,KAAKe,KAAMgG,KAva1C,EAwaLhG,KAAK4R,SAxaA,EAwa0B5R,KAAK4R,QAAwB5R,KAAKwU,cAAcxO,KAGxFoD,KAAM,SAASpD,GAEXhG,KAAKsS,GAAKtM,EAAM0B,OAChB1H,KAAKuS,GAAKvM,EAAM4B,OAEhB,IAAIiB,EAAYmJ,GAAahM,EAAM6C,WAE/BA,IACA7C,EAAM4N,gBAAkB5T,KAAKwF,QAAQmO,MAAQ9K,GAEjD7I,KAAKuB,OAAO6H,KAAKnK,KAAKe,KAAMgG,MAcpClF,EAAQ0R,GAAiBJ,GAAgB,CAKrCV,SAAU,CACNiC,MAAO,QACPW,UAAW,EACXpO,SAAU,GAGdwK,eAAgB,WACZ,MAAO,CAAClB,KAGZ0E,SAAU,SAASlO,GACf,OAAOhG,KAAKuB,OAAO2S,SAASjV,KAAKe,KAAMgG,KAClCnI,KAAKC,IAAIkI,EAAMoC,MAAQ,GAAKpI,KAAKwF,QAAQ8O,WApdpC,EAodiDtU,KAAK4R,QAGpExI,KAAM,SAASpD,GACX,GAAoB,IAAhBA,EAAMoC,MAAa,CACnB,IAAIsM,EAAQ1O,EAAMoC,MAAQ,EAAI,KAAO,MACrCpC,EAAM4N,gBAAkB5T,KAAKwF,QAAQmO,MAAQe,EAEjD1U,KAAKuB,OAAO6H,KAAKnK,KAAKe,KAAMgG,MAiBpClF,EAAQ2R,GAAiBhB,GAAY,CAKjCC,SAAU,CACNiC,MAAO,QACPzN,SAAU,EACVyO,KAAM,IACNL,UAAW,GAGf5D,eAAgB,WACZ,MAAO,CAACpB,KAGZ2E,QAAS,SAASjO,GACd,IAAIR,EAAUxF,KAAKwF,QACfoP,EAAgB5O,EAAME,SAASlH,SAAWwG,EAAQU,SAClD2O,EAAgB7O,EAAMmB,SAAW3B,EAAQ8O,UACzCQ,EAAY9O,EAAMgB,UAAYxB,EAAQmP,KAM1C,GAJA3U,KAAK2S,OAAS3M,GAIT6O,IAAkBD,GAAoC,GAAlB5O,EAAMD,YAA2C+O,EACtF9U,KAAKgU,aACF,GAn+CG,EAm+CChO,EAAMD,UACb/F,KAAKgU,QACLhU,KAAK0S,OAASzU,GAAkB,WAC5B+B,KAAK4R,MA1gBH,EA2gBF5R,KAAK6T,YACNrO,EAAQmP,KAAM3U,WACd,GAv+CC,EAu+CGgG,EAAMD,UACb,OA9gBM,EAghBV,OAAOyL,IAGXwC,MAAO,WACHe,aAAa/U,KAAK0S,SAGtBtJ,KAAM,SAASpD,GAvhBD,IAwhBNhG,KAAK4R,QAIL5L,GAt/CI,EAs/CMA,EAAMD,UAChB/F,KAAKsF,QAAQ8D,KAAKpJ,KAAKwF,QAAQmO,MAAQ,KAAM3N,IAE7ChG,KAAK2S,OAAO5L,UAAYhJ,IACxBiC,KAAKsF,QAAQ8D,KAAKpJ,KAAKwF,QAAQmO,MAAO3T,KAAK2S,aAevD7R,EAAQ8R,GAAkBR,GAAgB,CAKtCV,SAAU,CACNiC,MAAO,SACPW,UAAW,EACXpO,SAAU,GAGdwK,eAAgB,WACZ,MAAO,CAAClB,KAGZ0E,SAAU,SAASlO,GACf,OAAOhG,KAAKuB,OAAO2S,SAASjV,KAAKe,KAAMgG,KAClCnI,KAAKC,IAAIkI,EAAMuC,UAAYvI,KAAKwF,QAAQ8O,WAlkBnC,EAkkBgDtU,KAAK4R,UAcvE9Q,EAAQ+R,GAAiBT,GAAgB,CAKrCV,SAAU,CACNiC,MAAO,QACPW,UAAW,GACX5L,SAAU,GACVG,UAAW0L,GACXrO,SAAU,GAGdwK,eAAgB,WACZ,OAAO2B,GAAcjR,UAAUsP,eAAezR,KAAKe,OAGvDkU,SAAU,SAASlO,GACf,IACI0C,EADAG,EAAY7I,KAAKwF,QAAQqD,UAW7B,OARgB,GAAZA,EACAH,EAAW1C,EAAMgC,gBArjDFsJ,EAsjDRzI,EACPH,EAAW1C,EAAMkC,iBACVW,EAAY5D,IACnByD,EAAW1C,EAAMmC,kBAGdnI,KAAKuB,OAAO2S,SAASjV,KAAKe,KAAMgG,IACnC6C,EAAY7C,EAAM8B,iBAClB9B,EAAMmB,SAAWnH,KAAKwF,QAAQ8O,WAC9BtO,EAAMyC,aAAezI,KAAKwF,QAAQU,UAClCpI,EAAI4K,GAAY1I,KAAKwF,QAAQkD,UAzkDzB,EAykDqC1C,EAAMD,WAGvDqD,KAAM,SAASpD,GACX,IAAI6C,EAAYmJ,GAAahM,EAAM8B,iBAC/Be,GACA7I,KAAKsF,QAAQ8D,KAAKpJ,KAAKwF,QAAQmO,MAAQ9K,EAAW7C,GAGtDhG,KAAKsF,QAAQ8D,KAAKpJ,KAAKwF,QAAQmO,MAAO3N,MA2B9ClF,EAAQgS,GAAerB,GAAY,CAK/BC,SAAU,CACNiC,MAAO,MACPzN,SAAU,EACV8O,KAAM,EACNC,SAAU,IACVN,KAAM,IACNL,UAAW,EACXY,aAAc,IAGlBxE,eAAgB,WACZ,MAAO,CAACnB,KAGZ0E,QAAS,SAASjO,GACd,IAAIR,EAAUxF,KAAKwF,QAEfoP,EAAgB5O,EAAME,SAASlH,SAAWwG,EAAQU,SAClD2O,EAAgB7O,EAAMmB,SAAW3B,EAAQ8O,UACzCa,EAAiBnP,EAAMgB,UAAYxB,EAAQmP,KAI/C,GAFA3U,KAAKgU,QAzoDK,EA2oDLhO,EAAMD,WAA4C,IAAf/F,KAAKiT,MACzC,OAAOjT,KAAKoV,cAKhB,GAAIP,GAAiBM,GAAkBP,EAAe,CAClD,GAhpDI,GAgpDA5O,EAAMD,UACN,OAAO/F,KAAKoV,cAGhB,IAAIC,GAAgBrV,KAAK+S,OAAS/M,EAAMe,UAAY/G,KAAK+S,MAAQvN,EAAQyP,SACrEK,GAAiBtV,KAAKgT,SAAW5L,EAAYpH,KAAKgT,QAAShN,EAAMa,QAAUrB,EAAQ0P,aAgBvF,GAdAlV,KAAK+S,MAAQ/M,EAAMe,UACnB/G,KAAKgT,QAAUhN,EAAMa,OAEhByO,GAAkBD,EAGnBrV,KAAKiT,OAAS,EAFdjT,KAAKiT,MAAQ,EAKjBjT,KAAK2S,OAAS3M,EAKG,IADFhG,KAAKiT,MAAQzN,EAAQwP,KAIhC,OAAKhV,KAAKyT,sBAGNzT,KAAK0S,OAASzU,GAAkB,WAC5B+B,KAAK4R,MAltBX,EAmtBM5R,KAAK6T,YACNrO,EAAQyP,SAAUjV,MAttBvB,GAEA,EAytBV,OAAOwR,IAGX4D,YAAa,WAIT,OAHApV,KAAK0S,OAASzU,GAAkB,WAC5B+B,KAAK4R,MAAQJ,KACdxR,KAAKwF,QAAQyP,SAAUjV,MACnBwR,IAGXwC,MAAO,WACHe,aAAa/U,KAAK0S,SAGtBtJ,KAAM,WAvuBQ,GAwuBNpJ,KAAK4R,QACL5R,KAAK2S,OAAO4C,SAAWvV,KAAKiT,MAC5BjT,KAAKsF,QAAQ8D,KAAKpJ,KAAKwF,QAAQmO,MAAO3T,KAAK2S,YAoBvDO,GAAOsC,QAAU,QAMjBtC,GAAOxB,SAAW,CAOd+D,WAAW,EAQXlF,YAAalB,GAMbzJ,QAAQ,EASRH,YAAa,KAObiQ,WAAY,KAOZvC,OAAQ,CAEJ,CAACP,GAAkB,CAAChN,QAAQ,IAC5B,CAAC4M,GAAiB,CAAC5M,QAAQ,GAAQ,CAAC,WACpC,CAACiN,GAAiB,CAAChK,UArwDAyI,IAswDnB,CAACe,GAAe,CAACxJ,UAtwDEyI,GAswDgC,CAAC,UACpD,CAACwB,IACD,CAACA,GAAe,CAACa,MAAO,YAAaqB,KAAM,GAAI,CAAC,QAChD,CAACvC,KAQLkD,SAAU,CAMNC,WAAY,OAOZC,YAAa,OASbC,aAAc,OAOdC,eAAgB,OAOhBC,SAAU,OAQVC,kBAAmB,kBAa3B,SAAS7C,GAAQhP,EAASoB,GAzwD1B,IAA6BF,EA0wDzBtF,KAAKwF,QAAUhI,EAAO,GAAI0V,GAAOxB,SAAUlM,GAAW,IAEtDxF,KAAKwF,QAAQC,YAAczF,KAAKwF,QAAQC,aAAerB,EAEvDpE,KAAKkW,SAAW,GAChBlW,KAAKuG,QAAU,GACfvG,KAAKwQ,YAAc,GACnBxQ,KAAKmW,YAAc,GAEnBnW,KAAKoE,QAAUA,EACfpE,KAAKgG,MArwDE,KAfkBV,EAoxDQtF,MAlxDRwF,QAAQkQ,aAItBhR,EACAgH,GACA/G,EACA4I,GACC9I,EAGDuJ,GAFAxD,KAIOlF,EAASQ,GAswD3B9F,KAAKuQ,YAAc,IAAIN,GAAYjQ,KAAMA,KAAKwF,QAAQ+K,aAEtD6F,GAAepW,MAAM,GAErBrB,EAAKqB,KAAKwF,QAAQgL,aAAa,SAAS6F,GACpC,IAAI5F,EAAazQ,KAAKsW,IAAI,IAAKD,EAAK,GAAIA,EAAK,KAC7CA,EAAK,IAAM5F,EAAW4C,cAAcgD,EAAK,IACzCA,EAAK,IAAM5F,EAAW8C,eAAe8C,EAAK,MAC3CrW,MA4PP,SAASoW,GAAe9Q,EAASgR,GAC7B,IAIIvS,EAJAK,EAAUkB,EAAQlB,QACjBA,EAAQ+K,QAIbxQ,EAAK2G,EAAQE,QAAQmQ,UAAU,SAASzF,EAAO7Q,GAC3C0E,EAAOH,EAASQ,EAAQ+K,MAAO9P,GAC3BiX,GACAhR,EAAQ6Q,YAAYpS,GAAQK,EAAQ+K,MAAMpL,GAC1CK,EAAQ+K,MAAMpL,GAAQmM,GAEtB9L,EAAQ+K,MAAMpL,GAAQuB,EAAQ6Q,YAAYpS,IAAS,MAGtDuS,IACDhR,EAAQ6Q,YAAc,KAzQ9B/C,GAAQhS,UAAY,CAMhB+O,IAAK,SAAS3K,GAaV,OAZAhI,EAAOwC,KAAKwF,QAASA,GAGjBA,EAAQ+K,aACRvQ,KAAKuQ,YAAYD,SAEjB9K,EAAQC,cAERzF,KAAKgG,MAAMiE,UACXjK,KAAKgG,MAAM7F,OAASqF,EAAQC,YAC5BzF,KAAKgG,MAAMH,QAER7F,MASXuW,KAAM,SAASC,GACXxW,KAAKuG,QAAQkQ,QAAUD,EA5Db,EADP,GAsEPnN,UAAW,SAASyF,GAChB,IAAIvI,EAAUvG,KAAKuG,QACnB,IAAIA,EAAQkQ,QAAZ,CAOA,IAAIhG,EAFJzQ,KAAKuQ,YAAYQ,gBAAgBjC,GAGjC,IAAI0B,EAAcxQ,KAAKwQ,YAKnBkG,EAAgBnQ,EAAQmQ,gBAIvBA,GAAkBA,GAz8Bb,EAy8B8BA,EAAc9E,SAClD8E,EAAgBnQ,EAAQmQ,cAAgB,MAI5C,IADA,IAAI5X,EAAI,EACDA,EAAI0R,EAAYxR,QACnByR,EAAaD,EAAY1R,GA9FnB,IAsGFyH,EAAQkQ,SACHC,GAAiBjG,GAAciG,IAChCjG,EAAWiD,iBAAiBgD,GAGhCjG,EAAWuD,QAFXvD,EAAWpH,UAAUyF,IAOpB4H,GAAoC,GAAnBjG,EAAWmB,QAC7B8E,EAAgBnQ,EAAQmQ,cAAgBjG,GAE5C3R,MASRqT,IAAK,SAAS1B,GACV,GAAIA,aAAsBgB,GACtB,OAAOhB,EAIX,IADA,IAAID,EAAcxQ,KAAKwQ,YACd1R,EAAI,EAAGA,EAAI0R,EAAYxR,OAAQF,IACpC,GAAI0R,EAAY1R,GAAG0G,QAAQmO,OAASlD,EAChC,OAAOD,EAAY1R,GAG3B,OAAO,MASXwX,IAAK,SAAS7F,GACV,GAAIlS,EAAekS,EAAY,MAAOzQ,MAClC,OAAOA,KAIX,IAAI2W,EAAW3W,KAAKmS,IAAI1B,EAAWjL,QAAQmO,OAS3C,OARIgD,GACA3W,KAAK4W,OAAOD,GAGhB3W,KAAKwQ,YAAY/M,KAAKgN,GACtBA,EAAWnL,QAAUtF,KAErBA,KAAKuQ,YAAYD,SACVG,GAQXmG,OAAQ,SAASnG,GACb,GAAIlS,EAAekS,EAAY,SAAUzQ,MACrC,OAAOA,KAMX,GAHAyQ,EAAazQ,KAAKmS,IAAI1B,GAGN,CACZ,IAAID,EAAcxQ,KAAKwQ,YACnBlQ,EAAQ0C,EAAQwN,EAAaC,IAElB,IAAXnQ,IACAkQ,EAAYnE,OAAO/L,EAAO,GAC1BN,KAAKuQ,YAAYD,UAIzB,OAAOtQ,MASX6W,GAAI,SAASC,EAAQ9U,GACjB,GAAI8U,IAAWvZ,GAGXyE,IAAYzE,EAAhB,CAIA,IAAI2Y,EAAWlW,KAAKkW,SAKpB,OAJAvX,EAAKsD,EAAS6U,IAAS,SAASnD,GAC5BuC,EAASvC,GAASuC,EAASvC,IAAU,GACrCuC,EAASvC,GAAOlQ,KAAKzB,MAElBhC,OASX+W,IAAK,SAASD,EAAQ9U,GAClB,GAAI8U,IAAWvZ,EAAf,CAIA,IAAI2Y,EAAWlW,KAAKkW,SAQpB,OAPAvX,EAAKsD,EAAS6U,IAAS,SAASnD,GACvB3R,EAGDkU,EAASvC,IAAUuC,EAASvC,GAAOtH,OAAOrJ,EAAQkT,EAASvC,GAAQ3R,GAAU,UAFtEkU,EAASvC,MAKjB3T,OAQXoJ,KAAM,SAASuK,EAAOqD,GAEdhX,KAAKwF,QAAQiQ,WAkEzB,SAAyB9B,EAAOqD,GAC5B,IAAIC,EAAe5Z,EAAS6Z,YAAY,SACxCD,EAAaE,UAAUxD,GAAO,GAAM,GACpCsD,EAAaG,QAAUJ,EACvBA,EAAK7W,OAAOkX,cAAcJ,GArElBK,CAAgB3D,EAAOqD,GAI3B,IAAId,EAAWlW,KAAKkW,SAASvC,IAAU3T,KAAKkW,SAASvC,GAAOxQ,QAC5D,GAAK+S,GAAaA,EAASlX,OAA3B,CAIAgY,EAAK9U,KAAOyR,EACZqD,EAAK/F,eAAiB,WAClB+F,EAAK9N,SAAS+H,kBAIlB,IADA,IAAInS,EAAI,EACDA,EAAIoX,EAASlX,QAChBkX,EAASpX,GAAGkY,GACZlY,MAQRmL,QAAS,WACLjK,KAAKoE,SAAWgS,GAAepW,MAAM,GAErCA,KAAKkW,SAAW,GAChBlW,KAAKuG,QAAU,GACfvG,KAAKgG,MAAMiE,UACXjK,KAAKoE,QAAU,OAyCvB5G,EAAO0V,GAAQ,CACXqE,YAtoEc,EAuoEdC,WAtoEa,EAuoEbC,UAtoEY,EAuoEZC,aAtoEe,EAwoEfC,eAlrCiB,EAmrCjBC,YAlrCc,EAmrCdC,cAlrCgB,EAmrChBC,YAlrCc,EAmrCdC,iBAnrCc,EAorCdC,gBAlrCkB,GAmrClBxG,aAAcA,GAEdyG,eA9oEiB,EA+oEjB3G,eA9oEiB,EA+oEjB4G,gBA9oEkB,EA+oElBhT,aA9oEe,EA+oEfiT,eA9oEiB,GA+oEjB5D,qBA7oEuBjD,EA8oEvBrM,mBAAoBA,EACpBmT,cA7oEgB7D,GA+oEhBnB,QAASA,GACT/N,MAAOA,EACP4K,YAAaA,GAEb1C,WAAYA,GACZ/C,WAAYA,GACZkB,kBAAmBA,GACnBsC,gBAAiBA,GACjBnB,iBAAkBA,GAElB4E,WAAYA,GACZW,eAAgBA,GAChBiG,IAAKvF,GACLwF,IAAKjG,GACLkG,MAAO1F,GACP2F,MAAOhG,GACPiG,OAAQ7F,GACR8F,MAAOjG,GAEPoE,GAAI/U,EACJiV,IAAK3U,EACLzD,KAAMA,EACNiC,MAAOA,EACPH,OAAQA,EACRjD,OAAQA,EACRsD,QAASA,EACTxC,OAAQA,EACRsF,SAAUA,UAKsB,IAAXxG,EAAyBA,EAA0B,oBAATD,KAAuBA,KAAO,IACtF+V,OAASA,IAGhB,aACI,OAAOA,IACV,kCAtkFL,CA6kFG9V,OAAQC,YCjlFPsb,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBtb,IAAjBub,EACH,OAAOA,EAAa/b,QAGrB,IAAIC,EAAS2b,EAAyBE,GAAY,CAGjD9b,QAAS,IAOV,OAHAgc,EAAoBF,GAAU7b,EAAQA,EAAOD,QAAS6b,GAG/C5b,EAAOD,QCpBf6b,EAAoBI,EAAI,CAACjc,EAASkc,KACjC,IAAI,IAAI5V,KAAO4V,EACXL,EAAoBM,EAAED,EAAY5V,KAASuV,EAAoBM,EAAEnc,EAASsG,IAC5EnD,OAAOiZ,eAAepc,EAASsG,EAAK,CAAE+V,YAAY,EAAMjH,IAAK8G,EAAW5V,MCJ3EuV,EAAoBM,EAAI,CAACta,EAAKmF,IAAU7D,OAAOkB,UAAUlC,eAAeD,KAAKL,EAAKmF,GCClF6U,EAAoBS,EAAKtc,IACH,oBAAXuc,QAA0BA,OAAOC,aAC1CrZ,OAAOiZ,eAAepc,EAASuc,OAAOC,YAAa,CAAErJ,MAAO,WAE7DhQ,OAAOiZ,eAAepc,EAAS,aAAc,CAAEmT,OAAO,K,gECLvD,IAAIsJ,EAAS,WACX,IAAIC,EAAMzZ,KACN0Z,EAAKD,EAAIE,eACTC,EAAKH,EAAII,MAAMD,IAAMF,EACzB,OAAOE,EACL,MACA,CACE/C,GAAI,CACFiD,MAAO,SAASC,GAEd,OADAA,EAAOC,kBACAP,EAAIQ,cAAcF,MAI/B,CACEH,EACE,aACA,CACEM,MAAO,CAAEC,KAAM,SAAU9a,KAAM,4BAC/BwX,GAAI,CACFuD,WAAYX,EAAIY,sBAChBC,YAAab,EAAIc,yBAGrB,CACEd,EAAIe,OAASf,EAAIe,MAAMxb,OAAS,EAC5B4a,EACE,MACA,CACEa,WAAY,CACV,CACEpb,KAAM,OACNqb,QAAS,SACTxK,MAAOuJ,EAAIkB,cACXC,WAAY,kBAGhBC,IAAK,YACLC,YAAa,iBAEf,CACElB,EACE,MACA,CACEkB,YAAa,cACbjE,GAAI,CACFiD,MAAO,SAASC,GACdA,EAAOC,qBAIb,CACEJ,EACE,aACA,CACEM,MAAO,CACLC,KAAM,SACN9a,KAAMoa,EAAIsB,sBAGd,CAC2Bxd,MAAzBkc,EAAIuB,aAAa9Y,MACQ,SAAzBuX,EAAIuB,aAAa9Y,KACb0X,EAAG,MAAO,CACRvW,IAAKoW,EAAIuB,aAAara,IACtBma,YAAa,YACbZ,MAAO,CACLvZ,IAAK8Y,EAAIuB,aAAara,IACtBsa,OAAQxB,EAAIuB,aAAaC,QAAU,GACnCC,IAAKzB,EAAIuB,aAAaG,WAGI,WAA9B1B,EAAIe,MAAMf,EAAI2B,QAAQlZ,KACtB0X,EAAG,MAAO,CAAEkB,YAAa,oBAAsB,CAC7ClB,EAAG,SAAU,CACXM,MAAO,CACLvZ,IACE,iCACA8Y,EAAIe,MAAMf,EAAI2B,QAAQzJ,GACtB,cACF0J,MAAO,MACPC,OAAQ,MACRC,YAAa,IACbC,gBAAiB,QAIE,SAAzB/B,EAAIuB,aAAa9Y,KACjB0X,EACE,QACA,CACEvW,IAAKoW,EAAIuB,aAAaS,QAAQ,GAAG9a,IACjCka,IAAK,QACLX,MAAO,CACLwB,SAAU,GACVL,MAAO5B,EAAIuB,aAAaK,MACxBC,OAAQ7B,EAAIuB,aAAaM,OACzBK,SAAUlC,EAAIuB,aAAaW,WAG/BlC,EAAImC,GAAGnC,EAAIuB,aAAaS,SAAS,SAC/Blb,GAEA,OAAOqZ,EAAG,SAAU,CAClBvW,IAAK9C,EAAOI,IACZuZ,MAAO,CACLvZ,IAAKJ,EAAOI,IACZuB,KAAM3B,EAAO2B,WAInB,GAEFuX,EAAIoC,QAId,GAEFpC,EAAIqC,GAAG,KACPrC,EAAIsC,WACAnC,EACE,MACA,CACEkB,YAAa,qCACbkB,MAAO,CAAE,aAAcvC,EAAIwC,gBAC3BpF,GAAI,CACFiD,MAAO,SAASC,GACdA,EAAOC,mBAETkC,UAAW,SAASnC,GAClBN,EAAI0C,kBAAmB,GAEzBC,WAAY,SAASrC,GACnBN,EAAI0C,kBAAmB,KAI7B1C,EAAImC,GAAGnC,EAAI4C,aAAa,SAASC,EAAOhc,GACtC,OAAOsZ,EACL,MACA,CACEa,WAAY,CACV,CACEpb,KAAM,OACNqb,QAAS,SACTxK,MACE5P,GAASmZ,EAAI8C,WAAWC,OACxBlc,GAASmZ,EAAI8C,WAAWjU,IAC1BsS,WACE,yDAGNvX,IACyB,iBAAhBiZ,EAAMG,MACT,GAAKH,EAAMG,MAAQnc,EACnBA,EACN0b,MACE,iBACCvC,EAAI2B,SAAW9a,EAAQ,UAAY,IACtC6O,MAAO,CACLuN,gBAAiB,OAASJ,EAAMG,MAAQ,KAE1C5F,GAAI,CACFiD,MAAO,SAASC,GAEd,OADAA,EAAOC,kBACAP,EAAIkD,UAAUrc,MAI3B,CACgB,SAAdgc,EAAMpa,MAAiC,WAAdoa,EAAMpa,KAC3BuX,EAAImD,GAAG,YAAa,CAAChD,EAAG,eACxBH,EAAIoC,MAEV,MAGJ,GAEFpC,EAAIoC,KACRpC,EAAIqC,GAAG,KACPlC,EACE,MACA,CACEkB,YAAa,0BACbkB,MAAO,CAAE,aAAcvC,EAAIwC,gBAC3BpF,GAAI,CACFqF,UAAW,SAASnC,GAClBN,EAAI0C,kBAAmB,GAEzBC,WAAY,SAASrC,GACnBN,EAAI0C,kBAAmB,KAI7B,CACE1C,EAAImD,GACF,gBACA,CACEhD,EAAG,MAAO,CACRa,WAAY,CACV,CACEpb,KAAM,OACNqb,QAAS,SACTxK,MAAOuJ,EAAIoD,YACXjC,WAAY,gBAGhBkC,SAAU,CACRC,UAAWtD,EAAIuD,GAAGvD,EAAIuB,aAAaG,aAIzC,CAAEH,aAAcvB,EAAIuB,eAEtBvB,EAAIqC,GAAG,KACPlC,EACE,MACA,CAAEkB,YAAa,oBACf,CACErB,EAAImD,GACF,SACA,CACEnD,EAAIqC,GACF,iBACErC,EAAIuD,GAAGvD,EAAI2B,OAAS,GACpB,MACA3B,EAAIuD,GAAGvD,EAAIe,MAAMxb,QACjB,iBAGN,CAAEie,QAASxD,EAAI2B,OAAS,EAAG8B,MAAOzD,EAAIe,MAAMxb,UAGhD,IAGJ,GAEFya,EAAIqC,GAAG,KACPrC,EAAI0D,SACAvD,EACE,SACA,CACEkB,YAAa,yBACbkB,MAAO,CAAE,aAAcvC,EAAIwC,gBAC3B/B,MAAO,CAAEhY,KAAM,SAAUkb,MAAO3D,EAAI4D,WACpCxG,GAAI,CACFqF,UAAW,SAASnC,GAClBN,EAAI0C,kBAAmB,GAEzBC,WAAY,SAASrC,GACnBN,EAAI0C,kBAAmB,KAI7B,CAAC1C,EAAImD,GAAG,QAAS,CAAChD,EAAG,gBACrB,GAEFH,EAAIoC,KACRpC,EAAIqC,GAAG,KACPrC,EAAIe,MAAMxb,OAAS,EACf4a,EACE,SACA,CACEkB,YAAa,wCACbkB,MAAO,CAAE,aAAcvC,EAAIwC,gBAC3B/B,MAAO,CAAEhY,KAAM,SAAUkb,MAAO3D,EAAI6D,cACpCzG,GAAI,CACFiD,MAAO,SAASC,GAEd,OADAA,EAAOC,kBACAP,EAAI8D,iBAEbrB,UAAW,SAASnC,GAClBN,EAAI0C,kBAAmB,GAEzBC,WAAY,SAASrC,GACnBN,EAAI0C,kBAAmB,KAI7B,CAAC1C,EAAImD,GAAG,WAAY,CAAChD,EAAG,oBACxB,GAEFH,EAAIoC,KACRpC,EAAIqC,GAAG,KACPrC,EAAIe,MAAMxb,OAAS,EACf4a,EACE,SACA,CACEkB,YAAa,yCACbkB,MAAO,CAAE,aAAcvC,EAAIwC,gBAC3B/B,MAAO,CAAEhY,KAAM,SAAUkb,MAAO3D,EAAI+D,UACpC3G,GAAI,CACFiD,MAAO,SAASC,GAEd,OADAA,EAAOC,kBACAP,EAAIgE,aAEbvB,UAAW,SAASnC,GAClBN,EAAI0C,kBAAmB,GAEzBC,WAAY,SAASrC,GACnBN,EAAI0C,kBAAmB,KAI7B,CAAC1C,EAAImD,GAAG,OAAQ,CAAChD,EAAG,qBACpB,GAEFH,EAAIoC,OAGZpC,EAAIoC,QAId,IAIJrC,EAAOkE,eAAgB,ECjUvB,IAAI,EAAS,WACX,IACIhE,EADM1Z,KACG2Z,eACTC,EAFM5Z,KAEG6Z,MAAMD,IAAMF,EACzB,OAAOE,EAAG,OAAQ,CAChBA,EACE,MACA,CACEM,MAAO,CACLyD,KAAM,QACNlW,EAAG,MACHE,EAAG,MACH0T,MAAO,OACPC,OAAQ,OACRsC,QAAS,gBAGb,CACEhE,EAAG,OAAQ,CACTM,MAAO,CACLlB,EACE,mQCfC,SAAS6E,EACtBC,EACAtE,EACAuE,EACAC,EACAC,EACAC,EACAC,EACAC,GAGA,IAqBIC,EArBA7Y,EAAmC,mBAAlBsY,EACjBA,EAActY,QACdsY,EAsDJ,GAnDItE,IACFhU,EAAQgU,OAASA,EACjBhU,EAAQuY,gBAAkBA,EAC1BvY,EAAQ8Y,WAAY,GAIlBN,IACFxY,EAAQ+Y,YAAa,GAInBL,IACF1Y,EAAQgZ,SAAW,UAAYN,GAI7BC,GACFE,EAAO,SAAUjgB,IAEfA,EACEA,GACC4B,KAAKye,QAAUze,KAAKye,OAAOC,YAC3B1e,KAAKwC,QAAUxC,KAAKwC,OAAOic,QAAUze,KAAKwC,OAAOic,OAAOC,aAEZ,oBAAxBC,sBACrBvgB,EAAUugB,qBAGRV,GACFA,EAAahf,KAAKe,KAAM5B,GAGtBA,GAAWA,EAAQwgB,uBACrBxgB,EAAQwgB,sBAAsBtI,IAAI6H,IAKtC3Y,EAAQqZ,aAAeR,GACdJ,IACTI,EAAOD,EACH,WACAH,EAAahf,KACXe,MACCwF,EAAQ+Y,WAAave,KAAKwC,OAASxC,MAAM8e,MAAMC,SAASC,aAG3Df,GAGFI,EACF,GAAI7Y,EAAQ+Y,WAAY,CAGtB/Y,EAAQyZ,cAAgBZ,EAExB,IAAIa,EAAiB1Z,EAAQgU,OAC7BhU,EAAQgU,OAAS,SAAmC2F,EAAG/gB,GAErD,OADAigB,EAAKpf,KAAKb,GACH8gB,EAAeC,EAAG/gB,QAEtB,CAEL,IAAIuY,EAAWnR,EAAQ4Z,aACvB5Z,EAAQ4Z,aAAezI,EACnB,GAAGvJ,OAAOuJ,EAAU0H,GACpB,CAACA,GAIT,MAAO,CACLthB,QAAS+gB,EACTtY,QAASA,GDlEb,EAAOkY,eAAgB,EE5BvB,IAKI2B,EAAY,EALH,GAOX,EFoBoB,IElBpB,EACA,KACA,KACA,MAuBFA,EAAU7Z,QAAQ8Z,OAAS,mCAC3B,QAAeD,E,QCrCf,IAAI,EAAS,WACX,IACI3F,EADM1Z,KACG2Z,eACTC,EAFM5Z,KAEG6Z,MAAMD,IAAMF,EACzB,OAAOE,EAAG,OAAQ,CAChBA,EACE,MACA,CACEM,MAAO,CACLyD,KAAM,QACNlW,EAAG,MACHE,EAAG,MACH0T,MAAO,OACPC,OAAQ,OACRsC,QAAS,gBAGb,CACEhE,EAAG,OAAQ,CACTM,MAAO,CACLlB,EACE,kQAQd,EAAO0E,eAAgB,EC5BvB,IAKI,EAAY,EALH,GAOX,EDoBoB,IClBpB,EACA,KACA,KACA,MAuBF,EAAUlY,QAAQ8Z,OAAS,oCAC3B,QAAe,E,QCrCf,IAAI,EAAS,WACX,IACI5F,EADM1Z,KACG2Z,eACTC,EAFM5Z,KAEG6Z,MAAMD,IAAMF,EACzB,OAAOE,EAAG,OAAQ,CAChBA,EACE,MACA,CACE2F,YAAa,CAAE,oBAAqB,mBACpCrF,MAAO,CACLyD,KAAM,QACNlW,EAAG,MACHE,EAAG,MACH0T,MAAO,OACPC,OAAQ,OACRsC,QAAS,gBAGb,CACEhE,EAAG,OAAQ,CACTM,MAAO,CACLlB,EACE,qeAQd,EAAO0E,eAAgB,EC7BvB,IAKI,EAAY,EALH,GAOX,EDqBoB,ICnBpB,EACA,KACA,KACA,MAuBF,EAAUlY,QAAQ8Z,OAAS,+BAC3B,QAAe,E,QCrCf,IAAI,EAAS,WACX,IAAI7F,EAAMzZ,KACN0Z,EAAKD,EAAIE,eACTC,EAAKH,EAAII,MAAMD,IAAMF,EACzB,OAAOE,EACL,MACA,CACE2F,YAAa,CACX,oBAAqB,0BACrBC,OAAQ,QAEVtF,MAAO,CACLzS,EAAG,MACHE,EAAG,MACHiW,QAAS,sBACT,YAAa,aAGjB,CACEhE,EAAG,IAAK,CACNA,EAAG,IAAK,CACNA,EAAG,OAAQ,CACT2F,YAAa,CAAE5B,KAAM,QACrBzD,MAAO,CACLlB,EACE,4RAGNS,EAAIqC,GAAG,KACPlC,EAAG,OAAQ,CACT2F,YAAa,CAAE5B,KAAM,QACrBzD,MAAO,CACLlB,EACE,mZAShB,EAAO0E,eAAgB,ECzCvB,IAKI,EAAY,EALH,GAOX,EDiCoB,IC/BpB,EACA,KACA,KACA,MAuBF,EAAUlY,QAAQ8Z,OAAS,+BAC3B,QAAe,E,QCkIf,MAGsB,oBAAXliB,SACT8V,EAAS,EAAX,MCnKA,IAAI,EAAY,EDsKhB,CACEuM,WAAY,CACVC,cAAJ,EACIC,eAAJ,EACIC,UAAJ,EACIC,UAAJ,GAGEnW,MAAO,CACL8Q,MAAO,CACLtY,KAAMzD,MACNqhB,UAAU,GAGZC,cAAe,CACb7d,KAAM8d,QACNC,SAAN,GAGIC,aAAc,CACZhe,KAAM8d,QACNC,SAAN,GAGI9C,SAAU,CACRjb,KAAM8d,QACNC,SAAN,GAGIE,QAAS,CACPje,KAAMke,OACNH,QAAN,GAGII,QAAS,CACPne,KAAMke,OACNH,QAAN,GAGIlE,WAAY,CACV7Z,KAAM8d,QACNC,SAAN,GAIIK,SAAU,CACRpe,KAAM8d,QACNC,SAAN,GAGIM,aAAc,CACZre,KAAMke,OACNH,QAAN,KAGIO,kBAAmB,CACjBte,KAAMke,OACNH,QAAN,KAGIpD,YAAa,CACX3a,KAAM8d,QACNC,SAAN,GAGIQ,iBAAkB,CAChBve,KAAMke,OACNH,QAAN,GAGI5C,UAAW,CACTnb,KAAMwe,OACNT,QAAN,eAGI3C,aAAc,CACZpb,KAAMwe,OACNT,QAAN,YAGIzC,SAAU,CACRtb,KAAMwe,OACNT,QAAN,SAIEjJ,KAtFF,WAuFI,MAAO,CACLoE,OAAQpb,KAAKmgB,QACbxF,cAAe3a,KAAKkgB,aACpBjE,gBAAgB,EAChBlB,oBAAqB,0BACrB4F,MAAO,KACPC,iBAAkB,KAClBzE,kBAAkB,IAItB0E,SAAU,CACR7F,aADJ,WAEM,OAAOhb,KAAKwa,MAAMxa,KAAKob,SAGzBmB,WALJ,WAMM,IAAN,6BAEM,OAAIvc,KAAKob,QAAU0F,GAAY9gB,KAAKob,OAASpb,KAAKwa,MAAMxb,OAAS8hB,EACvE,CACQ,MAAR,iCACQ,IAAR,eAGU9gB,KAAKob,OAAS0F,EACxB,CACQ,MAAR,EACQ,IAAR,gBAGa,CACLtE,MAAOxc,KAAKwa,MAAMxb,OAASgB,KAAKqgB,QAChC/X,IAAKtI,KAAKwa,MAAMxb,OAAS,IAI7Bqd,YA1BJ,WA2BM,OAAOrc,KAAKwa,MAAMuG,KAAI,SAA5B,0CAIEC,MAAO,CACLrG,cADJ,SACA,GAEsB,MAAZtd,UACF2C,KAAKihB,iBAAiB/Q,IAI1BkL,OARJ,WASMpb,KAAKkhB,MAAM,iBAAkBlhB,KAAKob,QAE9Bpb,KAAKob,QAAUpb,KAAKwa,MAAMxb,OAASgB,KAAKygB,iBAAmB,GACrE,qBAEUzgB,KAAKob,SAAWpb,KAAKwa,MAAMxb,OAAS,GAC9C,0BAE0B,IAAhBgB,KAAKob,QACf,2BAEUpb,KAAKob,SAAWpb,KAAKmgB,SAC/B,6BAIEgB,QA1JF,WAiKI,GANInhB,KAAKsgB,WACPtgB,KAAK2gB,MAAQS,YAAYphB,KAAKyd,UAAWzd,KAAKugB,eAGhDvgB,KAAKihB,iBAAiBjhB,KAAK2a,eAEvB3a,KAAKqhB,MAAMC,UAAW,CACxB,IAAN,8BAEMC,EAAO1K,GAAG,aAAc7W,KAAKud,eAC7BgE,EAAO1K,GAAG,YAAa7W,KAAKyd,WAE5Bzd,KAAKqhB,MAAMC,UAAUnf,iBAAiB,YAAanC,KAAKwhB,qBACxDxhB,KAAKqhB,MAAMC,UAAUnf,iBAAiB,YAAanC,KAAKwhB,qBACxDxhB,KAAKqhB,MAAMC,UAAUnf,iBAAiB,YAAanC,KAAKwhB,uBAI5DC,cA7KF,WA8KIpkB,SAASgF,oBAAoB,UAAWrC,KAAK0hB,aAEzC1hB,KAAKsgB,UACPqB,cAAc3hB,KAAK2gB,OAGjB3gB,KAAKqhB,MAAMC,YACbthB,KAAKqhB,MAAMC,UAAUjf,oBAAoB,YAAarC,KAAKwhB,qBAC3DxhB,KAAKqhB,MAAMC,UAAUjf,oBAAoB,YAAarC,KAAKwhB,qBAC3DxhB,KAAKqhB,MAAMC,UAAUjf,oBAAoB,YAAarC,KAAKwhB,uBAI/DI,QAAS,CACPC,eADJ,WAEM7hB,KAAKkhB,MAAM,YAEPlhB,KAAK+f,eACP1iB,SAASykB,cAAc,QAAQC,UAAUzL,IAAI,aAG/CjZ,SAASykB,cAAc,QAAQC,UAAUzL,IAAI,YAC7CjZ,SAAS8E,iBAAiB,UAAWnC,KAAK0hB,aAEtC1hB,KAAKqhB,MAAMW,OAAShiB,KAAKqhB,MAAMW,MAAMrG,UACvC3b,KAAKqhB,MAAMW,MAAMC,QAIrBC,gBAhBJ,WAiBMliB,KAAKkhB,MAAM,YAEPlhB,KAAK+f,eACP1iB,SAASykB,cAAc,QAAQC,UAAUnL,OAAO,aAGlDvZ,SAASykB,cAAc,QAAQC,UAAUnL,OAAO,YAChDvZ,SAASgF,oBAAoB,UAAWrC,KAAK0hB,aAEzC1hB,KAAKqhB,MAAMW,QACbhiB,KAAKqhB,MAAMW,MAAMG,QACjBniB,KAAKqhB,MAAMW,MAAMI,YAAc,MAInCnB,iBAhCJ,SAgCA,GACU/Q,EAAOlQ,KAAK6hB,iBACtB,wBAGIlF,UArCJ,SAqCA,GACM3c,KAAKob,OAAS9a,EACdN,KAAKic,gBAAiB,EACtBjc,KAAK2a,eAAgB,GAGvB+G,YA3CJ,SA2CA,GACM,OAAQ/N,EAAM0O,SACZ,KAAK,GACHriB,KAAKud,gBACL,MACF,KAAK,GACHvd,KAAKyd,YACL,MACF,KAAK,GACHzd,KAAKia,kBAKXA,cAzDJ,WA0DUja,KAAKqhB,MAAMW,OACrB,yBACWhiB,KAAKmd,UACVnd,KAAKsiB,KAAKtiB,KAAM,iBAAiB,IAGnCyd,UAhEJ,WAiEMzd,KAAKsiB,KAAKtiB,KAAM,UAAWA,KAAKob,OAAS,GAAKpb,KAAKwa,MAAMxb,SAG3Due,cApEJ,WAqEMvd,KAAKsiB,KAAKtiB,KAAM,UAAWA,KAAKob,OAASpb,KAAKwa,MAAMxb,OAAS,GAAKgB,KAAKwa,MAAMxb,SAG/Eqb,sBAxEJ,WAyEMra,KAAKwhB,sBACLxhB,KAAK+a,oBAAsB,wBAG7BR,uBA7EJ,WA8EMva,KAAK+a,oBAAsB,2BAG7ByG,oBAjFJ,WAkFMzM,aAAa/U,KAAK4gB,kBAEd5gB,KAAKic,iBACPjc,KAAKic,gBAAiB,GAGpBjc,KAAKmc,iBACPnc,KAAKuiB,uBAELviB,KAAKwiB,yBAITA,sBA/FJ,WA+FA,WACMxiB,KAAK4gB,iBAAmBviB,YAAW,WACjC,EAAR,oBACA,yBAGIkkB,qBArGJ,WAsGMviB,KAAK4gB,iBAAmB,QCrc5BpH,EXsToB,IWpTpB,EACA,KACA,KACA,MAuBF,EAAUhU,QAAQ8Z,OAAS,8BAC3B,QAAe,E", "file": "vue-it-bigger.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Lightbox\"] = factory();\n\telse\n\t\troot[\"Lightbox\"] = factory();\n})(self, function() {\nreturn ", "/*! Hammer.JS - v2.0.7 - 2016-04-22\n * http://hammerjs.github.io/\n *\n * Copyright (c) 2016 <PERSON><PERSON>;\n * Licensed under the MIT license */\n(function(window, document, exportName, undefined) {\n  'use strict';\n\nvar VENDOR_PREFIXES = ['', 'webkit', 'Moz', 'MS', 'ms', 'o'];\nvar TEST_ELEMENT = document.createElement('div');\n\nvar TYPE_FUNCTION = 'function';\n\nvar round = Math.round;\nvar abs = Math.abs;\nvar now = Date.now;\n\n/**\n * set a timeout with a given scope\n * @param {Function} fn\n * @param {Number} timeout\n * @param {Object} context\n * @returns {number}\n */\nfunction setTimeoutContext(fn, timeout, context) {\n    return setTimeout(bindFn(fn, context), timeout);\n}\n\n/**\n * if the argument is an array, we want to execute the fn on each entry\n * if it aint an array we don't want to do a thing.\n * this is used by all the methods that accept a single and array argument.\n * @param {*|Array} arg\n * @param {String} fn\n * @param {Object} [context]\n * @returns {Boolean}\n */\nfunction invokeArrayArg(arg, fn, context) {\n    if (Array.isArray(arg)) {\n        each(arg, context[fn], context);\n        return true;\n    }\n    return false;\n}\n\n/**\n * walk objects and arrays\n * @param {Object} obj\n * @param {Function} iterator\n * @param {Object} context\n */\nfunction each(obj, iterator, context) {\n    var i;\n\n    if (!obj) {\n        return;\n    }\n\n    if (obj.forEach) {\n        obj.forEach(iterator, context);\n    } else if (obj.length !== undefined) {\n        i = 0;\n        while (i < obj.length) {\n            iterator.call(context, obj[i], i, obj);\n            i++;\n        }\n    } else {\n        for (i in obj) {\n            obj.hasOwnProperty(i) && iterator.call(context, obj[i], i, obj);\n        }\n    }\n}\n\n/**\n * wrap a method with a deprecation warning and stack trace\n * @param {Function} method\n * @param {String} name\n * @param {String} message\n * @returns {Function} A new function wrapping the supplied method.\n */\nfunction deprecate(method, name, message) {\n    var deprecationMessage = 'DEPRECATED METHOD: ' + name + '\\n' + message + ' AT \\n';\n    return function() {\n        var e = new Error('get-stack-trace');\n        var stack = e && e.stack ? e.stack.replace(/^[^\\(]+?[\\n$]/gm, '')\n            .replace(/^\\s+at\\s+/gm, '')\n            .replace(/^Object.<anonymous>\\s*\\(/gm, '{anonymous}()@') : 'Unknown Stack Trace';\n\n        var log = window.console && (window.console.warn || window.console.log);\n        if (log) {\n            log.call(window.console, deprecationMessage, stack);\n        }\n        return method.apply(this, arguments);\n    };\n}\n\n/**\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} target\n * @param {...Object} objects_to_assign\n * @returns {Object} target\n */\nvar assign;\nif (typeof Object.assign !== 'function') {\n    assign = function assign(target) {\n        if (target === undefined || target === null) {\n            throw new TypeError('Cannot convert undefined or null to object');\n        }\n\n        var output = Object(target);\n        for (var index = 1; index < arguments.length; index++) {\n            var source = arguments[index];\n            if (source !== undefined && source !== null) {\n                for (var nextKey in source) {\n                    if (source.hasOwnProperty(nextKey)) {\n                        output[nextKey] = source[nextKey];\n                    }\n                }\n            }\n        }\n        return output;\n    };\n} else {\n    assign = Object.assign;\n}\n\n/**\n * extend object.\n * means that properties in dest will be overwritten by the ones in src.\n * @param {Object} dest\n * @param {Object} src\n * @param {Boolean} [merge=false]\n * @returns {Object} dest\n */\nvar extend = deprecate(function extend(dest, src, merge) {\n    var keys = Object.keys(src);\n    var i = 0;\n    while (i < keys.length) {\n        if (!merge || (merge && dest[keys[i]] === undefined)) {\n            dest[keys[i]] = src[keys[i]];\n        }\n        i++;\n    }\n    return dest;\n}, 'extend', 'Use `assign`.');\n\n/**\n * merge the values from src in the dest.\n * means that properties that exist in dest will not be overwritten by src\n * @param {Object} dest\n * @param {Object} src\n * @returns {Object} dest\n */\nvar merge = deprecate(function merge(dest, src) {\n    return extend(dest, src, true);\n}, 'merge', 'Use `assign`.');\n\n/**\n * simple class inheritance\n * @param {Function} child\n * @param {Function} base\n * @param {Object} [properties]\n */\nfunction inherit(child, base, properties) {\n    var baseP = base.prototype,\n        childP;\n\n    childP = child.prototype = Object.create(baseP);\n    childP.constructor = child;\n    childP._super = baseP;\n\n    if (properties) {\n        assign(childP, properties);\n    }\n}\n\n/**\n * simple function bind\n * @param {Function} fn\n * @param {Object} context\n * @returns {Function}\n */\nfunction bindFn(fn, context) {\n    return function boundFn() {\n        return fn.apply(context, arguments);\n    };\n}\n\n/**\n * let a boolean value also be a function that must return a boolean\n * this first item in args will be used as the context\n * @param {Boolean|Function} val\n * @param {Array} [args]\n * @returns {Boolean}\n */\nfunction boolOrFn(val, args) {\n    if (typeof val == TYPE_FUNCTION) {\n        return val.apply(args ? args[0] || undefined : undefined, args);\n    }\n    return val;\n}\n\n/**\n * use the val2 when val1 is undefined\n * @param {*} val1\n * @param {*} val2\n * @returns {*}\n */\nfunction ifUndefined(val1, val2) {\n    return (val1 === undefined) ? val2 : val1;\n}\n\n/**\n * addEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nfunction addEventListeners(target, types, handler) {\n    each(splitStr(types), function(type) {\n        target.addEventListener(type, handler, false);\n    });\n}\n\n/**\n * removeEventListener with multiple events at once\n * @param {EventTarget} target\n * @param {String} types\n * @param {Function} handler\n */\nfunction removeEventListeners(target, types, handler) {\n    each(splitStr(types), function(type) {\n        target.removeEventListener(type, handler, false);\n    });\n}\n\n/**\n * find if a node is in the given parent\n * @method hasParent\n * @param {HTMLElement} node\n * @param {HTMLElement} parent\n * @return {Boolean} found\n */\nfunction hasParent(node, parent) {\n    while (node) {\n        if (node == parent) {\n            return true;\n        }\n        node = node.parentNode;\n    }\n    return false;\n}\n\n/**\n * small indexOf wrapper\n * @param {String} str\n * @param {String} find\n * @returns {Boolean} found\n */\nfunction inStr(str, find) {\n    return str.indexOf(find) > -1;\n}\n\n/**\n * split string on whitespace\n * @param {String} str\n * @returns {Array} words\n */\nfunction splitStr(str) {\n    return str.trim().split(/\\s+/g);\n}\n\n/**\n * find if a array contains the object using indexOf or a simple polyFill\n * @param {Array} src\n * @param {String} find\n * @param {String} [findByKey]\n * @return {Boolean|Number} false when not found, or the index\n */\nfunction inArray(src, find, findByKey) {\n    if (src.indexOf && !findByKey) {\n        return src.indexOf(find);\n    } else {\n        var i = 0;\n        while (i < src.length) {\n            if ((findByKey && src[i][findByKey] == find) || (!findByKey && src[i] === find)) {\n                return i;\n            }\n            i++;\n        }\n        return -1;\n    }\n}\n\n/**\n * convert array-like objects to real arrays\n * @param {Object} obj\n * @returns {Array}\n */\nfunction toArray(obj) {\n    return Array.prototype.slice.call(obj, 0);\n}\n\n/**\n * unique array with objects based on a key (like 'id') or just by the array's value\n * @param {Array} src [{id:1},{id:2},{id:1}]\n * @param {String} [key]\n * @param {Boolean} [sort=False]\n * @returns {Array} [{id:1},{id:2}]\n */\nfunction uniqueArray(src, key, sort) {\n    var results = [];\n    var values = [];\n    var i = 0;\n\n    while (i < src.length) {\n        var val = key ? src[i][key] : src[i];\n        if (inArray(values, val) < 0) {\n            results.push(src[i]);\n        }\n        values[i] = val;\n        i++;\n    }\n\n    if (sort) {\n        if (!key) {\n            results = results.sort();\n        } else {\n            results = results.sort(function sortUniqueArray(a, b) {\n                return a[key] > b[key];\n            });\n        }\n    }\n\n    return results;\n}\n\n/**\n * get the prefixed property\n * @param {Object} obj\n * @param {String} property\n * @returns {String|Undefined} prefixed\n */\nfunction prefixed(obj, property) {\n    var prefix, prop;\n    var camelProp = property[0].toUpperCase() + property.slice(1);\n\n    var i = 0;\n    while (i < VENDOR_PREFIXES.length) {\n        prefix = VENDOR_PREFIXES[i];\n        prop = (prefix) ? prefix + camelProp : property;\n\n        if (prop in obj) {\n            return prop;\n        }\n        i++;\n    }\n    return undefined;\n}\n\n/**\n * get a unique id\n * @returns {number} uniqueId\n */\nvar _uniqueId = 1;\nfunction uniqueId() {\n    return _uniqueId++;\n}\n\n/**\n * get the window object of an element\n * @param {HTMLElement} element\n * @returns {DocumentView|Window}\n */\nfunction getWindowForElement(element) {\n    var doc = element.ownerDocument || element;\n    return (doc.defaultView || doc.parentWindow || window);\n}\n\nvar MOBILE_REGEX = /mobile|tablet|ip(ad|hone|od)|android/i;\n\nvar SUPPORT_TOUCH = ('ontouchstart' in window);\nvar SUPPORT_POINTER_EVENTS = prefixed(window, 'PointerEvent') !== undefined;\nvar SUPPORT_ONLY_TOUCH = SUPPORT_TOUCH && MOBILE_REGEX.test(navigator.userAgent);\n\nvar INPUT_TYPE_TOUCH = 'touch';\nvar INPUT_TYPE_PEN = 'pen';\nvar INPUT_TYPE_MOUSE = 'mouse';\nvar INPUT_TYPE_KINECT = 'kinect';\n\nvar COMPUTE_INTERVAL = 25;\n\nvar INPUT_START = 1;\nvar INPUT_MOVE = 2;\nvar INPUT_END = 4;\nvar INPUT_CANCEL = 8;\n\nvar DIRECTION_NONE = 1;\nvar DIRECTION_LEFT = 2;\nvar DIRECTION_RIGHT = 4;\nvar DIRECTION_UP = 8;\nvar DIRECTION_DOWN = 16;\n\nvar DIRECTION_HORIZONTAL = DIRECTION_LEFT | DIRECTION_RIGHT;\nvar DIRECTION_VERTICAL = DIRECTION_UP | DIRECTION_DOWN;\nvar DIRECTION_ALL = DIRECTION_HORIZONTAL | DIRECTION_VERTICAL;\n\nvar PROPS_XY = ['x', 'y'];\nvar PROPS_CLIENT_XY = ['clientX', 'clientY'];\n\n/**\n * create new input type manager\n * @param {Manager} manager\n * @param {Function} callback\n * @returns {Input}\n * @constructor\n */\nfunction Input(manager, callback) {\n    var self = this;\n    this.manager = manager;\n    this.callback = callback;\n    this.element = manager.element;\n    this.target = manager.options.inputTarget;\n\n    // smaller wrapper around the handler, for the scope and the enabled state of the manager,\n    // so when disabled the input events are completely bypassed.\n    this.domHandler = function(ev) {\n        if (boolOrFn(manager.options.enable, [manager])) {\n            self.handler(ev);\n        }\n    };\n\n    this.init();\n\n}\n\nInput.prototype = {\n    /**\n     * should handle the inputEvent data and trigger the callback\n     * @virtual\n     */\n    handler: function() { },\n\n    /**\n     * bind the events\n     */\n    init: function() {\n        this.evEl && addEventListeners(this.element, this.evEl, this.domHandler);\n        this.evTarget && addEventListeners(this.target, this.evTarget, this.domHandler);\n        this.evWin && addEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    },\n\n    /**\n     * unbind the events\n     */\n    destroy: function() {\n        this.evEl && removeEventListeners(this.element, this.evEl, this.domHandler);\n        this.evTarget && removeEventListeners(this.target, this.evTarget, this.domHandler);\n        this.evWin && removeEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);\n    }\n};\n\n/**\n * create new input type manager\n * called by the Manager constructor\n * @param {Hammer} manager\n * @returns {Input}\n */\nfunction createInputInstance(manager) {\n    var Type;\n    var inputClass = manager.options.inputClass;\n\n    if (inputClass) {\n        Type = inputClass;\n    } else if (SUPPORT_POINTER_EVENTS) {\n        Type = PointerEventInput;\n    } else if (SUPPORT_ONLY_TOUCH) {\n        Type = TouchInput;\n    } else if (!SUPPORT_TOUCH) {\n        Type = MouseInput;\n    } else {\n        Type = TouchMouseInput;\n    }\n    return new (Type)(manager, inputHandler);\n}\n\n/**\n * handle input events\n * @param {Manager} manager\n * @param {String} eventType\n * @param {Object} input\n */\nfunction inputHandler(manager, eventType, input) {\n    var pointersLen = input.pointers.length;\n    var changedPointersLen = input.changedPointers.length;\n    var isFirst = (eventType & INPUT_START && (pointersLen - changedPointersLen === 0));\n    var isFinal = (eventType & (INPUT_END | INPUT_CANCEL) && (pointersLen - changedPointersLen === 0));\n\n    input.isFirst = !!isFirst;\n    input.isFinal = !!isFinal;\n\n    if (isFirst) {\n        manager.session = {};\n    }\n\n    // source event is the normalized value of the domEvents\n    // like 'touchstart, mouseup, pointerdown'\n    input.eventType = eventType;\n\n    // compute scale, rotation etc\n    computeInputData(manager, input);\n\n    // emit secret event\n    manager.emit('hammer.input', input);\n\n    manager.recognize(input);\n    manager.session.prevInput = input;\n}\n\n/**\n * extend the data with some usable properties like scale, rotate, velocity etc\n * @param {Object} manager\n * @param {Object} input\n */\nfunction computeInputData(manager, input) {\n    var session = manager.session;\n    var pointers = input.pointers;\n    var pointersLength = pointers.length;\n\n    // store the first input to calculate the distance and direction\n    if (!session.firstInput) {\n        session.firstInput = simpleCloneInputData(input);\n    }\n\n    // to compute scale and rotation we need to store the multiple touches\n    if (pointersLength > 1 && !session.firstMultiple) {\n        session.firstMultiple = simpleCloneInputData(input);\n    } else if (pointersLength === 1) {\n        session.firstMultiple = false;\n    }\n\n    var firstInput = session.firstInput;\n    var firstMultiple = session.firstMultiple;\n    var offsetCenter = firstMultiple ? firstMultiple.center : firstInput.center;\n\n    var center = input.center = getCenter(pointers);\n    input.timeStamp = now();\n    input.deltaTime = input.timeStamp - firstInput.timeStamp;\n\n    input.angle = getAngle(offsetCenter, center);\n    input.distance = getDistance(offsetCenter, center);\n\n    computeDeltaXY(session, input);\n    input.offsetDirection = getDirection(input.deltaX, input.deltaY);\n\n    var overallVelocity = getVelocity(input.deltaTime, input.deltaX, input.deltaY);\n    input.overallVelocityX = overallVelocity.x;\n    input.overallVelocityY = overallVelocity.y;\n    input.overallVelocity = (abs(overallVelocity.x) > abs(overallVelocity.y)) ? overallVelocity.x : overallVelocity.y;\n\n    input.scale = firstMultiple ? getScale(firstMultiple.pointers, pointers) : 1;\n    input.rotation = firstMultiple ? getRotation(firstMultiple.pointers, pointers) : 0;\n\n    input.maxPointers = !session.prevInput ? input.pointers.length : ((input.pointers.length >\n        session.prevInput.maxPointers) ? input.pointers.length : session.prevInput.maxPointers);\n\n    computeIntervalInputData(session, input);\n\n    // find the correct target\n    var target = manager.element;\n    if (hasParent(input.srcEvent.target, target)) {\n        target = input.srcEvent.target;\n    }\n    input.target = target;\n}\n\nfunction computeDeltaXY(session, input) {\n    var center = input.center;\n    var offset = session.offsetDelta || {};\n    var prevDelta = session.prevDelta || {};\n    var prevInput = session.prevInput || {};\n\n    if (input.eventType === INPUT_START || prevInput.eventType === INPUT_END) {\n        prevDelta = session.prevDelta = {\n            x: prevInput.deltaX || 0,\n            y: prevInput.deltaY || 0\n        };\n\n        offset = session.offsetDelta = {\n            x: center.x,\n            y: center.y\n        };\n    }\n\n    input.deltaX = prevDelta.x + (center.x - offset.x);\n    input.deltaY = prevDelta.y + (center.y - offset.y);\n}\n\n/**\n * velocity is calculated every x ms\n * @param {Object} session\n * @param {Object} input\n */\nfunction computeIntervalInputData(session, input) {\n    var last = session.lastInterval || input,\n        deltaTime = input.timeStamp - last.timeStamp,\n        velocity, velocityX, velocityY, direction;\n\n    if (input.eventType != INPUT_CANCEL && (deltaTime > COMPUTE_INTERVAL || last.velocity === undefined)) {\n        var deltaX = input.deltaX - last.deltaX;\n        var deltaY = input.deltaY - last.deltaY;\n\n        var v = getVelocity(deltaTime, deltaX, deltaY);\n        velocityX = v.x;\n        velocityY = v.y;\n        velocity = (abs(v.x) > abs(v.y)) ? v.x : v.y;\n        direction = getDirection(deltaX, deltaY);\n\n        session.lastInterval = input;\n    } else {\n        // use latest velocity info if it doesn't overtake a minimum period\n        velocity = last.velocity;\n        velocityX = last.velocityX;\n        velocityY = last.velocityY;\n        direction = last.direction;\n    }\n\n    input.velocity = velocity;\n    input.velocityX = velocityX;\n    input.velocityY = velocityY;\n    input.direction = direction;\n}\n\n/**\n * create a simple clone from the input used for storage of firstInput and firstMultiple\n * @param {Object} input\n * @returns {Object} clonedInputData\n */\nfunction simpleCloneInputData(input) {\n    // make a simple copy of the pointers because we will get a reference if we don't\n    // we only need clientXY for the calculations\n    var pointers = [];\n    var i = 0;\n    while (i < input.pointers.length) {\n        pointers[i] = {\n            clientX: round(input.pointers[i].clientX),\n            clientY: round(input.pointers[i].clientY)\n        };\n        i++;\n    }\n\n    return {\n        timeStamp: now(),\n        pointers: pointers,\n        center: getCenter(pointers),\n        deltaX: input.deltaX,\n        deltaY: input.deltaY\n    };\n}\n\n/**\n * get the center of all the pointers\n * @param {Array} pointers\n * @return {Object} center contains `x` and `y` properties\n */\nfunction getCenter(pointers) {\n    var pointersLength = pointers.length;\n\n    // no need to loop when only one touch\n    if (pointersLength === 1) {\n        return {\n            x: round(pointers[0].clientX),\n            y: round(pointers[0].clientY)\n        };\n    }\n\n    var x = 0, y = 0, i = 0;\n    while (i < pointersLength) {\n        x += pointers[i].clientX;\n        y += pointers[i].clientY;\n        i++;\n    }\n\n    return {\n        x: round(x / pointersLength),\n        y: round(y / pointersLength)\n    };\n}\n\n/**\n * calculate the velocity between two points. unit is in px per ms.\n * @param {Number} deltaTime\n * @param {Number} x\n * @param {Number} y\n * @return {Object} velocity `x` and `y`\n */\nfunction getVelocity(deltaTime, x, y) {\n    return {\n        x: x / deltaTime || 0,\n        y: y / deltaTime || 0\n    };\n}\n\n/**\n * get the direction between two points\n * @param {Number} x\n * @param {Number} y\n * @return {Number} direction\n */\nfunction getDirection(x, y) {\n    if (x === y) {\n        return DIRECTION_NONE;\n    }\n\n    if (abs(x) >= abs(y)) {\n        return x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;\n    }\n    return y < 0 ? DIRECTION_UP : DIRECTION_DOWN;\n}\n\n/**\n * calculate the absolute distance between two points\n * @param {Object} p1 {x, y}\n * @param {Object} p2 {x, y}\n * @param {Array} [props] containing x and y keys\n * @return {Number} distance\n */\nfunction getDistance(p1, p2, props) {\n    if (!props) {\n        props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]],\n        y = p2[props[1]] - p1[props[1]];\n\n    return Math.sqrt((x * x) + (y * y));\n}\n\n/**\n * calculate the angle between two coordinates\n * @param {Object} p1\n * @param {Object} p2\n * @param {Array} [props] containing x and y keys\n * @return {Number} angle\n */\nfunction getAngle(p1, p2, props) {\n    if (!props) {\n        props = PROPS_XY;\n    }\n    var x = p2[props[0]] - p1[props[0]],\n        y = p2[props[1]] - p1[props[1]];\n    return Math.atan2(y, x) * 180 / Math.PI;\n}\n\n/**\n * calculate the rotation degrees between two pointersets\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} rotation\n */\nfunction getRotation(start, end) {\n    return getAngle(end[1], end[0], PROPS_CLIENT_XY) + getAngle(start[1], start[0], PROPS_CLIENT_XY);\n}\n\n/**\n * calculate the scale factor between two pointersets\n * no scale is 1, and goes down to 0 when pinched together, and bigger when pinched out\n * @param {Array} start array of pointers\n * @param {Array} end array of pointers\n * @return {Number} scale\n */\nfunction getScale(start, end) {\n    return getDistance(end[0], end[1], PROPS_CLIENT_XY) / getDistance(start[0], start[1], PROPS_CLIENT_XY);\n}\n\nvar MOUSE_INPUT_MAP = {\n    mousedown: INPUT_START,\n    mousemove: INPUT_MOVE,\n    mouseup: INPUT_END\n};\n\nvar MOUSE_ELEMENT_EVENTS = 'mousedown';\nvar MOUSE_WINDOW_EVENTS = 'mousemove mouseup';\n\n/**\n * Mouse events input\n * @constructor\n * @extends Input\n */\nfunction MouseInput() {\n    this.evEl = MOUSE_ELEMENT_EVENTS;\n    this.evWin = MOUSE_WINDOW_EVENTS;\n\n    this.pressed = false; // mousedown state\n\n    Input.apply(this, arguments);\n}\n\ninherit(MouseInput, Input, {\n    /**\n     * handle mouse events\n     * @param {Object} ev\n     */\n    handler: function MEhandler(ev) {\n        var eventType = MOUSE_INPUT_MAP[ev.type];\n\n        // on start we want to have the left mouse button down\n        if (eventType & INPUT_START && ev.button === 0) {\n            this.pressed = true;\n        }\n\n        if (eventType & INPUT_MOVE && ev.which !== 1) {\n            eventType = INPUT_END;\n        }\n\n        // mouse must be down\n        if (!this.pressed) {\n            return;\n        }\n\n        if (eventType & INPUT_END) {\n            this.pressed = false;\n        }\n\n        this.callback(this.manager, eventType, {\n            pointers: [ev],\n            changedPointers: [ev],\n            pointerType: INPUT_TYPE_MOUSE,\n            srcEvent: ev\n        });\n    }\n});\n\nvar POINTER_INPUT_MAP = {\n    pointerdown: INPUT_START,\n    pointermove: INPUT_MOVE,\n    pointerup: INPUT_END,\n    pointercancel: INPUT_CANCEL,\n    pointerout: INPUT_CANCEL\n};\n\n// in IE10 the pointer types is defined as an enum\nvar IE10_POINTER_TYPE_ENUM = {\n    2: INPUT_TYPE_TOUCH,\n    3: INPUT_TYPE_PEN,\n    4: INPUT_TYPE_MOUSE,\n    5: INPUT_TYPE_KINECT // see https://twitter.com/jacobrossi/status/480596438489890816\n};\n\nvar POINTER_ELEMENT_EVENTS = 'pointerdown';\nvar POINTER_WINDOW_EVENTS = 'pointermove pointerup pointercancel';\n\n// IE10 has prefixed support, and case-sensitive\nif (window.MSPointerEvent && !window.PointerEvent) {\n    POINTER_ELEMENT_EVENTS = 'MSPointerDown';\n    POINTER_WINDOW_EVENTS = 'MSPointerMove MSPointerUp MSPointerCancel';\n}\n\n/**\n * Pointer events input\n * @constructor\n * @extends Input\n */\nfunction PointerEventInput() {\n    this.evEl = POINTER_ELEMENT_EVENTS;\n    this.evWin = POINTER_WINDOW_EVENTS;\n\n    Input.apply(this, arguments);\n\n    this.store = (this.manager.session.pointerEvents = []);\n}\n\ninherit(PointerEventInput, Input, {\n    /**\n     * handle mouse events\n     * @param {Object} ev\n     */\n    handler: function PEhandler(ev) {\n        var store = this.store;\n        var removePointer = false;\n\n        var eventTypeNormalized = ev.type.toLowerCase().replace('ms', '');\n        var eventType = POINTER_INPUT_MAP[eventTypeNormalized];\n        var pointerType = IE10_POINTER_TYPE_ENUM[ev.pointerType] || ev.pointerType;\n\n        var isTouch = (pointerType == INPUT_TYPE_TOUCH);\n\n        // get index of the event in the store\n        var storeIndex = inArray(store, ev.pointerId, 'pointerId');\n\n        // start and mouse must be down\n        if (eventType & INPUT_START && (ev.button === 0 || isTouch)) {\n            if (storeIndex < 0) {\n                store.push(ev);\n                storeIndex = store.length - 1;\n            }\n        } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n            removePointer = true;\n        }\n\n        // it not found, so the pointer hasn't been down (so it's probably a hover)\n        if (storeIndex < 0) {\n            return;\n        }\n\n        // update the event in the store\n        store[storeIndex] = ev;\n\n        this.callback(this.manager, eventType, {\n            pointers: store,\n            changedPointers: [ev],\n            pointerType: pointerType,\n            srcEvent: ev\n        });\n\n        if (removePointer) {\n            // remove from the store\n            store.splice(storeIndex, 1);\n        }\n    }\n});\n\nvar SINGLE_TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n};\n\nvar SINGLE_TOUCH_TARGET_EVENTS = 'touchstart';\nvar SINGLE_TOUCH_WINDOW_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * Touch events input\n * @constructor\n * @extends Input\n */\nfunction SingleTouchInput() {\n    this.evTarget = SINGLE_TOUCH_TARGET_EVENTS;\n    this.evWin = SINGLE_TOUCH_WINDOW_EVENTS;\n    this.started = false;\n\n    Input.apply(this, arguments);\n}\n\ninherit(SingleTouchInput, Input, {\n    handler: function TEhandler(ev) {\n        var type = SINGLE_TOUCH_INPUT_MAP[ev.type];\n\n        // should we handle the touch events?\n        if (type === INPUT_START) {\n            this.started = true;\n        }\n\n        if (!this.started) {\n            return;\n        }\n\n        var touches = normalizeSingleTouches.call(this, ev, type);\n\n        // when done, reset the started state\n        if (type & (INPUT_END | INPUT_CANCEL) && touches[0].length - touches[1].length === 0) {\n            this.started = false;\n        }\n\n        this.callback(this.manager, type, {\n            pointers: touches[0],\n            changedPointers: touches[1],\n            pointerType: INPUT_TYPE_TOUCH,\n            srcEvent: ev\n        });\n    }\n});\n\n/**\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction normalizeSingleTouches(ev, type) {\n    var all = toArray(ev.touches);\n    var changed = toArray(ev.changedTouches);\n\n    if (type & (INPUT_END | INPUT_CANCEL)) {\n        all = uniqueArray(all.concat(changed), 'identifier', true);\n    }\n\n    return [all, changed];\n}\n\nvar TOUCH_INPUT_MAP = {\n    touchstart: INPUT_START,\n    touchmove: INPUT_MOVE,\n    touchend: INPUT_END,\n    touchcancel: INPUT_CANCEL\n};\n\nvar TOUCH_TARGET_EVENTS = 'touchstart touchmove touchend touchcancel';\n\n/**\n * Multi-user touch events input\n * @constructor\n * @extends Input\n */\nfunction TouchInput() {\n    this.evTarget = TOUCH_TARGET_EVENTS;\n    this.targetIds = {};\n\n    Input.apply(this, arguments);\n}\n\ninherit(TouchInput, Input, {\n    handler: function MTEhandler(ev) {\n        var type = TOUCH_INPUT_MAP[ev.type];\n        var touches = getTouches.call(this, ev, type);\n        if (!touches) {\n            return;\n        }\n\n        this.callback(this.manager, type, {\n            pointers: touches[0],\n            changedPointers: touches[1],\n            pointerType: INPUT_TYPE_TOUCH,\n            srcEvent: ev\n        });\n    }\n});\n\n/**\n * @this {TouchInput}\n * @param {Object} ev\n * @param {Number} type flag\n * @returns {undefined|Array} [all, changed]\n */\nfunction getTouches(ev, type) {\n    var allTouches = toArray(ev.touches);\n    var targetIds = this.targetIds;\n\n    // when there is only one touch, the process can be simplified\n    if (type & (INPUT_START | INPUT_MOVE) && allTouches.length === 1) {\n        targetIds[allTouches[0].identifier] = true;\n        return [allTouches, allTouches];\n    }\n\n    var i,\n        targetTouches,\n        changedTouches = toArray(ev.changedTouches),\n        changedTargetTouches = [],\n        target = this.target;\n\n    // get target touches from touches\n    targetTouches = allTouches.filter(function(touch) {\n        return hasParent(touch.target, target);\n    });\n\n    // collect touches\n    if (type === INPUT_START) {\n        i = 0;\n        while (i < targetTouches.length) {\n            targetIds[targetTouches[i].identifier] = true;\n            i++;\n        }\n    }\n\n    // filter changed touches to only contain touches that exist in the collected target ids\n    i = 0;\n    while (i < changedTouches.length) {\n        if (targetIds[changedTouches[i].identifier]) {\n            changedTargetTouches.push(changedTouches[i]);\n        }\n\n        // cleanup removed touches\n        if (type & (INPUT_END | INPUT_CANCEL)) {\n            delete targetIds[changedTouches[i].identifier];\n        }\n        i++;\n    }\n\n    if (!changedTargetTouches.length) {\n        return;\n    }\n\n    return [\n        // merge targetTouches with changedTargetTouches so it contains ALL touches, including 'end' and 'cancel'\n        uniqueArray(targetTouches.concat(changedTargetTouches), 'identifier', true),\n        changedTargetTouches\n    ];\n}\n\n/**\n * Combined touch and mouse input\n *\n * Touch has a higher priority then mouse, and while touching no mouse events are allowed.\n * This because touch devices also emit mouse events while doing a touch.\n *\n * @constructor\n * @extends Input\n */\n\nvar DEDUP_TIMEOUT = 2500;\nvar DEDUP_DISTANCE = 25;\n\nfunction TouchMouseInput() {\n    Input.apply(this, arguments);\n\n    var handler = bindFn(this.handler, this);\n    this.touch = new TouchInput(this.manager, handler);\n    this.mouse = new MouseInput(this.manager, handler);\n\n    this.primaryTouch = null;\n    this.lastTouches = [];\n}\n\ninherit(TouchMouseInput, Input, {\n    /**\n     * handle mouse and touch events\n     * @param {Hammer} manager\n     * @param {String} inputEvent\n     * @param {Object} inputData\n     */\n    handler: function TMEhandler(manager, inputEvent, inputData) {\n        var isTouch = (inputData.pointerType == INPUT_TYPE_TOUCH),\n            isMouse = (inputData.pointerType == INPUT_TYPE_MOUSE);\n\n        if (isMouse && inputData.sourceCapabilities && inputData.sourceCapabilities.firesTouchEvents) {\n            return;\n        }\n\n        // when we're in a touch event, record touches to  de-dupe synthetic mouse event\n        if (isTouch) {\n            recordTouches.call(this, inputEvent, inputData);\n        } else if (isMouse && isSyntheticEvent.call(this, inputData)) {\n            return;\n        }\n\n        this.callback(manager, inputEvent, inputData);\n    },\n\n    /**\n     * remove the event listeners\n     */\n    destroy: function destroy() {\n        this.touch.destroy();\n        this.mouse.destroy();\n    }\n});\n\nfunction recordTouches(eventType, eventData) {\n    if (eventType & INPUT_START) {\n        this.primaryTouch = eventData.changedPointers[0].identifier;\n        setLastTouch.call(this, eventData);\n    } else if (eventType & (INPUT_END | INPUT_CANCEL)) {\n        setLastTouch.call(this, eventData);\n    }\n}\n\nfunction setLastTouch(eventData) {\n    var touch = eventData.changedPointers[0];\n\n    if (touch.identifier === this.primaryTouch) {\n        var lastTouch = {x: touch.clientX, y: touch.clientY};\n        this.lastTouches.push(lastTouch);\n        var lts = this.lastTouches;\n        var removeLastTouch = function() {\n            var i = lts.indexOf(lastTouch);\n            if (i > -1) {\n                lts.splice(i, 1);\n            }\n        };\n        setTimeout(removeLastTouch, DEDUP_TIMEOUT);\n    }\n}\n\nfunction isSyntheticEvent(eventData) {\n    var x = eventData.srcEvent.clientX, y = eventData.srcEvent.clientY;\n    for (var i = 0; i < this.lastTouches.length; i++) {\n        var t = this.lastTouches[i];\n        var dx = Math.abs(x - t.x), dy = Math.abs(y - t.y);\n        if (dx <= DEDUP_DISTANCE && dy <= DEDUP_DISTANCE) {\n            return true;\n        }\n    }\n    return false;\n}\n\nvar PREFIXED_TOUCH_ACTION = prefixed(TEST_ELEMENT.style, 'touchAction');\nvar NATIVE_TOUCH_ACTION = PREFIXED_TOUCH_ACTION !== undefined;\n\n// magical touchAction value\nvar TOUCH_ACTION_COMPUTE = 'compute';\nvar TOUCH_ACTION_AUTO = 'auto';\nvar TOUCH_ACTION_MANIPULATION = 'manipulation'; // not implemented\nvar TOUCH_ACTION_NONE = 'none';\nvar TOUCH_ACTION_PAN_X = 'pan-x';\nvar TOUCH_ACTION_PAN_Y = 'pan-y';\nvar TOUCH_ACTION_MAP = getTouchActionProps();\n\n/**\n * Touch Action\n * sets the touchAction property or uses the js alternative\n * @param {Manager} manager\n * @param {String} value\n * @constructor\n */\nfunction TouchAction(manager, value) {\n    this.manager = manager;\n    this.set(value);\n}\n\nTouchAction.prototype = {\n    /**\n     * set the touchAction value on the element or enable the polyfill\n     * @param {String} value\n     */\n    set: function(value) {\n        // find out the touch-action by the event handlers\n        if (value == TOUCH_ACTION_COMPUTE) {\n            value = this.compute();\n        }\n\n        if (NATIVE_TOUCH_ACTION && this.manager.element.style && TOUCH_ACTION_MAP[value]) {\n            this.manager.element.style[PREFIXED_TOUCH_ACTION] = value;\n        }\n        this.actions = value.toLowerCase().trim();\n    },\n\n    /**\n     * just re-set the touchAction value\n     */\n    update: function() {\n        this.set(this.manager.options.touchAction);\n    },\n\n    /**\n     * compute the value for the touchAction property based on the recognizer's settings\n     * @returns {String} value\n     */\n    compute: function() {\n        var actions = [];\n        each(this.manager.recognizers, function(recognizer) {\n            if (boolOrFn(recognizer.options.enable, [recognizer])) {\n                actions = actions.concat(recognizer.getTouchAction());\n            }\n        });\n        return cleanTouchActions(actions.join(' '));\n    },\n\n    /**\n     * this method is called on each input cycle and provides the preventing of the browser behavior\n     * @param {Object} input\n     */\n    preventDefaults: function(input) {\n        var srcEvent = input.srcEvent;\n        var direction = input.offsetDirection;\n\n        // if the touch action did prevented once this session\n        if (this.manager.session.prevented) {\n            srcEvent.preventDefault();\n            return;\n        }\n\n        var actions = this.actions;\n        var hasNone = inStr(actions, TOUCH_ACTION_NONE) && !TOUCH_ACTION_MAP[TOUCH_ACTION_NONE];\n        var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_Y];\n        var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_X];\n\n        if (hasNone) {\n            //do not prevent defaults if this is a tap gesture\n\n            var isTapPointer = input.pointers.length === 1;\n            var isTapMovement = input.distance < 2;\n            var isTapTouchTime = input.deltaTime < 250;\n\n            if (isTapPointer && isTapMovement && isTapTouchTime) {\n                return;\n            }\n        }\n\n        if (hasPanX && hasPanY) {\n            // `pan-x pan-y` means browser handles all scrolling/panning, do not prevent\n            return;\n        }\n\n        if (hasNone ||\n            (hasPanY && direction & DIRECTION_HORIZONTAL) ||\n            (hasPanX && direction & DIRECTION_VERTICAL)) {\n            return this.preventSrc(srcEvent);\n        }\n    },\n\n    /**\n     * call preventDefault to prevent the browser's default behavior (scrolling in most cases)\n     * @param {Object} srcEvent\n     */\n    preventSrc: function(srcEvent) {\n        this.manager.session.prevented = true;\n        srcEvent.preventDefault();\n    }\n};\n\n/**\n * when the touchActions are collected they are not a valid value, so we need to clean things up. *\n * @param {String} actions\n * @returns {*}\n */\nfunction cleanTouchActions(actions) {\n    // none\n    if (inStr(actions, TOUCH_ACTION_NONE)) {\n        return TOUCH_ACTION_NONE;\n    }\n\n    var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X);\n    var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y);\n\n    // if both pan-x and pan-y are set (different recognizers\n    // for different directions, e.g. horizontal pan but vertical swipe?)\n    // we need none (as otherwise with pan-x pan-y combined none of these\n    // recognizers will work, since the browser would handle all panning\n    if (hasPanX && hasPanY) {\n        return TOUCH_ACTION_NONE;\n    }\n\n    // pan-x OR pan-y\n    if (hasPanX || hasPanY) {\n        return hasPanX ? TOUCH_ACTION_PAN_X : TOUCH_ACTION_PAN_Y;\n    }\n\n    // manipulation\n    if (inStr(actions, TOUCH_ACTION_MANIPULATION)) {\n        return TOUCH_ACTION_MANIPULATION;\n    }\n\n    return TOUCH_ACTION_AUTO;\n}\n\nfunction getTouchActionProps() {\n    if (!NATIVE_TOUCH_ACTION) {\n        return false;\n    }\n    var touchMap = {};\n    var cssSupports = window.CSS && window.CSS.supports;\n    ['auto', 'manipulation', 'pan-y', 'pan-x', 'pan-x pan-y', 'none'].forEach(function(val) {\n\n        // If css.supports is not supported but there is native touch-action assume it supports\n        // all values. This is the case for IE 10 and 11.\n        touchMap[val] = cssSupports ? window.CSS.supports('touch-action', val) : true;\n    });\n    return touchMap;\n}\n\n/**\n * Recognizer flow explained; *\n * All recognizers have the initial state of POSSIBLE when a input session starts.\n * The definition of a input session is from the first input until the last input, with all it's movement in it. *\n * Example session for mouse-input: mousedown -> mousemove -> mouseup\n *\n * On each recognizing cycle (see Manager.recognize) the .recognize() method is executed\n * which determines with state it should be.\n *\n * If the recognizer has the state FAILED, CANCELLED or RECOGNIZED (equals ENDED), it is reset to\n * POSSIBLE to give it another change on the next cycle.\n *\n *               Possible\n *                  |\n *            +-----+---------------+\n *            |                     |\n *      +-----+-----+               |\n *      |           |               |\n *   Failed      Cancelled          |\n *                          +-------+------+\n *                          |              |\n *                      Recognized       Began\n *                                         |\n *                                      Changed\n *                                         |\n *                                  Ended/Recognized\n */\nvar STATE_POSSIBLE = 1;\nvar STATE_BEGAN = 2;\nvar STATE_CHANGED = 4;\nvar STATE_ENDED = 8;\nvar STATE_RECOGNIZED = STATE_ENDED;\nvar STATE_CANCELLED = 16;\nvar STATE_FAILED = 32;\n\n/**\n * Recognizer\n * Every recognizer needs to extend from this class.\n * @constructor\n * @param {Object} options\n */\nfunction Recognizer(options) {\n    this.options = assign({}, this.defaults, options || {});\n\n    this.id = uniqueId();\n\n    this.manager = null;\n\n    // default is enable true\n    this.options.enable = ifUndefined(this.options.enable, true);\n\n    this.state = STATE_POSSIBLE;\n\n    this.simultaneous = {};\n    this.requireFail = [];\n}\n\nRecognizer.prototype = {\n    /**\n     * @virtual\n     * @type {Object}\n     */\n    defaults: {},\n\n    /**\n     * set options\n     * @param {Object} options\n     * @return {Recognizer}\n     */\n    set: function(options) {\n        assign(this.options, options);\n\n        // also update the touchAction, in case something changed about the directions/enabled state\n        this.manager && this.manager.touchAction.update();\n        return this;\n    },\n\n    /**\n     * recognize simultaneous with an other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    recognizeWith: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'recognizeWith', this)) {\n            return this;\n        }\n\n        var simultaneous = this.simultaneous;\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        if (!simultaneous[otherRecognizer.id]) {\n            simultaneous[otherRecognizer.id] = otherRecognizer;\n            otherRecognizer.recognizeWith(this);\n        }\n        return this;\n    },\n\n    /**\n     * drop the simultaneous link. it doesnt remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    dropRecognizeWith: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'dropRecognizeWith', this)) {\n            return this;\n        }\n\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        delete this.simultaneous[otherRecognizer.id];\n        return this;\n    },\n\n    /**\n     * recognizer can only run when an other is failing\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    requireFailure: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'requireFailure', this)) {\n            return this;\n        }\n\n        var requireFail = this.requireFail;\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        if (inArray(requireFail, otherRecognizer) === -1) {\n            requireFail.push(otherRecognizer);\n            otherRecognizer.requireFailure(this);\n        }\n        return this;\n    },\n\n    /**\n     * drop the requireFailure link. it does not remove the link on the other recognizer.\n     * @param {Recognizer} otherRecognizer\n     * @returns {Recognizer} this\n     */\n    dropRequireFailure: function(otherRecognizer) {\n        if (invokeArrayArg(otherRecognizer, 'dropRequireFailure', this)) {\n            return this;\n        }\n\n        otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);\n        var index = inArray(this.requireFail, otherRecognizer);\n        if (index > -1) {\n            this.requireFail.splice(index, 1);\n        }\n        return this;\n    },\n\n    /**\n     * has require failures boolean\n     * @returns {boolean}\n     */\n    hasRequireFailures: function() {\n        return this.requireFail.length > 0;\n    },\n\n    /**\n     * if the recognizer can recognize simultaneous with an other recognizer\n     * @param {Recognizer} otherRecognizer\n     * @returns {Boolean}\n     */\n    canRecognizeWith: function(otherRecognizer) {\n        return !!this.simultaneous[otherRecognizer.id];\n    },\n\n    /**\n     * You should use `tryEmit` instead of `emit` directly to check\n     * that all the needed recognizers has failed before emitting.\n     * @param {Object} input\n     */\n    emit: function(input) {\n        var self = this;\n        var state = this.state;\n\n        function emit(event) {\n            self.manager.emit(event, input);\n        }\n\n        // 'panstart' and 'panmove'\n        if (state < STATE_ENDED) {\n            emit(self.options.event + stateStr(state));\n        }\n\n        emit(self.options.event); // simple 'eventName' events\n\n        if (input.additionalEvent) { // additional event(panleft, panright, pinchin, pinchout...)\n            emit(input.additionalEvent);\n        }\n\n        // panend and pancancel\n        if (state >= STATE_ENDED) {\n            emit(self.options.event + stateStr(state));\n        }\n    },\n\n    /**\n     * Check that all the require failure recognizers has failed,\n     * if true, it emits a gesture event,\n     * otherwise, setup the state to FAILED.\n     * @param {Object} input\n     */\n    tryEmit: function(input) {\n        if (this.canEmit()) {\n            return this.emit(input);\n        }\n        // it's failing anyway\n        this.state = STATE_FAILED;\n    },\n\n    /**\n     * can we emit?\n     * @returns {boolean}\n     */\n    canEmit: function() {\n        var i = 0;\n        while (i < this.requireFail.length) {\n            if (!(this.requireFail[i].state & (STATE_FAILED | STATE_POSSIBLE))) {\n                return false;\n            }\n            i++;\n        }\n        return true;\n    },\n\n    /**\n     * update the recognizer\n     * @param {Object} inputData\n     */\n    recognize: function(inputData) {\n        // make a new copy of the inputData\n        // so we can change the inputData without messing up the other recognizers\n        var inputDataClone = assign({}, inputData);\n\n        // is is enabled and allow recognizing?\n        if (!boolOrFn(this.options.enable, [this, inputDataClone])) {\n            this.reset();\n            this.state = STATE_FAILED;\n            return;\n        }\n\n        // reset when we've reached the end\n        if (this.state & (STATE_RECOGNIZED | STATE_CANCELLED | STATE_FAILED)) {\n            this.state = STATE_POSSIBLE;\n        }\n\n        this.state = this.process(inputDataClone);\n\n        // the recognizer has recognized a gesture\n        // so trigger an event\n        if (this.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED | STATE_CANCELLED)) {\n            this.tryEmit(inputDataClone);\n        }\n    },\n\n    /**\n     * return the state of the recognizer\n     * the actual recognizing happens in this method\n     * @virtual\n     * @param {Object} inputData\n     * @returns {Const} STATE\n     */\n    process: function(inputData) { }, // jshint ignore:line\n\n    /**\n     * return the preferred touch-action\n     * @virtual\n     * @returns {Array}\n     */\n    getTouchAction: function() { },\n\n    /**\n     * called when the gesture isn't allowed to recognize\n     * like when another is being recognized or it is disabled\n     * @virtual\n     */\n    reset: function() { }\n};\n\n/**\n * get a usable string, used as event postfix\n * @param {Const} state\n * @returns {String} state\n */\nfunction stateStr(state) {\n    if (state & STATE_CANCELLED) {\n        return 'cancel';\n    } else if (state & STATE_ENDED) {\n        return 'end';\n    } else if (state & STATE_CHANGED) {\n        return 'move';\n    } else if (state & STATE_BEGAN) {\n        return 'start';\n    }\n    return '';\n}\n\n/**\n * direction cons to string\n * @param {Const} direction\n * @returns {String}\n */\nfunction directionStr(direction) {\n    if (direction == DIRECTION_DOWN) {\n        return 'down';\n    } else if (direction == DIRECTION_UP) {\n        return 'up';\n    } else if (direction == DIRECTION_LEFT) {\n        return 'left';\n    } else if (direction == DIRECTION_RIGHT) {\n        return 'right';\n    }\n    return '';\n}\n\n/**\n * get a recognizer by name if it is bound to a manager\n * @param {Recognizer|String} otherRecognizer\n * @param {Recognizer} recognizer\n * @returns {Recognizer}\n */\nfunction getRecognizerByNameIfManager(otherRecognizer, recognizer) {\n    var manager = recognizer.manager;\n    if (manager) {\n        return manager.get(otherRecognizer);\n    }\n    return otherRecognizer;\n}\n\n/**\n * This recognizer is just used as a base for the simple attribute recognizers.\n * @constructor\n * @extends Recognizer\n */\nfunction AttrRecognizer() {\n    Recognizer.apply(this, arguments);\n}\n\ninherit(AttrRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof AttrRecognizer\n     */\n    defaults: {\n        /**\n         * @type {Number}\n         * @default 1\n         */\n        pointers: 1\n    },\n\n    /**\n     * Used to check if it the recognizer receives valid input, like input.distance > 10.\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {Boolean} recognized\n     */\n    attrTest: function(input) {\n        var optionPointers = this.options.pointers;\n        return optionPointers === 0 || input.pointers.length === optionPointers;\n    },\n\n    /**\n     * Process the input and return the state for the recognizer\n     * @memberof AttrRecognizer\n     * @param {Object} input\n     * @returns {*} State\n     */\n    process: function(input) {\n        var state = this.state;\n        var eventType = input.eventType;\n\n        var isRecognized = state & (STATE_BEGAN | STATE_CHANGED);\n        var isValid = this.attrTest(input);\n\n        // on cancel input and we've recognized before, return STATE_CANCELLED\n        if (isRecognized && (eventType & INPUT_CANCEL || !isValid)) {\n            return state | STATE_CANCELLED;\n        } else if (isRecognized || isValid) {\n            if (eventType & INPUT_END) {\n                return state | STATE_ENDED;\n            } else if (!(state & STATE_BEGAN)) {\n                return STATE_BEGAN;\n            }\n            return state | STATE_CHANGED;\n        }\n        return STATE_FAILED;\n    }\n});\n\n/**\n * Pan\n * Recognized when the pointer is down and moved in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nfunction PanRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n\n    this.pX = null;\n    this.pY = null;\n}\n\ninherit(PanRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof PanRecognizer\n     */\n    defaults: {\n        event: 'pan',\n        threshold: 10,\n        pointers: 1,\n        direction: DIRECTION_ALL\n    },\n\n    getTouchAction: function() {\n        var direction = this.options.direction;\n        var actions = [];\n        if (direction & DIRECTION_HORIZONTAL) {\n            actions.push(TOUCH_ACTION_PAN_Y);\n        }\n        if (direction & DIRECTION_VERTICAL) {\n            actions.push(TOUCH_ACTION_PAN_X);\n        }\n        return actions;\n    },\n\n    directionTest: function(input) {\n        var options = this.options;\n        var hasMoved = true;\n        var distance = input.distance;\n        var direction = input.direction;\n        var x = input.deltaX;\n        var y = input.deltaY;\n\n        // lock to axis?\n        if (!(direction & options.direction)) {\n            if (options.direction & DIRECTION_HORIZONTAL) {\n                direction = (x === 0) ? DIRECTION_NONE : (x < 0) ? DIRECTION_LEFT : DIRECTION_RIGHT;\n                hasMoved = x != this.pX;\n                distance = Math.abs(input.deltaX);\n            } else {\n                direction = (y === 0) ? DIRECTION_NONE : (y < 0) ? DIRECTION_UP : DIRECTION_DOWN;\n                hasMoved = y != this.pY;\n                distance = Math.abs(input.deltaY);\n            }\n        }\n        input.direction = direction;\n        return hasMoved && distance > options.threshold && direction & options.direction;\n    },\n\n    attrTest: function(input) {\n        return AttrRecognizer.prototype.attrTest.call(this, input) &&\n            (this.state & STATE_BEGAN || (!(this.state & STATE_BEGAN) && this.directionTest(input)));\n    },\n\n    emit: function(input) {\n\n        this.pX = input.deltaX;\n        this.pY = input.deltaY;\n\n        var direction = directionStr(input.direction);\n\n        if (direction) {\n            input.additionalEvent = this.options.event + direction;\n        }\n        this._super.emit.call(this, input);\n    }\n});\n\n/**\n * Pinch\n * Recognized when two or more pointers are moving toward (zoom-in) or away from each other (zoom-out).\n * @constructor\n * @extends AttrRecognizer\n */\nfunction PinchRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n}\n\ninherit(PinchRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof PinchRecognizer\n     */\n    defaults: {\n        event: 'pinch',\n        threshold: 0,\n        pointers: 2\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_NONE];\n    },\n\n    attrTest: function(input) {\n        return this._super.attrTest.call(this, input) &&\n            (Math.abs(input.scale - 1) > this.options.threshold || this.state & STATE_BEGAN);\n    },\n\n    emit: function(input) {\n        if (input.scale !== 1) {\n            var inOut = input.scale < 1 ? 'in' : 'out';\n            input.additionalEvent = this.options.event + inOut;\n        }\n        this._super.emit.call(this, input);\n    }\n});\n\n/**\n * Press\n * Recognized when the pointer is down for x ms without any movement.\n * @constructor\n * @extends Recognizer\n */\nfunction PressRecognizer() {\n    Recognizer.apply(this, arguments);\n\n    this._timer = null;\n    this._input = null;\n}\n\ninherit(PressRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof PressRecognizer\n     */\n    defaults: {\n        event: 'press',\n        pointers: 1,\n        time: 251, // minimal time of the pointer to be pressed\n        threshold: 9 // a minimal movement is ok, but keep it low\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_AUTO];\n    },\n\n    process: function(input) {\n        var options = this.options;\n        var validPointers = input.pointers.length === options.pointers;\n        var validMovement = input.distance < options.threshold;\n        var validTime = input.deltaTime > options.time;\n\n        this._input = input;\n\n        // we only allow little movement\n        // and we've reached an end event, so a tap is possible\n        if (!validMovement || !validPointers || (input.eventType & (INPUT_END | INPUT_CANCEL) && !validTime)) {\n            this.reset();\n        } else if (input.eventType & INPUT_START) {\n            this.reset();\n            this._timer = setTimeoutContext(function() {\n                this.state = STATE_RECOGNIZED;\n                this.tryEmit();\n            }, options.time, this);\n        } else if (input.eventType & INPUT_END) {\n            return STATE_RECOGNIZED;\n        }\n        return STATE_FAILED;\n    },\n\n    reset: function() {\n        clearTimeout(this._timer);\n    },\n\n    emit: function(input) {\n        if (this.state !== STATE_RECOGNIZED) {\n            return;\n        }\n\n        if (input && (input.eventType & INPUT_END)) {\n            this.manager.emit(this.options.event + 'up', input);\n        } else {\n            this._input.timeStamp = now();\n            this.manager.emit(this.options.event, this._input);\n        }\n    }\n});\n\n/**\n * Rotate\n * Recognized when two or more pointer are moving in a circular motion.\n * @constructor\n * @extends AttrRecognizer\n */\nfunction RotateRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n}\n\ninherit(RotateRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof RotateRecognizer\n     */\n    defaults: {\n        event: 'rotate',\n        threshold: 0,\n        pointers: 2\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_NONE];\n    },\n\n    attrTest: function(input) {\n        return this._super.attrTest.call(this, input) &&\n            (Math.abs(input.rotation) > this.options.threshold || this.state & STATE_BEGAN);\n    }\n});\n\n/**\n * Swipe\n * Recognized when the pointer is moving fast (velocity), with enough distance in the allowed direction.\n * @constructor\n * @extends AttrRecognizer\n */\nfunction SwipeRecognizer() {\n    AttrRecognizer.apply(this, arguments);\n}\n\ninherit(SwipeRecognizer, AttrRecognizer, {\n    /**\n     * @namespace\n     * @memberof SwipeRecognizer\n     */\n    defaults: {\n        event: 'swipe',\n        threshold: 10,\n        velocity: 0.3,\n        direction: DIRECTION_HORIZONTAL | DIRECTION_VERTICAL,\n        pointers: 1\n    },\n\n    getTouchAction: function() {\n        return PanRecognizer.prototype.getTouchAction.call(this);\n    },\n\n    attrTest: function(input) {\n        var direction = this.options.direction;\n        var velocity;\n\n        if (direction & (DIRECTION_HORIZONTAL | DIRECTION_VERTICAL)) {\n            velocity = input.overallVelocity;\n        } else if (direction & DIRECTION_HORIZONTAL) {\n            velocity = input.overallVelocityX;\n        } else if (direction & DIRECTION_VERTICAL) {\n            velocity = input.overallVelocityY;\n        }\n\n        return this._super.attrTest.call(this, input) &&\n            direction & input.offsetDirection &&\n            input.distance > this.options.threshold &&\n            input.maxPointers == this.options.pointers &&\n            abs(velocity) > this.options.velocity && input.eventType & INPUT_END;\n    },\n\n    emit: function(input) {\n        var direction = directionStr(input.offsetDirection);\n        if (direction) {\n            this.manager.emit(this.options.event + direction, input);\n        }\n\n        this.manager.emit(this.options.event, input);\n    }\n});\n\n/**\n * A tap is ecognized when the pointer is doing a small tap/click. Multiple taps are recognized if they occur\n * between the given interval and position. The delay option can be used to recognize multi-taps without firing\n * a single tap.\n *\n * The eventData from the emitted event contains the property `tapCount`, which contains the amount of\n * multi-taps being recognized.\n * @constructor\n * @extends Recognizer\n */\nfunction TapRecognizer() {\n    Recognizer.apply(this, arguments);\n\n    // previous time and center,\n    // used for tap counting\n    this.pTime = false;\n    this.pCenter = false;\n\n    this._timer = null;\n    this._input = null;\n    this.count = 0;\n}\n\ninherit(TapRecognizer, Recognizer, {\n    /**\n     * @namespace\n     * @memberof PinchRecognizer\n     */\n    defaults: {\n        event: 'tap',\n        pointers: 1,\n        taps: 1,\n        interval: 300, // max time between the multi-tap taps\n        time: 250, // max time of the pointer to be down (like finger on the screen)\n        threshold: 9, // a minimal movement is ok, but keep it low\n        posThreshold: 10 // a multi-tap can be a bit off the initial position\n    },\n\n    getTouchAction: function() {\n        return [TOUCH_ACTION_MANIPULATION];\n    },\n\n    process: function(input) {\n        var options = this.options;\n\n        var validPointers = input.pointers.length === options.pointers;\n        var validMovement = input.distance < options.threshold;\n        var validTouchTime = input.deltaTime < options.time;\n\n        this.reset();\n\n        if ((input.eventType & INPUT_START) && (this.count === 0)) {\n            return this.failTimeout();\n        }\n\n        // we only allow little movement\n        // and we've reached an end event, so a tap is possible\n        if (validMovement && validTouchTime && validPointers) {\n            if (input.eventType != INPUT_END) {\n                return this.failTimeout();\n            }\n\n            var validInterval = this.pTime ? (input.timeStamp - this.pTime < options.interval) : true;\n            var validMultiTap = !this.pCenter || getDistance(this.pCenter, input.center) < options.posThreshold;\n\n            this.pTime = input.timeStamp;\n            this.pCenter = input.center;\n\n            if (!validMultiTap || !validInterval) {\n                this.count = 1;\n            } else {\n                this.count += 1;\n            }\n\n            this._input = input;\n\n            // if tap count matches we have recognized it,\n            // else it has began recognizing...\n            var tapCount = this.count % options.taps;\n            if (tapCount === 0) {\n                // no failing requirements, immediately trigger the tap event\n                // or wait as long as the multitap interval to trigger\n                if (!this.hasRequireFailures()) {\n                    return STATE_RECOGNIZED;\n                } else {\n                    this._timer = setTimeoutContext(function() {\n                        this.state = STATE_RECOGNIZED;\n                        this.tryEmit();\n                    }, options.interval, this);\n                    return STATE_BEGAN;\n                }\n            }\n        }\n        return STATE_FAILED;\n    },\n\n    failTimeout: function() {\n        this._timer = setTimeoutContext(function() {\n            this.state = STATE_FAILED;\n        }, this.options.interval, this);\n        return STATE_FAILED;\n    },\n\n    reset: function() {\n        clearTimeout(this._timer);\n    },\n\n    emit: function() {\n        if (this.state == STATE_RECOGNIZED) {\n            this._input.tapCount = this.count;\n            this.manager.emit(this.options.event, this._input);\n        }\n    }\n});\n\n/**\n * Simple way to create a manager with a default set of recognizers.\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nfunction Hammer(element, options) {\n    options = options || {};\n    options.recognizers = ifUndefined(options.recognizers, Hammer.defaults.preset);\n    return new Manager(element, options);\n}\n\n/**\n * @const {string}\n */\nHammer.VERSION = '2.0.7';\n\n/**\n * default settings\n * @namespace\n */\nHammer.defaults = {\n    /**\n     * set if DOM events are being triggered.\n     * But this is slower and unused by simple implementations, so disabled by default.\n     * @type {Boolean}\n     * @default false\n     */\n    domEvents: false,\n\n    /**\n     * The value for the touchAction property/fallback.\n     * When set to `compute` it will magically set the correct value based on the added recognizers.\n     * @type {String}\n     * @default compute\n     */\n    touchAction: TOUCH_ACTION_COMPUTE,\n\n    /**\n     * @type {Boolean}\n     * @default true\n     */\n    enable: true,\n\n    /**\n     * EXPERIMENTAL FEATURE -- can be removed/changed\n     * Change the parent input target element.\n     * If Null, then it is being set the to main element.\n     * @type {Null|EventTarget}\n     * @default null\n     */\n    inputTarget: null,\n\n    /**\n     * force an input class\n     * @type {Null|Function}\n     * @default null\n     */\n    inputClass: null,\n\n    /**\n     * Default recognizer setup when calling `Hammer()`\n     * When creating a new Manager these will be skipped.\n     * @type {Array}\n     */\n    preset: [\n        // RecognizerClass, options, [recognizeWith, ...], [requireFailure, ...]\n        [RotateRecognizer, {enable: false}],\n        [PinchRecognizer, {enable: false}, ['rotate']],\n        [SwipeRecognizer, {direction: DIRECTION_HORIZONTAL}],\n        [PanRecognizer, {direction: DIRECTION_HORIZONTAL}, ['swipe']],\n        [TapRecognizer],\n        [TapRecognizer, {event: 'doubletap', taps: 2}, ['tap']],\n        [PressRecognizer]\n    ],\n\n    /**\n     * Some CSS properties can be used to improve the working of Hammer.\n     * Add them to this method and they will be set when creating a new Manager.\n     * @namespace\n     */\n    cssProps: {\n        /**\n         * Disables text selection to improve the dragging gesture. Mainly for desktop browsers.\n         * @type {String}\n         * @default 'none'\n         */\n        userSelect: 'none',\n\n        /**\n         * Disable the Windows Phone grippers when pressing an element.\n         * @type {String}\n         * @default 'none'\n         */\n        touchSelect: 'none',\n\n        /**\n         * Disables the default callout shown when you touch and hold a touch target.\n         * On iOS, when you touch and hold a touch target such as a link, Safari displays\n         * a callout containing information about the link. This property allows you to disable that callout.\n         * @type {String}\n         * @default 'none'\n         */\n        touchCallout: 'none',\n\n        /**\n         * Specifies whether zooming is enabled. Used by IE10>\n         * @type {String}\n         * @default 'none'\n         */\n        contentZooming: 'none',\n\n        /**\n         * Specifies that an entire element should be draggable instead of its contents. Mainly for desktop browsers.\n         * @type {String}\n         * @default 'none'\n         */\n        userDrag: 'none',\n\n        /**\n         * Overrides the highlight color shown when the user taps a link or a JavaScript\n         * clickable element in iOS. This property obeys the alpha value, if specified.\n         * @type {String}\n         * @default 'rgba(0,0,0,0)'\n         */\n        tapHighlightColor: 'rgba(0,0,0,0)'\n    }\n};\n\nvar STOP = 1;\nvar FORCED_STOP = 2;\n\n/**\n * Manager\n * @param {HTMLElement} element\n * @param {Object} [options]\n * @constructor\n */\nfunction Manager(element, options) {\n    this.options = assign({}, Hammer.defaults, options || {});\n\n    this.options.inputTarget = this.options.inputTarget || element;\n\n    this.handlers = {};\n    this.session = {};\n    this.recognizers = [];\n    this.oldCssProps = {};\n\n    this.element = element;\n    this.input = createInputInstance(this);\n    this.touchAction = new TouchAction(this, this.options.touchAction);\n\n    toggleCssProps(this, true);\n\n    each(this.options.recognizers, function(item) {\n        var recognizer = this.add(new (item[0])(item[1]));\n        item[2] && recognizer.recognizeWith(item[2]);\n        item[3] && recognizer.requireFailure(item[3]);\n    }, this);\n}\n\nManager.prototype = {\n    /**\n     * set options\n     * @param {Object} options\n     * @returns {Manager}\n     */\n    set: function(options) {\n        assign(this.options, options);\n\n        // Options that need a little more setup\n        if (options.touchAction) {\n            this.touchAction.update();\n        }\n        if (options.inputTarget) {\n            // Clean up existing event listeners and reinitialize\n            this.input.destroy();\n            this.input.target = options.inputTarget;\n            this.input.init();\n        }\n        return this;\n    },\n\n    /**\n     * stop recognizing for this session.\n     * This session will be discarded, when a new [input]start event is fired.\n     * When forced, the recognizer cycle is stopped immediately.\n     * @param {Boolean} [force]\n     */\n    stop: function(force) {\n        this.session.stopped = force ? FORCED_STOP : STOP;\n    },\n\n    /**\n     * run the recognizers!\n     * called by the inputHandler function on every movement of the pointers (touches)\n     * it walks through all the recognizers and tries to detect the gesture that is being made\n     * @param {Object} inputData\n     */\n    recognize: function(inputData) {\n        var session = this.session;\n        if (session.stopped) {\n            return;\n        }\n\n        // run the touch-action polyfill\n        this.touchAction.preventDefaults(inputData);\n\n        var recognizer;\n        var recognizers = this.recognizers;\n\n        // this holds the recognizer that is being recognized.\n        // so the recognizer's state needs to be BEGAN, CHANGED, ENDED or RECOGNIZED\n        // if no recognizer is detecting a thing, it is set to `null`\n        var curRecognizer = session.curRecognizer;\n\n        // reset when the last recognizer is recognized\n        // or when we're in a new session\n        if (!curRecognizer || (curRecognizer && curRecognizer.state & STATE_RECOGNIZED)) {\n            curRecognizer = session.curRecognizer = null;\n        }\n\n        var i = 0;\n        while (i < recognizers.length) {\n            recognizer = recognizers[i];\n\n            // find out if we are allowed try to recognize the input for this one.\n            // 1.   allow if the session is NOT forced stopped (see the .stop() method)\n            // 2.   allow if we still haven't recognized a gesture in this session, or the this recognizer is the one\n            //      that is being recognized.\n            // 3.   allow if the recognizer is allowed to run simultaneous with the current recognized recognizer.\n            //      this can be setup with the `recognizeWith()` method on the recognizer.\n            if (session.stopped !== FORCED_STOP && ( // 1\n                    !curRecognizer || recognizer == curRecognizer || // 2\n                    recognizer.canRecognizeWith(curRecognizer))) { // 3\n                recognizer.recognize(inputData);\n            } else {\n                recognizer.reset();\n            }\n\n            // if the recognizer has been recognizing the input as a valid gesture, we want to store this one as the\n            // current active recognizer. but only if we don't already have an active recognizer\n            if (!curRecognizer && recognizer.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED)) {\n                curRecognizer = session.curRecognizer = recognizer;\n            }\n            i++;\n        }\n    },\n\n    /**\n     * get a recognizer by its event name.\n     * @param {Recognizer|String} recognizer\n     * @returns {Recognizer|Null}\n     */\n    get: function(recognizer) {\n        if (recognizer instanceof Recognizer) {\n            return recognizer;\n        }\n\n        var recognizers = this.recognizers;\n        for (var i = 0; i < recognizers.length; i++) {\n            if (recognizers[i].options.event == recognizer) {\n                return recognizers[i];\n            }\n        }\n        return null;\n    },\n\n    /**\n     * add a recognizer to the manager\n     * existing recognizers with the same event name will be removed\n     * @param {Recognizer} recognizer\n     * @returns {Recognizer|Manager}\n     */\n    add: function(recognizer) {\n        if (invokeArrayArg(recognizer, 'add', this)) {\n            return this;\n        }\n\n        // remove existing\n        var existing = this.get(recognizer.options.event);\n        if (existing) {\n            this.remove(existing);\n        }\n\n        this.recognizers.push(recognizer);\n        recognizer.manager = this;\n\n        this.touchAction.update();\n        return recognizer;\n    },\n\n    /**\n     * remove a recognizer by name or instance\n     * @param {Recognizer|String} recognizer\n     * @returns {Manager}\n     */\n    remove: function(recognizer) {\n        if (invokeArrayArg(recognizer, 'remove', this)) {\n            return this;\n        }\n\n        recognizer = this.get(recognizer);\n\n        // let's make sure this recognizer exists\n        if (recognizer) {\n            var recognizers = this.recognizers;\n            var index = inArray(recognizers, recognizer);\n\n            if (index !== -1) {\n                recognizers.splice(index, 1);\n                this.touchAction.update();\n            }\n        }\n\n        return this;\n    },\n\n    /**\n     * bind event\n     * @param {String} events\n     * @param {Function} handler\n     * @returns {EventEmitter} this\n     */\n    on: function(events, handler) {\n        if (events === undefined) {\n            return;\n        }\n        if (handler === undefined) {\n            return;\n        }\n\n        var handlers = this.handlers;\n        each(splitStr(events), function(event) {\n            handlers[event] = handlers[event] || [];\n            handlers[event].push(handler);\n        });\n        return this;\n    },\n\n    /**\n     * unbind event, leave emit blank to remove all handlers\n     * @param {String} events\n     * @param {Function} [handler]\n     * @returns {EventEmitter} this\n     */\n    off: function(events, handler) {\n        if (events === undefined) {\n            return;\n        }\n\n        var handlers = this.handlers;\n        each(splitStr(events), function(event) {\n            if (!handler) {\n                delete handlers[event];\n            } else {\n                handlers[event] && handlers[event].splice(inArray(handlers[event], handler), 1);\n            }\n        });\n        return this;\n    },\n\n    /**\n     * emit event to the listeners\n     * @param {String} event\n     * @param {Object} data\n     */\n    emit: function(event, data) {\n        // we also want to trigger dom events\n        if (this.options.domEvents) {\n            triggerDomEvent(event, data);\n        }\n\n        // no handlers, so skip it all\n        var handlers = this.handlers[event] && this.handlers[event].slice();\n        if (!handlers || !handlers.length) {\n            return;\n        }\n\n        data.type = event;\n        data.preventDefault = function() {\n            data.srcEvent.preventDefault();\n        };\n\n        var i = 0;\n        while (i < handlers.length) {\n            handlers[i](data);\n            i++;\n        }\n    },\n\n    /**\n     * destroy the manager and unbinds all events\n     * it doesn't unbind dom events, that is the user own responsibility\n     */\n    destroy: function() {\n        this.element && toggleCssProps(this, false);\n\n        this.handlers = {};\n        this.session = {};\n        this.input.destroy();\n        this.element = null;\n    }\n};\n\n/**\n * add/remove the css properties as defined in manager.options.cssProps\n * @param {Manager} manager\n * @param {Boolean} add\n */\nfunction toggleCssProps(manager, add) {\n    var element = manager.element;\n    if (!element.style) {\n        return;\n    }\n    var prop;\n    each(manager.options.cssProps, function(value, name) {\n        prop = prefixed(element.style, name);\n        if (add) {\n            manager.oldCssProps[prop] = element.style[prop];\n            element.style[prop] = value;\n        } else {\n            element.style[prop] = manager.oldCssProps[prop] || '';\n        }\n    });\n    if (!add) {\n        manager.oldCssProps = {};\n    }\n}\n\n/**\n * trigger dom event\n * @param {String} event\n * @param {Object} data\n */\nfunction triggerDomEvent(event, data) {\n    var gestureEvent = document.createEvent('Event');\n    gestureEvent.initEvent(event, true, true);\n    gestureEvent.gesture = data;\n    data.target.dispatchEvent(gestureEvent);\n}\n\nassign(Hammer, {\n    INPUT_START: INPUT_START,\n    INPUT_MOVE: INPUT_MOVE,\n    INPUT_END: INPUT_END,\n    INPUT_CANCEL: INPUT_CANCEL,\n\n    STATE_POSSIBLE: STATE_POSSIBLE,\n    STATE_BEGAN: STATE_BEGAN,\n    STATE_CHANGED: STATE_CHANGED,\n    STATE_ENDED: STATE_ENDED,\n    STATE_RECOGNIZED: STATE_RECOGNIZED,\n    STATE_CANCELLED: STATE_CANCELLED,\n    STATE_FAILED: STATE_FAILED,\n\n    DIRECTION_NONE: DIRECTION_NONE,\n    DIRECTION_LEFT: DIRECTION_LEFT,\n    DIRECTION_RIGHT: DIRECTION_RIGHT,\n    DIRECTION_UP: DIRECTION_UP,\n    DIRECTION_DOWN: DIRECTION_DOWN,\n    DIRECTION_HORIZONTAL: DIRECTION_HORIZONTAL,\n    DIRECTION_VERTICAL: DIRECTION_VERTICAL,\n    DIRECTION_ALL: DIRECTION_ALL,\n\n    Manager: Manager,\n    Input: Input,\n    TouchAction: TouchAction,\n\n    TouchInput: TouchInput,\n    MouseInput: MouseInput,\n    PointerEventInput: PointerEventInput,\n    TouchMouseInput: TouchMouseInput,\n    SingleTouchInput: SingleTouchInput,\n\n    Recognizer: Recognizer,\n    AttrRecognizer: AttrRecognizer,\n    Tap: TapRecognizer,\n    Pan: PanRecognizer,\n    Swipe: SwipeRecognizer,\n    Pinch: PinchRecognizer,\n    Rotate: RotateRecognizer,\n    Press: PressRecognizer,\n\n    on: addEventListeners,\n    off: removeEventListeners,\n    each: each,\n    merge: merge,\n    extend: extend,\n    assign: assign,\n    inherit: inherit,\n    bindFn: bindFn,\n    prefixed: prefixed\n});\n\n// this prevents errors when Hammer is loaded in the presence of an AMD\n//  style loader but by script tag, not by the loader.\nvar freeGlobal = (typeof window !== 'undefined' ? window : (typeof self !== 'undefined' ? self : {})); // jshint ignore:line\nfreeGlobal.Hammer = Hammer;\n\nif (typeof define === 'function' && define.amd) {\n    define(function() {\n        return Hammer;\n    });\n} else if (typeof module != 'undefined' && module.exports) {\n    module.exports = Hammer;\n} else {\n    window[exportName] = Hammer;\n}\n\n})(window, document, 'Hammer');\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    {\n      on: {\n        click: function($event) {\n          $event.stopPropagation()\n          return _vm.closeLightBox($event)\n        }\n      }\n    },\n    [\n      _c(\n        \"transition\",\n        {\n          attrs: { mode: \"out-in\", name: \"vib-container-transition\" },\n          on: {\n            afterEnter: _vm.enableImageTransition,\n            beforeLeave: _vm.disableImageTransition\n          }\n        },\n        [\n          _vm.media && _vm.media.length > 0\n            ? _c(\n                \"div\",\n                {\n                  directives: [\n                    {\n                      name: \"show\",\n                      rawName: \"v-show\",\n                      value: _vm.lightBoxShown,\n                      expression: \"lightBoxShown\"\n                    }\n                  ],\n                  ref: \"container\",\n                  staticClass: \"vib-container\"\n                },\n                [\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"vib-content\",\n                      on: {\n                        click: function($event) {\n                          $event.stopPropagation()\n                        }\n                      }\n                    },\n                    [\n                      _c(\n                        \"transition\",\n                        {\n                          attrs: {\n                            mode: \"out-in\",\n                            name: _vm.imageTransitionName\n                          }\n                        },\n                        [\n                          _vm.currentMedia.type == undefined ||\n                          _vm.currentMedia.type == \"image\"\n                            ? _c(\"img\", {\n                                key: _vm.currentMedia.src,\n                                staticClass: \"vib-image\",\n                                attrs: {\n                                  src: _vm.currentMedia.src,\n                                  srcset: _vm.currentMedia.srcset || \"\",\n                                  alt: _vm.currentMedia.caption\n                                }\n                              })\n                            : _vm.media[_vm.select].type == \"youtube\"\n                            ? _c(\"div\", { staticClass: \"video-background\" }, [\n                                _c(\"iframe\", {\n                                  attrs: {\n                                    src:\n                                      \"https://www.youtube.com/embed/\" +\n                                      _vm.media[_vm.select].id +\n                                      \"?showinfo=0\",\n                                    width: \"560\",\n                                    height: \"315\",\n                                    frameborder: \"0\",\n                                    allowfullscreen: \"\"\n                                  }\n                                })\n                              ])\n                            : _vm.currentMedia.type == \"video\"\n                            ? _c(\n                                \"video\",\n                                {\n                                  key: _vm.currentMedia.sources[0].src,\n                                  ref: \"video\",\n                                  attrs: {\n                                    controls: \"\",\n                                    width: _vm.currentMedia.width,\n                                    height: _vm.currentMedia.height,\n                                    autoplay: _vm.currentMedia.autoplay\n                                  }\n                                },\n                                _vm._l(_vm.currentMedia.sources, function(\n                                  source\n                                ) {\n                                  return _c(\"source\", {\n                                    key: source.src,\n                                    attrs: {\n                                      src: source.src,\n                                      type: source.type\n                                    }\n                                  })\n                                }),\n                                0\n                              )\n                            : _vm._e()\n                        ]\n                      )\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _vm.showThumbs\n                    ? _c(\n                        \"div\",\n                        {\n                          staticClass: \"vib-thumbnail-wrapper vib-hideable\",\n                          class: { \"vib-hidden\": _vm.controlsHidden },\n                          on: {\n                            click: function($event) {\n                              $event.stopPropagation()\n                            },\n                            mouseover: function($event) {\n                              _vm.interfaceHovered = true\n                            },\n                            mouseleave: function($event) {\n                              _vm.interfaceHovered = false\n                            }\n                          }\n                        },\n                        _vm._l(_vm.imagesThumb, function(image, index) {\n                          return _c(\n                            \"div\",\n                            {\n                              directives: [\n                                {\n                                  name: \"show\",\n                                  rawName: \"v-show\",\n                                  value:\n                                    index >= _vm.thumbIndex.begin &&\n                                    index <= _vm.thumbIndex.end,\n                                  expression:\n                                    \"index >= thumbIndex.begin && index <= thumbIndex.end\"\n                                }\n                              ],\n                              key:\n                                typeof image.thumb === \"string\"\n                                  ? \"\" + image.thumb + index\n                                  : index,\n                              class:\n                                \"vib-thumbnail\" +\n                                (_vm.select === index ? \"-active\" : \"\"),\n                              style: {\n                                backgroundImage: \"url(\" + image.thumb + \")\"\n                              },\n                              on: {\n                                click: function($event) {\n                                  $event.stopPropagation()\n                                  return _vm.showImage(index)\n                                }\n                              }\n                            },\n                            [\n                              image.type == \"video\" || image.type == \"youtube\"\n                                ? _vm._t(\"videoIcon\", [_c(\"VideoIcon\")])\n                                : _vm._e()\n                            ],\n                            2\n                          )\n                        }),\n                        0\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _c(\n                    \"div\",\n                    {\n                      staticClass: \"vib-footer vib-hideable\",\n                      class: { \"vib-hidden\": _vm.controlsHidden },\n                      on: {\n                        mouseover: function($event) {\n                          _vm.interfaceHovered = true\n                        },\n                        mouseleave: function($event) {\n                          _vm.interfaceHovered = false\n                        }\n                      }\n                    },\n                    [\n                      _vm._t(\n                        \"customCaption\",\n                        [\n                          _c(\"div\", {\n                            directives: [\n                              {\n                                name: \"show\",\n                                rawName: \"v-show\",\n                                value: _vm.showCaption,\n                                expression: \"showCaption\"\n                              }\n                            ],\n                            domProps: {\n                              innerHTML: _vm._s(_vm.currentMedia.caption)\n                            }\n                          })\n                        ],\n                        { currentMedia: _vm.currentMedia }\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"div\",\n                        { staticClass: \"vib-footer-count\" },\n                        [\n                          _vm._t(\n                            \"footer\",\n                            [\n                              _vm._v(\n                                \"\\n            \" +\n                                  _vm._s(_vm.select + 1) +\n                                  \" / \" +\n                                  _vm._s(_vm.media.length) +\n                                  \"\\n          \"\n                              )\n                            ],\n                            { current: _vm.select + 1, total: _vm.media.length }\n                          )\n                        ],\n                        2\n                      )\n                    ],\n                    2\n                  ),\n                  _vm._v(\" \"),\n                  _vm.closable\n                    ? _c(\n                        \"button\",\n                        {\n                          staticClass: \"vib-close vib-hideable\",\n                          class: { \"vib-hidden\": _vm.controlsHidden },\n                          attrs: { type: \"button\", title: _vm.closeText },\n                          on: {\n                            mouseover: function($event) {\n                              _vm.interfaceHovered = true\n                            },\n                            mouseleave: function($event) {\n                              _vm.interfaceHovered = false\n                            }\n                          }\n                        },\n                        [_vm._t(\"close\", [_c(\"CloseIcon\")])],\n                        2\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.media.length > 1\n                    ? _c(\n                        \"button\",\n                        {\n                          staticClass: \"vib-arrow vib-arrow-left vib-hideable\",\n                          class: { \"vib-hidden\": _vm.controlsHidden },\n                          attrs: { type: \"button\", title: _vm.previousText },\n                          on: {\n                            click: function($event) {\n                              $event.stopPropagation()\n                              return _vm.previousImage()\n                            },\n                            mouseover: function($event) {\n                              _vm.interfaceHovered = true\n                            },\n                            mouseleave: function($event) {\n                              _vm.interfaceHovered = false\n                            }\n                          }\n                        },\n                        [_vm._t(\"previous\", [_c(\"LeftArrowIcon\")])],\n                        2\n                      )\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.media.length > 1\n                    ? _c(\n                        \"button\",\n                        {\n                          staticClass: \"vib-arrow vib-arrow-right vib-hideable\",\n                          class: { \"vib-hidden\": _vm.controlsHidden },\n                          attrs: { type: \"button\", title: _vm.nextText },\n                          on: {\n                            click: function($event) {\n                              $event.stopPropagation()\n                              return _vm.nextImage()\n                            },\n                            mouseover: function($event) {\n                              _vm.interfaceHovered = true\n                            },\n                            mouseleave: function($event) {\n                              _vm.interfaceHovered = false\n                            }\n                          }\n                        },\n                        [_vm._t(\"next\", [_c(\"RightArrowIcon\")])],\n                        2\n                      )\n                    : _vm._e()\n                ]\n              )\n            : _vm._e()\n        ]\n      )\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"span\", [\n    _c(\n      \"svg\",\n      {\n        attrs: {\n          fill: \"white\",\n          x: \"0px\",\n          y: \"0px\",\n          width: \"100%\",\n          height: \"100%\",\n          viewBox: \"0 0 512 512\"\n        }\n      },\n      [\n        _c(\"path\", {\n          attrs: {\n            d:\n              \"M213.7,256L213.7,256L213.7,256L380.9,81.9c4.2-4.3,4.1-11.4-0.2-15.8l-29.9-30.6c-4.3-4.4-11.3-4.5-15.5-0.2L131.1,247.9 c-2.2,2.2-3.2,5.2-3,8.1c-0.1,3,0.9,5.9,3,8.1l204.2,212.7c4.2,4.3,11.2,4.2,15.5-0.2l29.9-30.6c4.3-4.4,4.4-11.5,0.2-15.8 L213.7,256z\"\n          }\n        })\n      ]\n    )\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "/* globals __VUE_SSR_CONTEXT__ */\n\n// IMPORTANT: Do NOT use ES2015 features in this file (except for modules).\n// This module is a runtime utility for cleaner component module output and will\n// be included in the final webpack user bundle.\n\nexport default function normalizeComponent (\n  scriptExports,\n  render,\n  staticRenderFns,\n  functionalTemplate,\n  injectStyles,\n  scopeId,\n  moduleIdentifier, /* server only */\n  shadowMode /* vue-cli only */\n) {\n  // Vue.extend constructor export interop\n  var options = typeof scriptExports === 'function'\n    ? scriptExports.options\n    : scriptExports\n\n  // render functions\n  if (render) {\n    options.render = render\n    options.staticRenderFns = staticRenderFns\n    options._compiled = true\n  }\n\n  // functional template\n  if (functionalTemplate) {\n    options.functional = true\n  }\n\n  // scopedId\n  if (scopeId) {\n    options._scopeId = 'data-v-' + scopeId\n  }\n\n  var hook\n  if (moduleIdentifier) { // server build\n    hook = function (context) {\n      // 2.3 injection\n      context =\n        context || // cached call\n        (this.$vnode && this.$vnode.ssrContext) || // stateful\n        (this.parent && this.parent.$vnode && this.parent.$vnode.ssrContext) // functional\n      // 2.2 with runInNewContext: true\n      if (!context && typeof __VUE_SSR_CONTEXT__ !== 'undefined') {\n        context = __VUE_SSR_CONTEXT__\n      }\n      // inject component styles\n      if (injectStyles) {\n        injectStyles.call(this, context)\n      }\n      // register component module identifier for async chunk inferrence\n      if (context && context._registeredComponents) {\n        context._registeredComponents.add(moduleIdentifier)\n      }\n    }\n    // used by ssr in case component is cached and beforeCreate\n    // never gets called\n    options._ssrRegister = hook\n  } else if (injectStyles) {\n    hook = shadowMode\n      ? function () {\n        injectStyles.call(\n          this,\n          (options.functional ? this.parent : this).$root.$options.shadowRoot\n        )\n      }\n      : injectStyles\n  }\n\n  if (hook) {\n    if (options.functional) {\n      // for template-only hot-reload because in that case the render fn doesn't\n      // go through the normalizer\n      options._injectStyles = hook\n      // register for functional component in vue file\n      var originalRender = options.render\n      options.render = function renderWithStyleInjection (h, context) {\n        hook.call(context)\n        return originalRender(h, context)\n      }\n    } else {\n      // inject component registration as beforeCreate hook\n      var existing = options.beforeCreate\n      options.beforeCreate = existing\n        ? [].concat(existing, hook)\n        : [hook]\n    }\n  }\n\n  return {\n    exports: scriptExports,\n    options: options\n  }\n}\n", "import { render, staticRenderFns } from \"./LeftArrowIcon.vue?vue&type=template&id=73aba34a&\"\nvar script = {}\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/home/<USER>/vue-it-bigger/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('73aba34a')) {\n      api.createRecord('73aba34a', component.options)\n    } else {\n      api.reload('73aba34a', component.options)\n    }\n    module.hot.accept(\"./LeftArrowIcon.vue?vue&type=template&id=73aba34a&\", function () {\n      api.rerender('73aba34a', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/LeftArrowIcon.vue\"\nexport default component.exports", "var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"span\", [\n    _c(\n      \"svg\",\n      {\n        attrs: {\n          fill: \"white\",\n          x: \"0px\",\n          y: \"0px\",\n          width: \"100%\",\n          height: \"100%\",\n          viewBox: \"0 0 512 512\"\n        }\n      },\n      [\n        _c(\"path\", {\n          attrs: {\n            d:\n              \"M298.3,256L298.3,256L298.3,256L131.1,81.9c-4.2-4.3-4.1-11.4,0.2-15.8l29.9-30.6c4.3-4.4,11.3-4.5,15.5-0.2l204.2,212.7 c2.2,2.2,3.2,5.2,3,8.1c0.1,3-0.9,5.9-3,8.1L176.7,476.8c-4.2,4.3-11.2,4.2-15.5-0.2L131.3,446c-4.3-4.4-4.4-11.5-0.2-15.8 L298.3,256z\"\n          }\n        })\n      ]\n    )\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./RightArrowIcon.vue?vue&type=template&id=c29d3ed4&\"\nvar script = {}\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/home/<USER>/vue-it-bigger/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('c29d3ed4')) {\n      api.createRecord('c29d3ed4', component.options)\n    } else {\n      api.reload('c29d3ed4', component.options)\n    }\n    module.hot.accept(\"./RightArrowIcon.vue?vue&type=template&id=c29d3ed4&\", function () {\n      api.rerender('c29d3ed4', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/RightArrowIcon.vue\"\nexport default component.exports", "var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\"span\", [\n    _c(\n      \"svg\",\n      {\n        staticStyle: { \"enable-background\": \"new 0 0 512 512\" },\n        attrs: {\n          fill: \"white\",\n          x: \"0px\",\n          y: \"0px\",\n          width: \"100%\",\n          height: \"100%\",\n          viewBox: \"0 0 512 512\"\n        }\n      },\n      [\n        _c(\"path\", {\n          attrs: {\n            d:\n              \"M443.6,387.1L312.4,255.4l131.5-130c5.4-5.4,5.4-14.2,0-19.6l-37.4-37.6c-2.6-2.6-6.1-4-9.8-4c-3.7,0-7.2,1.5-9.8,4 L256,197.8L124.9,68.3c-2.6-2.6-6.1-4-9.8-4c-3.7,0-7.2,1.5-9.8,4L68,105.9c-5.4,5.4-5.4,14.2,0,19.6l131.5,130L68.4,387.1 c-2.6,2.6-4.1,6.1-4.1,9.8c0,3.7,1.4,7.2,4.1,9.8l37.4,37.6c2.7,2.7,6.2,4.1,9.8,4.1c3.5,0,7.1-1.3,9.8-4.1L256,313.1l130.7,131.1 c2.7,2.7,6.2,4.1,9.8,4.1c3.5,0,7.1-1.3,9.8-4.1l37.4-37.6c2.6-2.6,4.1-6.1,4.1-9.8C447.7,393.2,446.2,389.7,443.6,387.1z\"\n          }\n        })\n      ]\n    )\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./CloseIcon.vue?vue&type=template&id=1a43cbde&\"\nvar script = {}\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/home/<USER>/vue-it-bigger/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('1a43cbde')) {\n      api.createRecord('1a43cbde', component.options)\n    } else {\n      api.reload('1a43cbde', component.options)\n    }\n    module.hot.accept(\"./CloseIcon.vue?vue&type=template&id=1a43cbde&\", function () {\n      api.rerender('1a43cbde', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/CloseIcon.vue\"\nexport default component.exports", "var render = function() {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"svg\",\n    {\n      staticStyle: {\n        \"enable-background\": \"new 0 0 271.953 271.953\",\n        margin: \"12px\"\n      },\n      attrs: {\n        x: \"0px\",\n        y: \"0px\",\n        viewBox: \"0 0 271.953 271.953\",\n        \"xml:space\": \"preserve\"\n      }\n    },\n    [\n      _c(\"g\", [\n        _c(\"g\", [\n          _c(\"path\", {\n            staticStyle: { fill: \"#fff\" },\n            attrs: {\n              d:\n                \"M135.977,271.953c75.097,0,135.977-60.879,135.977-135.977S211.074,0,135.977,0S0,60.879,0,135.977    S60.879,271.953,135.977,271.953z M250.197,135.977c0,62.979-51.241,114.22-114.22,114.22s-114.22-51.241-114.22-114.22    s51.241-114.22,114.22-114.22S250.197,72.998,250.197,135.977z\"\n            }\n          }),\n          _vm._v(\" \"),\n          _c(\"path\", {\n            staticStyle: { fill: \"#fff\" },\n            attrs: {\n              d:\n                \"M112.295,205.031c2.692,1.115,5.434,1.659,8.235,1.659c5.662,0,11.183-2.208,15.344-6.375    l48.93-48.952c8.496-8.496,8.485-22.273-0.011-30.769l-48.957-48.952c-4.161-4.161-9.73-6.375-15.393-6.375    c-2.801,0-5.461,0.544-8.153,1.659c-8.126,3.367-13.255,11.297-13.255,20.097v97.903    C99.034,193.729,104.164,201.664,112.295,205.031z M120.791,88.613v-1.588l48.952,48.952l-48.952,48.952V88.613z\"\n            }\n          })\n        ])\n      ])\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./VideoIcon.vue?vue&type=template&id=cf51c4d8&\"\nvar script = {}\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/home/<USER>/vue-it-bigger/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('cf51c4d8')) {\n      api.createRecord('cf51c4d8', component.options)\n    } else {\n      api.reload('cf51c4d8', component.options)\n    }\n    module.hot.accept(\"./VideoIcon.vue?vue&type=template&id=cf51c4d8&\", function () {\n      api.rerender('cf51c4d8', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/VideoIcon.vue\"\nexport default component.exports", "<template>\n  <div @click.stop=\"closeLightBox\">\n    <transition\n      mode=\"out-in\"\n      name=\"vib-container-transition\"\n      @afterEnter=\"enableImageTransition\"\n      @beforeLeave=\"disableImageTransition\"\n    >\n      <div\n        v-if=\"media && media.length > 0\"\n        v-show=\"lightBoxShown\"\n        ref=\"container\"\n        class=\"vib-container\"\n      >\n        <div\n          class=\"vib-content\"\n          @click.stop\n        >\n          <transition\n            mode=\"out-in\"\n            :name=\"imageTransitionName\"\n          >\n            <img\n              v-if=\"currentMedia.type == undefined || currentMedia.type == 'image'\"\n              :key=\"currentMedia.src\"\n              :src=\"currentMedia.src\"\n              :srcset=\"currentMedia.srcset || ''\"\n              class=\"vib-image\"\n              :alt=\"currentMedia.caption\"\n            >\n            <div\n              v-else-if=\"media[select].type == 'youtube'\"\n              class=\"video-background\"\n            >\n              <iframe\n                :src=\"'https://www.youtube.com/embed/' + media[select].id + '?showinfo=0'\"\n                width=\"560\"\n                height=\"315\"\n                frameborder=\"0\"\n                allowfullscreen\n              />\n            </div>\n            <video\n              v-else-if=\"currentMedia.type == 'video'\"\n              :key=\"currentMedia.sources[0].src\"\n              ref=\"video\"\n              controls\n              :width=\"currentMedia.width\"\n              :height=\"currentMedia.height\"\n              :autoplay=\"currentMedia.autoplay\"\n            >\n              <source\n                v-for=\"source in currentMedia.sources\"\n                :key=\"source.src\"\n                :src=\"source.src\"\n                :type=\"source.type\"\n              >\n            </video>\n          </transition>\n        </div> <!-- .vib-content -->\n\n        <div\n          v-if=\"showThumbs\"\n          class=\"vib-thumbnail-wrapper vib-hideable\"\n          :class=\"{ 'vib-hidden': controlsHidden }\"\n          @click.stop\n          @mouseover=\"interfaceHovered = true\"\n          @mouseleave=\"interfaceHovered = false\"\n        >\n          <div\n            v-for=\"(image, index) in imagesThumb\"\n            v-show=\"index >= thumbIndex.begin && index <= thumbIndex.end\"\n            :key=\"typeof image.thumb === 'string' ? `${image.thumb}${index}` : index\"\n            :style=\"{ backgroundImage: 'url(' + image.thumb + ')' }\"\n            :class=\"'vib-thumbnail' + (select === index ? '-active' : '')\"\n            @click.stop=\"showImage(index)\"\n          >\n            <slot\n              v-if=\"image.type == 'video' || image.type == 'youtube'\"\n              name=\"videoIcon\"\n            >\n              <VideoIcon />\n            </slot>\n          </div>\n        </div> <!-- .vib-thumbnail-wrapper -->\n\n        <div\n          class=\"vib-footer vib-hideable\"\n          :class=\"{ 'vib-hidden': controlsHidden }\"\n          @mouseover=\"interfaceHovered = true\"\n          @mouseleave=\"interfaceHovered = false\"\n        >\n          <slot\n            name=\"customCaption\"\n            :currentMedia=\"currentMedia\"\n          >\n            <div\n              v-show=\"showCaption\"\n              v-html=\"currentMedia.caption\"\n            />\n          </slot>\n\n          <div class=\"vib-footer-count\">\n            <slot\n              name=\"footer\"\n              :current=\"select + 1\"\n              :total=\"media.length\"\n            >\n              {{ select + 1 }} / {{ media.length }}\n            </slot>\n          </div>\n        </div>\n\n        <button\n          v-if=\"closable\"\n          type=\"button\"\n          :title=\"closeText\"\n          class=\"vib-close vib-hideable\"\n          :class=\"{ 'vib-hidden': controlsHidden }\"\n          @mouseover=\"interfaceHovered = true\"\n          @mouseleave=\"interfaceHovered = false\"\n        >\n          <slot name=\"close\">\n            <CloseIcon />\n          </slot>\n        </button>\n\n        <button\n          v-if=\"media.length > 1\"\n          type=\"button\"\n          class=\"vib-arrow vib-arrow-left vib-hideable\"\n          :class=\"{ 'vib-hidden': controlsHidden }\"\n          :title=\"previousText\"\n          @click.stop=\"previousImage()\"\n          @mouseover=\"interfaceHovered = true\"\n          @mouseleave=\"interfaceHovered = false\"\n        >\n          <slot name=\"previous\">\n            <LeftArrowIcon />\n          </slot>\n        </button>\n\n        <button\n          v-if=\"media.length > 1\"\n          type=\"button\"\n          class=\"vib-arrow vib-arrow-right vib-hideable\"\n          :class=\"{ 'vib-hidden': controlsHidden }\"\n          :title=\"nextText\"\n          @click.stop=\"nextImage()\"\n          @mouseover=\"interfaceHovered = true\"\n          @mouseleave=\"interfaceHovered = false\"\n        >\n          <slot name=\"next\">\n            <RightArrowIcon />\n          </slot>\n        </button>\n      </div> <!-- .vib-container -->\n    </transition>\n  </div>\n</template>\n\n<script>\nimport LeftArrowIcon from './LeftArrowIcon'\nimport RightArrowIcon from './RightArrowIcon'\nimport CloseIcon from './CloseIcon'\nimport VideoIcon from './VideoIcon'\n\nlet Hammer\n\n// istanbul ignore else\nif (typeof window !== 'undefined') {\n  Hammer = require('hammerjs')\n}\n\nexport default {\n  components: {\n    LeftArrowIcon,\n    RightArrowIcon,\n    CloseIcon,\n    VideoIcon,\n  },\n\n  props: {\n    media: {\n      type: Array,\n      required: true,\n    },\n\n    disableScroll: {\n      type: Boolean,\n      default: true,\n    },\n\n    showLightBox: {\n      type: Boolean,\n      default: true,\n    },\n\n    closable: {\n      type: Boolean,\n      default: true,\n    },\n\n    startAt: {\n      type: Number,\n      default: 0,\n    },\n\n    nThumbs: {\n      type: Number,\n      default: 7,\n    },\n\n    showThumbs: {\n      type: Boolean,\n      default: true,\n    },\n\n    // Mode\n    autoPlay: {\n      type: Boolean,\n      default: false,\n    },\n\n    autoPlayTime: {\n      type: Number,\n      default: 3000,\n    },\n\n    interfaceHideTime: {\n      type: Number,\n      default: 3000,\n    },\n\n    showCaption: {\n      type: Boolean,\n      default: false,\n    },\n\n    lengthToLoadMore: {\n      type: Number,\n      default: 0\n    },\n\n    closeText: {\n      type: String,\n      default: 'Close (Esc)'\n    },\n\n    previousText: {\n      type: String,\n      default: 'Previous',\n    },\n\n    nextText: {\n      type: String,\n      default: 'Next',\n    },\n  },\n\n  data() {\n    return {\n      select: this.startAt,\n      lightBoxShown: this.showLightBox,\n      controlsHidden: false,\n      imageTransitionName: 'vib-image-no-transition',\n      timer: null,\n      interactionTimer: null,\n      interfaceHovered: false,\n    }\n  },\n\n  computed: {\n    currentMedia() {\n      return this.media[this.select]\n    },\n\n    thumbIndex() {\n      const halfDown = Math.floor(this.nThumbs / 2)\n\n      if (this.select >= halfDown && this.select < this.media.length - halfDown)\n        return {\n          begin: this.select - halfDown + (1 - this.nThumbs % 2),\n          end: this.select + halfDown,\n        }\n\n      if (this.select < halfDown)\n        return {\n          begin: 0,\n          end: this.nThumbs - 1,\n        }\n\n      return {\n        begin: this.media.length - this.nThumbs,\n        end: this.media.length - 1,\n      }\n    },\n\n    imagesThumb() {\n      return this.media.map(({ thumb, type }) => ({ thumb, type }))\n    },\n  },\n\n  watch: {\n    lightBoxShown(value) {\n      // istanbul ignore else\n      if (document != null) {\n        this.onToggleLightBox(value)\n      }\n    },\n\n    select() {\n      this.$emit('onImageChanged', this.select)\n\n      if (this.select >= this.media.length - this.lengthToLoadMore - 1)\n        this.$emit('onLoad')\n\n      if (this.select === this.media.length - 1)\n        this.$emit('onLastIndex')\n\n      if (this.select === 0)\n        this.$emit('onFirstIndex')\n\n      if (this.select === this.startAt)\n        this.$emit('onStartIndex')\n    },\n  },\n\n  mounted() {\n    if (this.autoPlay) {\n      this.timer = setInterval(this.nextImage, this.autoPlayTime)\n    }\n\n    this.onToggleLightBox(this.lightBoxShown)\n\n    if (this.$refs.container) {\n      const hammer = new Hammer(this.$refs.container)\n\n      hammer.on('swiperight', this.previousImage)\n      hammer.on('swipeleft', this.nextImage)\n\n      this.$refs.container.addEventListener('mousedown', this.handleMouseActivity);\n      this.$refs.container.addEventListener('mousemove', this.handleMouseActivity);\n      this.$refs.container.addEventListener('touchmove', this.handleMouseActivity);\n    }\n  },\n\n  beforeDestroy() {\n    document.removeEventListener('keydown', this.addKeyEvent)\n\n    if (this.autoPlay) {\n      clearInterval(this.timer)\n    }\n\n    if (this.$refs.container) {\n      this.$refs.container.removeEventListener('mousedown', this.handleMouseActivity);\n      this.$refs.container.removeEventListener('mousemove', this.handleMouseActivity);\n      this.$refs.container.removeEventListener('touchmove', this.handleMouseActivity);\n    }\n  },\n\n  methods: {\n    onLightBoxOpen() {\n      this.$emit('onOpened')\n\n      if (this.disableScroll) {\n        document.querySelector('html').classList.add('no-scroll')\n      }\n\n      document.querySelector('body').classList.add('vib-open')\n      document.addEventListener('keydown', this.addKeyEvent)\n\n      if (this.$refs.video && this.$refs.video.autoplay) {\n        this.$refs.video.play()\n      }\n    },\n\n    onLightBoxClose() {\n      this.$emit('onClosed')\n\n      if (this.disableScroll) {\n        document.querySelector('html').classList.remove('no-scroll')\n      }\n\n      document.querySelector('body').classList.remove('vib-open')\n      document.removeEventListener('keydown', this.addKeyEvent)\n\n      if (this.$refs.video) {\n        this.$refs.video.pause()\n        this.$refs.video.currentTime = '0'\n      }\n    },\n\n    onToggleLightBox(value) {\n      if (value) this.onLightBoxOpen()\n      else this.onLightBoxClose()\n    },\n\n    showImage(index) {\n      this.select = index\n      this.controlsHidden = false\n      this.lightBoxShown = true\n    },\n\n    addKeyEvent(event) {\n      switch (event.keyCode) {\n        case 37: // left arrow\n          this.previousImage()\n          break\n        case 39: // right arrow\n          this.nextImage()\n          break\n        case 27: // esc\n          this.closeLightBox()\n          break\n      }\n    },\n\n    closeLightBox() {\n      if (this.$refs.video)\n        this.$refs.video.pause();\n      if (!this.closable) return;\n      this.$set(this, 'lightBoxShown', false)\n    },\n\n    nextImage() {\n      this.$set(this, 'select', (this.select + 1) % this.media.length)\n    },\n\n    previousImage() {\n      this.$set(this, 'select', (this.select + this.media.length - 1) % this.media.length)\n    },\n\n    enableImageTransition() {\n      this.handleMouseActivity()\n      this.imageTransitionName = 'vib-image-transition'\n    },\n\n    disableImageTransition() {\n      this.imageTransitionName = 'vib-image-no-transition'\n    },\n\n    handleMouseActivity() {\n      clearTimeout(this.interactionTimer);\n\n      if (this.controlsHidden) {\n        this.controlsHidden = false\n      }\n\n      if (this.interfaceHovered) {\n        this.stopInteractionTimer()\n      } else {\n        this.startInteractionTimer()\n      }\n    },\n\n    startInteractionTimer() {\n      this.interactionTimer = setTimeout(() => {\n        this.controlsHidden = true\n      }, this.interfaceHideTime)\n    },\n\n    stopInteractionTimer() {\n      this.interactionTimer = null\n    },\n  },\n}\n</script>\n\n<style src=\"./style.css\">\n</style>\n", "import { render, staticRenderFns } from \"./LightBox.vue?vue&type=template&id=77795e85&\"\nimport script from \"./LightBox.vue?vue&type=script&lang=js&\"\nexport * from \"./LightBox.vue?vue&type=script&lang=js&\"\nimport style0 from \"./style.css?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"/home/<USER>/vue-it-bigger/node_modules/vue-hot-reload-api/dist/index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('77795e85')) {\n      api.createRecord('77795e85', component.options)\n    } else {\n      api.reload('77795e85', component.options)\n    }\n    module.hot.accept(\"./LightBox.vue?vue&type=template&id=77795e85&\", function () {\n      api.rerender('77795e85', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/components/LightBox.vue\"\nexport default component.exports"], "sourceRoot": ""}