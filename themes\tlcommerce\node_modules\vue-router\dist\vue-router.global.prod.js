/*!
  * vue-router v4.0.0
  * (c) 2020 <PERSON>
  * @license MIT
  */
var VueRouter=function(e,t){"use strict";const n="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,r=e=>n?Symbol(e):"_vr_"+e,o=r("rvlm"),a=r("rvd"),i=r("r"),c=r("rl"),l=r("rvl"),s="undefined"!=typeof window;const u=Object.assign;function f(e,t){const n={};for(const r in t){const o=t[r];n[r]=Array.isArray(o)?o.map(e):e(o)}return n}let p=()=>{};const d=/\/$/;function h(e,t,n="/"){let r,o={},a="",i="";const c=t.indexOf("?"),l=t.indexOf("#",c>-1?c:0);return c>-1&&(r=t.slice(0,c),a=t.slice(c+1,l>-1?l:t.length),o=e(a)),l>-1&&(r=r||t.slice(0,l),i=t.slice(l,t.length)),r=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),r=e.split("/");let o,a,i=n.length-1;for(o=0;o<r.length;o++)if(a=r[o],1!==i&&"."!==a){if(".."!==a)break;i--}return n.slice(0,i).join("/")+"/"+r.slice(o-(o===r.length?1:0)).join("/")}(null!=r?r:t,n),{fullPath:r+(a&&"?")+a+i,path:r,query:o,hash:i}}function m(e,t){return!t||e.toLowerCase().indexOf(t.toLowerCase())?e:e.slice(t.length)||"/"}function v(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function g(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let n in e)if(!y(e[n],t[n]))return!1;return!0}function y(e,t){return Array.isArray(e)?b(e,t):Array.isArray(t)?b(t,e):e===t}function b(e,t){return Array.isArray(t)?e.length===t.length&&e.every(((e,n)=>e===t[n])):1===e.length&&e[0]===t}var _,O;!function(e){e.pop="pop",e.push="push"}(_||(_={})),function(e){e.back="back",e.forward="forward",e.unknown=""}(O||(O={}));function E(e){if(!e)if(s){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(d,"")}const w=/^[^#]+#/;function P(e,t){return e.replace(w,"#")+t}const R=()=>({left:window.pageXOffset,top:window.pageYOffset});function A(e){let t;if("el"in e){let n=e.el;const r="string"==typeof n&&n.startsWith("#"),o="string"==typeof n?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!o)return;t=function(e,t){const n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}(o,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.pageXOffset,null!=t.top?t.top:window.pageYOffset)}function k(e,t){return(history.state?history.state.position-t:-1)+e}const T=new Map;let j=()=>location.protocol+"//"+location.host;function C(e,t){const{pathname:n,search:r,hash:o}=t;if(e.indexOf("#")>-1){let e=o.slice(1);return"/"!==e[0]&&(e="/"+e),m(e,"")}return m(n,e)+r+o}function S(e,t,n,r=!1,o=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:o?R():null}}function x(e){const t=function(e){const{history:t,location:n}=window;let r={value:C(e,n)},o={value:t.state};function a(r,a,i){const c=e.indexOf("#"),l=c>-1?e.slice(c)+r:j()+e+r;try{t[i?"replaceState":"pushState"](a,"",l),o.value=a}catch(e){console.error(e),n[i?"replace":"assign"](l)}}return o.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:o,push:function(e,n){const i=u({},o.value,t.state,{forward:e,scroll:R()});a(i.current,i,!0),a(e,u({},S(r.value,e,null),{position:i.position+1},n),!1),r.value=e},replace:function(e,n){a(e,u({},t.state,S(o.value.back,e,o.value.forward,!0),n,{position:o.value.position}),!0),r.value=e}}}(e=E(e)),n=function(e,t,n,r){let o=[],a=[],i=null;const c=({state:a})=>{const c=C(e,location),l=n.value,s=t.value;let u=0;if(a){if(n.value=c,t.value=a,i&&i===l)return void(i=null);u=s?a.position-s.position:0}else r(c);o.forEach((e=>{e(n.value,l,{delta:u,type:_.pop,direction:u?u>0?O.forward:O.back:O.unknown})}))};function l(){const{history:e}=window;e.state&&e.replaceState(u({},e.state,{scroll:R()}),"")}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",l),{pauseListeners:function(){i=n.value},listen:function(e){o.push(e);const t=()=>{const t=o.indexOf(e);t>-1&&o.splice(t,1)};return a.push(t),t},destroy:function(){for(const e of a)e();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace);const r=u({location:"",base:e,go:function(e,t=!0){t||n.pauseListeners(),history.go(e)},createHref:P.bind(null,e)},t,n);return Object.defineProperty(r,"location",{get:()=>t.location.value}),Object.defineProperty(r,"state",{get:()=>t.state.value}),r}function M(e){return"string"==typeof e||"symbol"==typeof e}const L={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0},N=r("nf");var $;function G(e,t){return u(new Error,{type:e,[N]:!0},t)}function I(e,t){return e instanceof Error&&N in e&&(null==t||!!(e.type&t))}($=e.NavigationFailureType||(e.NavigationFailureType={}))[$.aborted=4]="aborted",$[$.cancelled=8]="cancelled",$[$.duplicated=16]="duplicated";const B="[^/]+?",U={sensitive:!1,strict:!1,start:!0,end:!0},D=/[.+*?^${}()[\]/\\]/g;function V(e,t){let n=0;for(;n<e.length&&n<t.length;){const r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function H(e,t){let n=0;const r=e.score,o=t.score;for(;n<r.length&&n<o.length;){const e=V(r[n],o[n]);if(e)return e;n++}return o.length-r.length}const q={type:0,value:""},K=/[a-zA-Z0-9_]/;function F(e,t,n){const r=function(e,t){const n=u({},U,t);let r=[],o=n.start?"^":"";const a=[];for(const t of e){const e=t.length?[]:[90];n.strict&&!t.length&&(o+="/");for(let r=0;r<t.length;r++){const i=t[r];let c=40+(n.sensitive?.25:0);if(0===i.type)r||(o+="/"),o+=i.value.replace(D,"\\$&"),c+=40;else if(1===i.type){const{value:e,repeatable:t,optional:n,regexp:l}=i;a.push({name:e,repeatable:t,optional:n});const s=l||B;if(s!==B){c+=10;try{new RegExp(`(${s})`)}catch(t){throw new Error(`Invalid custom RegExp for param "${e}" (${s}): `+t.message)}}let u=t?`((?:${s})(?:/(?:${s}))*)`:`(${s})`;r||(u=n?`(?:/${u})`:"/"+u),n&&(u+="?"),o+=u,c+=20,n&&(c+=-8),t&&(c+=-20),".*"===s&&(c+=-50)}e.push(c)}r.push(e)}if(n.strict&&n.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(o+="/?"),n.end?o+="$":n.strict&&(o+="(?:/|$)");const i=new RegExp(o,n.sensitive?"":"i");return{re:i,score:r,keys:a,parse:function(e){const t=e.match(i),n={};if(!t)return null;for(let e=1;e<t.length;e++){const r=t[e]||"",o=a[e-1];n[o.name]=r&&o.repeatable?r.split("/"):r}return n},stringify:function(t){let n="",r=!1;for(const o of e){r&&n.endsWith("/")||(n+="/"),r=!1;for(const e of o)if(0===e.type)n+=e.value;else if(1===e.type){const{value:o,repeatable:a,optional:i}=e,c=o in t?t[o]:"";if(Array.isArray(c)&&!a)throw new Error(`Provided param "${o}" is an array but it is not repeatable (* or + modifiers)`);const l=Array.isArray(c)?c.join("/"):c;if(!l){if(!i)throw new Error(`Missing required param "${o}"`);n.endsWith("/")?n=n.slice(0,-1):r=!0}n+=l}}return n}}}(function(e){if(!e)return[[]];if("/"===e)return[[q]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${n})/"${s}": ${e}`)}let n=0,r=n;const o=[];let a;function i(){a&&o.push(a),a=[]}let c,l=0,s="",u="";function f(){s&&(0===n?a.push({type:0,value:s}):1===n||2===n||3===n?(a.length>1&&("*"===c||"+"===c)&&t(`A repeatable param (${s}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:s,regexp:u,repeatable:"*"===c||"+"===c,optional:"*"===c||"?"===c})):t("Invalid state to consume buffer"),s="")}function p(){s+=c}for(;l<e.length;)if(c=e[l++],"\\"!==c||2===n)switch(n){case 0:"/"===c?(s&&f(),i()):":"===c?(f(),n=1):p();break;case 4:p(),n=r;break;case 1:"("===c?(n=2,u=""):K.test(c)?p():(f(),n=0,"*"!==c&&"?"!==c&&"+"!==c&&l--);break;case 2:")"===c?"\\"==u[u.length-1]?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:f(),n=0,"*"!==c&&"?"!==c&&"+"!==c&&l--;break;default:t("Unknown state")}else r=n,n=4;return 2===n&&t(`Unfinished custom RegExp for param "${s}"`),f(),i(),o}(e.path),n),o=u(r,{record:e,parent:t,children:[],alias:[]});return t&&!o.record.aliasOf==!t.record.aliasOf&&t.children.push(o),o}function W(e,t){const n=[],r=new Map;function o(e,n,r){let c=!r,l=function(e){return{path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:void 0,beforeEnter:e.beforeEnter,props:z(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||{}:{default:e.component}}}(e);l.aliasOf=r&&r.record;const s=Y(t,e),f=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)f.push(u({},l,{components:r?r.record.components:l.components,path:e,aliasOf:r?r.record:l}))}let d,h;for(const t of f){let{path:u}=t;if(n&&"/"!==u[0]){let e=n.record.path,r="/"===e[e.length-1]?"":"/";t.path=n.record.path+(u&&r+u)}if(d=F(t,n,s),r?r.alias.push(d):(h=h||d,h!==d&&h.alias.push(d),c&&e.name&&!Q(d)&&a(e.name)),"children"in l){let e=l.children;for(let t=0;t<e.length;t++)o(e[t],d,r&&r.children[t])}r=r||d,i(d)}return h?()=>{a(h)}:p}function a(e){if(M(e)){const t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(a),t.alias.forEach(a))}else{let t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(a),e.alias.forEach(a))}}function i(e){let t=0;for(;t<n.length&&H(e,n[t])>=0;)t++;n.splice(t,0,e),e.record.name&&!Q(e)&&r.set(e.record.name,e)}return t=Y({strict:!1,end:!0,sensitive:!1},t),e.forEach((e=>o(e))),{addRoute:o,resolve:function(e,t){let o,a,i,c={};if("name"in e&&e.name){if(o=r.get(e.name),!o)throw G(1,{location:e});i=o.record.name,c=u(function(e,t){let n={};for(let r of t)r in e&&(n[r]=e[r]);return n}(t.params,o.keys.filter((e=>!e.optional)).map((e=>e.name))),e.params),a=o.stringify(c)}else if("path"in e)a=e.path,o=n.find((e=>e.re.test(a))),o&&(c=o.parse(a),i=o.record.name);else{if(o=t.name?r.get(t.name):n.find((e=>e.re.test(t.path))),!o)throw G(1,{location:e,currentLocation:t});i=o.record.name,c=u({},t.params,e.params),a=o.stringify(c)}const l=[];let s=o;for(;s;)l.unshift(s.record),s=s.parent;return{name:i,path:a,params:c,matched:l,meta:X(l)}},removeRoute:a,getRoutes:function(){return n},getRecordMatcher:function(e){return r.get(e)}}}function z(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(let r in e.components)t[r]="boolean"==typeof n?n:n[r];return t}function Q(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function X(e){return e.reduce(((e,t)=>u(e,t.meta)),{})}function Y(e,t){let n={};for(let r in e)n[r]=r in t?t[r]:e[r];return n}const Z=/#/g,J=/&/g,ee=/\//g,te=/=/g,ne=/\?/g,re=/\+/g,oe=/%5B/g,ae=/%5D/g,ie=/%5E/g,ce=/%60/g,le=/%7B/g,se=/%7C/g,ue=/%7D/g,fe=/%20/g;function pe(e){return encodeURI(""+e).replace(se,"|").replace(oe,"[").replace(ae,"]")}function de(e){return pe(e).replace(re,"%2B").replace(fe,"+").replace(Z,"%23").replace(J,"%26").replace(ce,"`").replace(le,"{").replace(ue,"}").replace(ie,"^")}function he(e){return function(e){return pe(e).replace(Z,"%23").replace(ne,"%3F")}(e).replace(ee,"%2F")}function me(e){try{return decodeURIComponent(""+e)}catch(e){}return""+e}function ve(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let e=0;e<n.length;++e){const r=n[e].replace(re," ");let o=r.indexOf("="),a=me(o<0?r:r.slice(0,o)),i=o<0?null:me(r.slice(o+1));if(a in t){let e=t[a];Array.isArray(e)||(e=t[a]=[e]),e.push(i)}else t[a]=i}return t}function ge(e){let t="";for(let n in e){t.length&&(t+="&");const r=e[n];if(n=de(n).replace(te,"%3D"),null==r){void 0!==r&&(t+=n);continue}let o=Array.isArray(r)?r.map((e=>e&&de(e))):[r&&de(r)];for(let e=0;e<o.length;e++)t+=(e?"&":"")+n,null!=o[e]&&(t+="="+o[e])}return t}function ye(e){const t={};for(let n in e){let r=e[n];void 0!==r&&(t[n]=Array.isArray(r)?r.map((e=>null==e?null:""+e)):null==r?r:""+r)}return t}function be(){let e=[];return{add:function(t){return e.push(t),()=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)}},list:()=>e,reset:function(){e=[]}}}function _e(e,n,r){const o=()=>{e[n].delete(r)};t.onUnmounted(o),t.onDeactivated(o),t.onActivated((()=>{e[n].add(r)})),e[n].add(r)}function Oe(e,t,n,r,o){const a=r&&(r.enterCallbacks[o]=r.enterCallbacks[o]||[]);return()=>new Promise(((i,c)=>{const l=e=>{var l;!1===e?c(G(4,{from:n,to:t})):e instanceof Error?c(e):"string"==typeof(l=e)||l&&"object"==typeof l?c(G(2,{from:t,to:e})):(a&&r.enterCallbacks[o]===a&&"function"==typeof e&&a.push(e),i())},s=e.call(r&&r.instances[o],t,n,l);let u=Promise.resolve(s);e.length<3&&(u=u.then(l)),u.catch((e=>c(e)))}))}function Ee(e,t,r,o){const a=[];for(const i of e)for(const e in i.components){let c=i.components[e];if("beforeRouteEnter"===t||i.instances[e])if(we(c)){const n=(c.__vccOpts||c)[t];n&&a.push(Oe(n,r,o,i,e))}else{let l=c();l=l.catch(console.error),a.push((()=>l.then((a=>{if(!a)return Promise.reject(new Error(`Couldn't resolve component "${e}" at "${i.path}"`));const c=(l=a).__esModule||n&&"Module"===l[Symbol.toStringTag]?a.default:a;var l;i.components[e]=c;const s=c[t];return s&&Oe(s,r,o,i,e)()}))))}}return a}function we(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}function Pe(e){const n=t.inject(i),r=t.inject(c),o=t.computed((()=>n.resolve(t.unref(e.to)))),a=t.computed((()=>{let{matched:e}=o.value,{length:t}=e;const n=e[t-1];let a=r.matched;if(!n||!a.length)return-1;let i=a.findIndex(v.bind(null,n));if(i>-1)return i;let c=Ae(e[t-2]);return t>1&&Ae(n)===c&&a[a.length-1].path!==c?a.findIndex(v.bind(null,e[t-2])):i})),l=t.computed((()=>a.value>-1&&function(e,t){for(let n in t){let r=t[n],o=e[n];if("string"==typeof r){if(r!==o)return!1}else if(!Array.isArray(o)||o.length!==r.length||r.some(((e,t)=>e!==o[t])))return!1}return!0}(r.params,o.value.params))),s=t.computed((()=>a.value>-1&&a.value===r.matched.length-1&&g(r.params,o.value.params)));return{route:o,href:t.computed((()=>o.value.href)),isActive:l,isExactActive:s,navigate:function(r={}){return function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(r)?n[t.unref(e.replace)?"replace":"push"](t.unref(e.to)):Promise.resolve()}}}const Re=t.defineComponent({name:"RouterLink",props:{to:{type:[String,Object],required:!0},activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},setup(e,{slots:n,attrs:r}){const o=t.reactive(Pe(e)),{options:a}=t.inject(i),c=t.computed((()=>({[ke(e.activeClass,a.linkActiveClass,"router-link-active")]:o.isActive,[ke(e.exactActiveClass,a.linkExactActiveClass,"router-link-exact-active")]:o.isExactActive})));return()=>{const a=n.default&&n.default(o);return e.custom?a:t.h("a",u({"aria-current":o.isExactActive?e.ariaCurrentValue:null,onClick:o.navigate,href:o.href},r,{class:c.value}),a)}}});function Ae(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const ke=(e,t,n)=>null!=e?e:null!=t?t:n;function Te(e,t){if(!e)return null;const n=e(t);return 1===n.length?n[0]:n}const je=t.defineComponent({name:"RouterView",props:{name:{type:String,default:"default"},route:Object},setup(e,{attrs:n,slots:r}){const i=t.inject(l),c=t.computed((()=>e.route||i.value)),s=t.inject(a,0),f=t.computed((()=>c.value.matched[s]));t.provide(a,s+1),t.provide(o,f),t.provide(l,c);const p=t.ref();return t.watch((()=>[p.value,f.value,e.name]),(([e,t,n],[r,o,a])=>{t&&(t.instances[n]=e,o&&o!==t&&e&&e===r&&(t.leaveGuards=o.leaveGuards,t.updateGuards=o.updateGuards)),!e||!t||o&&v(t,o)&&r||(t.enterCallbacks[n]||[]).forEach((t=>t(e)))}),{flush:"post"}),()=>{const o=c.value,a=f.value,i=a&&a.components[e.name],l=e.name;if(!i)return Te(r.default,{Component:i,route:o});const s=a.props[e.name],d=s?!0===s?o.params:"function"==typeof s?s(o):s:null,h=t.h(i,u({},d,n,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[l]=null)},ref:p}));return Te(r.default,{Component:h,route:o})||h}}});var Ce="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Se(e){if(e.__esModule)return e;var t=Object.defineProperty({},"__esModule",{value:!0});return Object.keys(e).forEach((function(n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})})),t}function xe(e){var t={exports:{}};return e(t,t.exports),t.exports}var Me=xe((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.hook=t.target=t.isBrowser=void 0,t.isBrowser="undefined"!=typeof navigator,t.target=t.isBrowser?window:void 0!==Ce?Ce:{},t.hook=t.target.__VUE_DEVTOOLS_GLOBAL_HOOK__})),Le=xe((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.ApiHookEvents=void 0,function(e){e.SETUP_DEVTOOLS_PLUGIN="devtools-plugin:setup"}(t.ApiHookEvents||(t.ApiHookEvents={}))}));Object.defineProperty(e,"__esModule",{value:!0});var Ne=Object.freeze({__proto__:null});Object.defineProperty(e,"__esModule",{value:!0});var $e=Object.freeze({__proto__:null});Object.defineProperty(e,"__esModule",{value:!0});var Ge=Object.freeze({__proto__:null});Object.defineProperty(e,"__esModule",{value:!0});var Ie=Object.freeze({__proto__:null}),Be=xe((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.Hooks=void 0,function(e){e.TRANSFORM_CALL="transformCall",e.GET_APP_RECORD_NAME="getAppRecordName",e.GET_APP_ROOT_INSTANCE="getAppRootInstance",e.REGISTER_APPLICATION="registerApplication",e.WALK_COMPONENT_TREE="walkComponentTree",e.WALK_COMPONENT_PARENTS="walkComponentParents",e.INSPECT_COMPONENT="inspectComponent",e.GET_COMPONENT_BOUNDS="getComponentBounds",e.GET_COMPONENT_NAME="getComponentName",e.GET_ELEMENT_COMPONENT="getElementComponent",e.GET_INSPECTOR_TREE="getInspectorTree",e.GET_INSPECTOR_STATE="getInspectorState"}(t.Hooks||(t.Hooks={}))})),Ue=Se(Ne),De=Se($e),Ve=Se(Ge),He=Se(Ie),qe=xe((function(e,t){var n=Ce&&Ce.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),r=Ce&&Ce.__exportStar||function(e,t){for(var r in e)"default"===r||t.hasOwnProperty(r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),r(Ue,t),r(De,t),r(Ve,t),r(He,t),r(Be,t)}));xe((function(e,t){var n=Ce&&Ce.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),r=Ce&&Ce.__exportStar||function(e,t){for(var r in e)"default"===r||t.hasOwnProperty(r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.setupDevtoolsPlugin=void 0,r(qe,t),t.setupDevtoolsPlugin=function(e,t){if(Me.hook)Me.hook.emit(Le.ApiHookEvents.SETUP_DEVTOOLS_PLUGIN,e,t);else{(Me.target.__VUE_DEVTOOLS_PLUGINS__=Me.target.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:e,setupFn:t})}}}));function Ke(e){return e.reduce(((e,t)=>e.then((()=>t()))),Promise.resolve())}return e.RouterLink=Re,e.RouterView=je,e.START_LOCATION=L,e.createMemoryHistory=function(e=""){let t=[],n=[""],r=0;function o(e){r++,r===n.length||n.splice(r),n.push(e)}const a={location:"",state:{},base:e,createHref:P.bind(null,e),replace(e){n.splice(r--,1),o(e)},push(e,t){o(e)},listen:e=>(t.push(e),()=>{const n=t.indexOf(e);n>-1&&t.splice(n,1)}),destroy(){t=[]},go(e,o=!0){const a=this.location,i=e<0?O.back:O.forward;r=Math.max(0,Math.min(r+e,n.length-1)),o&&function(e,n,{direction:r,delta:o}){const a={direction:r,delta:o,type:_.pop};for(let r of t)r(e,n,a)}(this.location,a,{direction:i,delta:e})}};return Object.defineProperty(a,"location",{get:()=>n[r]}),a},e.createRouter=function(e){const n=W(e.routes,e);let r=e.parseQuery||ve,o=e.stringifyQuery||ge,a=e.history;const d=be(),m=be(),y=be(),b=t.shallowRef(L);let _=L;s&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const O=f.bind(null,(e=>""+e)),E=f.bind(null,he),w=f.bind(null,me);function P(e,t){if(t=u({},t||b.value),"string"==typeof e){let o=h(r,e,t.path),i=n.resolve({path:o.path},t),c=a.createHref(o.fullPath);return u(o,i,{params:w(i.params),hash:me(o.hash),redirectedFrom:void 0,href:c})}let i;"path"in e?i=u({},e,{path:h(r,e.path,t.path).path}):(i=u({},e,{params:E(e.params)}),t.params=E(t.params));let c=n.resolve(i,t);const l=e.hash||"";c.params=O(w(c.params));const s=function(e,t){let n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}(o,u({},e,{hash:(f=l,pe(f).replace(le,"{").replace(ue,"}").replace(ie,"^")),path:c.path}));var f;let p=a.createHref(s);return u({fullPath:s,hash:l,query:o===ge?ye(e.query):e.query},c,{redirectedFrom:void 0,href:p})}function j(e){return"string"==typeof e?{path:e}:u({},e)}function C(e,t){if(_!==e)return G(8,{from:t,to:e})}function S(e){return N(e)}function x(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:n}=t;let r=j("function"==typeof n?n(e):n);return u({query:e.query,hash:e.hash,params:e.params},r)}}function N(e,t){const n=_=P(e),r=b.value,a=e.state,i=e.force,c=!0===e.replace,l=x(n);if(l)return N(u(l,{state:a,force:i,replace:c}),t||n);const s=n;let f;return s.redirectedFrom=t,!i&&function(e,t,n){let r=t.matched.length-1,o=n.matched.length-1;return r>-1&&r===o&&v(t.matched[r],n.matched[o])&&g(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}(o,r,n)&&(f=G(16,{to:s,from:r}),X(r,r,!0,!1)),(f?Promise.resolve(f):B(s,r)).catch((e=>I(e)?e:z(e))).then((e=>{if(e){if(I(e,2))return N(u(j(e.to),{state:a,force:i,replace:c}),t||s)}else e=D(s,r,!0,c,a);return U(s,r,e),e}))}function $(e,t){const n=C(e,t);return n?Promise.reject(n):Promise.resolve()}function B(e,t){let n;const[r,o,a]=function(e,t){const n=[],r=[],o=[],a=Math.max(t.matched.length,e.matched.length);for(let i=0;i<a;i++){const a=t.matched[i];a&&(e.matched.indexOf(a)<0?n.push(a):r.push(a));const c=e.matched[i];c&&t.matched.indexOf(c)<0&&o.push(c)}return[n,r,o]}(e,t);n=Ee(r.reverse(),"beforeRouteLeave",e,t);for(const o of r)o.leaveGuards.forEach((r=>{n.push(Oe(r,e,t))}));const i=$.bind(null,e,t);return n.push(i),Ke(n).then((()=>{n=[];for(const r of d.list())n.push(Oe(r,e,t));return n.push(i),Ke(n)})).then((()=>{n=Ee(o,"beforeRouteUpdate",e,t);for(const r of o)r.updateGuards.forEach((r=>{n.push(Oe(r,e,t))}));return n.push(i),Ke(n)})).then((()=>{n=[];for(const r of e.matched)if(r.beforeEnter&&t.matched.indexOf(r)<0)if(Array.isArray(r.beforeEnter))for(const o of r.beforeEnter)n.push(Oe(o,e,t));else n.push(Oe(r.beforeEnter,e,t));return n.push(i),Ke(n)})).then((()=>(e.matched.forEach((e=>e.enterCallbacks={})),n=Ee(a,"beforeRouteEnter",e,t),n.push(i),Ke(n)))).then((()=>{n=[];for(const r of m.list())n.push(Oe(r,e,t));return n.push(i),Ke(n)})).catch((e=>I(e,8)?e:Promise.reject(e)))}function U(e,t,n){for(const r of y.list())r(e,t,n)}function D(e,t,n,r,o){const i=C(e,t);if(i)return i;const c=t===L,l=s?history.state:{};n&&(r||c?a.replace(e.fullPath,u({scroll:c&&l&&l.scroll},o)):a.push(e.fullPath,o)),b.value=e,X(e,t,n,c),Q()}let V;function H(){V=a.listen(((e,t,n)=>{let r=P(e);const o=x(r);if(o)return void N(u(o,{replace:!0}),r).catch(p);_=r;const i=b.value;var c,l;s&&(c=k(i.fullPath,n.delta),l=R(),T.set(c,l)),B(r,i).catch((e=>I(e,12)?e:I(e,2)?(n.delta&&a.go(-n.delta,!1),N(e.to,r).catch(p),Promise.reject()):(n.delta&&a.go(-n.delta,!1),z(e)))).then((e=>{(e=e||D(r,i,!1))&&n.delta&&a.go(-n.delta,!1),U(r,i,e)})).catch(p)}))}let q,K=be(),F=be();function z(e){return Q(e),F.list().forEach((t=>t(e))),Promise.reject(e)}function Q(e){q||(q=!0,H(),K.list().forEach((([t,n])=>e?n(e):t())),K.reset())}function X(n,r,o,a){const{scrollBehavior:i}=e;if(!s||!i)return Promise.resolve();let c=!o&&function(e){const t=T.get(e);return T.delete(e),t}(k(n.fullPath,0))||(a||!o)&&history.state&&history.state.scroll||null;return t.nextTick().then((()=>i(n,r,c))).then((e=>e&&A(e))).catch(z)}const Y=e=>a.go(e);let Z;const J=new Set;return{currentRoute:b,addRoute:function(e,t){let r,o;return M(e)?(r=n.getRecordMatcher(e),o=t):o=e,n.addRoute(o,r)},removeRoute:function(e){let t=n.getRecordMatcher(e);t&&n.removeRoute(t)},hasRoute:function(e){return!!n.getRecordMatcher(e)},getRoutes:function(){return n.getRoutes().map((e=>e.record))},resolve:P,options:e,push:S,replace:function(e){return S(u(j(e),{replace:!0}))},go:Y,back:()=>Y(-1),forward:()=>Y(1),beforeEach:d.add,beforeResolve:m.add,afterEach:y.add,onError:F.add,isReady:function(){return q&&b.value!==L?Promise.resolve():new Promise(((e,t)=>{K.add([e,t])}))},install(e){e.component("RouterLink",Re),e.component("RouterView",je),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{get:()=>t.unref(b)}),s&&!Z&&b.value===L&&(Z=!0,S(a.location).catch((e=>{})));const n={};for(let e in L)n[e]=t.computed((()=>b.value[e]));e.provide(i,this),e.provide(c,t.reactive(n)),e.provide(l,b);let r=e.unmount;J.add(e),e.unmount=function(){J.delete(e),J.size<1&&(V(),b.value=L,Z=!1,q=!1),r.call(this,arguments)}}}},e.createRouterMatcher=W,e.createWebHashHistory=function(e){return(e=location.host?e||location.pathname:"").indexOf("#")<0&&(e+="#"),x(e)},e.createWebHistory=x,e.isNavigationFailure=I,e.matchedRouteKey=o,e.onBeforeRouteLeave=function(e){const n=t.inject(o,{}).value;n&&_e(n,"leaveGuards",e)},e.onBeforeRouteUpdate=function(e){const n=t.inject(o,{}).value;n&&_e(n,"updateGuards",e)},e.parseQuery=ve,e.routeLocationKey=c,e.routerKey=i,e.routerViewLocationKey=l,e.stringifyQuery=ge,e.useLink=Pe,e.useRoute=function(){return t.inject(c)},e.useRouter=function(){return t.inject(i)},e.viewDepthKey=a,Object.defineProperty(e,"__esModule",{value:!0}),e}({},Vue);
