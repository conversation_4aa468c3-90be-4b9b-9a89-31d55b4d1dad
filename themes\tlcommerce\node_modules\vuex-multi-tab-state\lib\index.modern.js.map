{"version": 3, "file": "index.modern.js", "sources": ["../src/tab.ts", "../src/index.ts"], "sourcesContent": ["export default class Tab {\n  private tabId!: string;\n\n  private window!: Window;\n\n  constructor(window: Window) {\n    // Thanks to: https://gist.github.com/6174/6062387\n    this.tabId =\n      Math.random()\n        .toString(36)\n        .substring(2, 15) +\n      Math.random()\n        .toString(36)\n        .substring(2, 15);\n    this.window = window;\n  }\n\n  storageAvailable(): Bo<PERSON>an {\n    const test = 'vuex-multi-tab-state-test';\n    try {\n      this.window.localStorage.setItem(test, test);\n      this.window.localStorage.removeItem(test);\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  saveState(key: string, state: object) {\n    const toSave = JSON.stringify({\n      id: this.tabId,\n      state,\n    });\n\n    // Save the state in local storage\n    this.window.localStorage.setItem(key, toSave);\n  }\n\n  fetchState(key: string, cb: Function) {\n    const value = this.window.localStorage.getItem(key);\n\n    if (value) {\n      try {\n        const parsed = JSON.parse(value);\n        cb(parsed.state);\n      } catch (e) {\n        console.warn(`State saved in localStorage with key ${key} is invalid!`);\n      }\n    }\n  }\n\n  addEventListener(key: string, cb: Function) {\n    return this.window.addEventListener('storage', (event: StorageEvent) => {\n      if (!event.newValue || event.key !== key) {\n        return;\n      }\n\n      try {\n        const newState = JSON.parse(event.newValue);\n\n        // Check if the new state is from another tab\n        if (newState.id !== this.tabId) {\n          cb(newState.state);\n        }\n      } catch (e) {\n        console.warn(\n          `New state saved in localStorage with key ${key} is invalid`\n        );\n      }\n    });\n  }\n}\n", "import { pick, set, remove } from 'dot-object';\nimport Tab from './tab';\n\nexport interface Options {\n  statesPaths?: string[];\n  key?: string;\n  onBeforeReplace?(state: any): any;\n  onBeforeSave?(state: any): any;\n}\n\nexport default function(options?: Options) {\n  const tab = new Tab(window);\n  let key: string = 'vuex-multi-tab';\n  let statesPaths: string[] = [];\n  let onBeforeReplace = (state: any) => state;\n  let onBeforeSave = (state: any) => state;\n\n  if (options) {\n    key = options.key ? options.key : key;\n    statesPaths = options.statesPaths ? options.statesPaths : statesPaths;\n    onBeforeReplace = options.onBeforeReplace || onBeforeReplace;\n    onBeforeSave = options.onBeforeSave || onBeforeSave;\n  }\n\n  function filterStates(state: { [key: string]: any }): { [key: string]: any } {\n    const result = {};\n    statesPaths.forEach(statePath => {\n      set(statePath, pick(statePath, state), result);\n    });\n    return result;\n  }\n\n  /**\n   * simple object deep clone method\n   * @param obj\n   */\n  function cloneObj(obj: any): any {\n    if (Array.isArray(obj)) {\n      return obj.map(val => cloneObj(val));\n    }\n    if (typeof obj === 'object' && obj !== null) {\n      return Object.keys(obj).reduce((r: any, objKey) => {\n        r[objKey] = cloneObj(obj[objKey]);\n        return r;\n      }, {});\n    }\n    return obj;\n  }\n\n  function mergeState(oldState: any, newState: object) {\n    // if whole state is to be replaced then do just that\n    if (statesPaths.length === 0) return { ...newState };\n\n    // else clone old state\n    const merged: any = cloneObj(oldState);\n\n    // and replace only specified paths\n    statesPaths.forEach(statePath => {\n      const newValue = pick(statePath, newState);\n      // remove value if it doesn't exist, overwrite otherwise\n      if (typeof newValue === 'undefined') remove(statePath, merged);\n      else set(statePath, newValue, merged);\n    });\n    return merged;\n  }\n\n  if (!tab.storageAvailable()) {\n    throw new Error('Local storage is not available!');\n  }\n\n  function replaceState(store: any, state: object) {\n    const adjustedState = onBeforeReplace(state);\n\n    if (adjustedState) {\n      store.replaceState(mergeState(store.state, adjustedState));\n    }\n  }\n\n  return (store: any) => {\n    // First time, fetch state from local storage\n    tab.fetchState(key, (state: object) => {\n      replaceState(store, state);\n    });\n\n    // Add event listener to the state saved in local storage\n    tab.addEventListener(key, (state: object) => {\n      replaceState(store, state);\n    });\n\n    store.subscribe((mutation: MutationEvent, state: object) => {\n      let toSave = state;\n\n      // Filter state\n      if (statesPaths.length > 0) {\n        toSave = filterStates(state);\n      }\n\n      toSave = onBeforeSave(toSave);\n\n      // Save state in local storage\n      if (toSave) {\n        tab.saveState(key, toSave);\n      }\n    });\n  };\n}\n"], "names": ["Tab", "constructor", "window", "this", "tabId", "Math", "random", "toString", "substring", "storageAvailable", "test", "localStorage", "setItem", "removeItem", "e", "saveState", "key", "state", "toSave", "JSON", "stringify", "id", "fetchState", "cb", "value", "getItem", "parse", "console", "warn", "addEventListener", "event", "newValue", "newState", "options", "tab", "statesPaths", "onBeforeReplace", "onBeforeSave", "Error", "replaceState", "store", "adjustedState", "oldState", "length", "merged", "cloneObj", "obj", "Array", "isArray", "map", "val", "Object", "keys", "reduce", "r", "obj<PERSON><PERSON>", "for<PERSON>ach", "statePath", "pick", "remove", "set", "mergeState", "subscribe", "mutation", "result", "filterStates"], "mappings": "4QAAqBA,EAKnBC,YAAYC,GAEVC,KAAKC,MACHC,KAAKC,SACFC,SAAS,IACTC,UAAU,EAAG,IAChBH,KAAKC,SACFC,SAAS,IACTC,UAAU,EAAG,IAClBL,KAAKD,OAASA,EAGhBO,mBACE,MAAMC,EAAO,4BACb,IAGE,OAFAP,KAAKD,OAAOS,aAAaC,QAAQF,EAAMA,GACvCP,KAAKD,OAAOS,aAAaE,WAAWH,MAEpC,MAAOI,GACP,UAIJC,UAAUC,EAAaC,GACrB,MAAMC,EAASC,KAAKC,UAAU,CAC5BC,GAAIlB,KAAKC,MACTa,MAAAA,IAIFd,KAAKD,OAAOS,aAAaC,QAAQI,EAAKE,GAGxCI,WAAWN,EAAaO,GACtB,MAAMC,EAAQrB,KAAKD,OAAOS,aAAac,QAAQT,GAE/C,GAAIQ,EACF,IAEED,EADeJ,KAAKO,MAAMF,GAChBP,OACV,MAAOH,GACPa,QAAQC,6CAA6CZ,kBAK3Da,iBAAiBb,EAAaO,GAC5B,YAAYrB,OAAO2B,iBAAiB,UAAYC,IAC9C,GAAKA,EAAMC,UAAYD,EAAMd,MAAQA,EAIrC,IACE,MAAMgB,EAAWb,KAAKO,MAAMI,EAAMC,UAG9BC,EAASX,KAAOlB,KAAKC,OACvBmB,EAAGS,EAASf,OAEd,MAAOH,GACPa,QAAQC,iDACsCZ,4CCxD9BiB,GACtB,MAAMC,EAAM,IAAIlC,EAAIE,QACpB,IAAIc,EAAc,iBACdmB,EAAwB,GACxBC,EAAmBnB,GAAeA,EAClCoB,EAAgBpB,GAAeA,EAmDnC,GAjDIgB,IACFjB,EAAMiB,EAAQjB,IAAMiB,EAAQjB,IAAMA,EAClCmB,EAAcF,EAAQE,YAAcF,EAAQE,YAAcA,EAC1DC,EAAkBH,EAAQG,iBAAmBA,EAC7CC,EAAeJ,EAAQI,cAAgBA,IA6CpCH,EAAIzB,mBACP,UAAU6B,MAAM,mCAGlB,SAASC,EAAaC,EAAYvB,GAChC,MAAMwB,EAAgBL,EAAgBnB,GAElCwB,GACFD,EAAMD,aAzBV,SAAoBG,EAAeV,GAEjC,GAA2B,IAAvBG,EAAYQ,OAAc,YAAYX,GAG1C,MAAMY,EAlBR,SAASC,EAASC,GAChB,OAAIC,MAAMC,QAAQF,GACTA,EAAIG,IAAIC,GAAOL,EAASK,IAEd,iBAARJ,GAA4B,OAARA,EACtBK,OAAOC,KAAKN,GAAKO,OAAO,CAACC,EAAQC,KACtCD,EAAEC,GAAUV,EAASC,EAAIS,IAClBD,GACN,IAEER,EAQaD,CAASH,GAS7B,OANAP,EAAYqB,QAAQC,IAClB,MAAM1B,EAAW2B,EAAKD,EAAWzB,QAET,IAAbD,EAA0B4B,EAAOF,EAAWb,GAClDgB,EAAIH,EAAW1B,EAAUa,KAEzBA,EAWciB,CAAWrB,EAAMvB,MAAOwB,IAI/C,OAAQD,IAENN,EAAIZ,WAAWN,EAAMC,IACnBsB,EAAaC,EAAOvB,KAItBiB,EAAIL,iBAAiBb,EAAMC,IACzBsB,EAAaC,EAAOvB,KAGtBuB,EAAMsB,UAAU,CAACC,EAAyB9C,KACxC,IAAIC,EAASD,EAGTkB,EAAYQ,OAAS,IACvBzB,EAtEN,SAAsBD,GACpB,MAAM+C,EAAS,GAIf,OAHA7B,EAAYqB,QAAQC,IAClBG,EAAIH,EAAWC,EAAKD,EAAWxC,GAAQ+C,KAElCA,EAiEMC,CAAahD,IAGxBC,EAASmB,EAAanB,GAGlBA,GACFgB,EAAInB,UAAUC,EAAKE"}