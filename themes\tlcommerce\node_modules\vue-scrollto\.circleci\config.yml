version: 2
jobs:
    build:
        docker:
            - image: circleci/node:10

        working_directory: ~/repo

        steps:
            - checkout

            - restore_cache:
                  keys:
                      - v1-dependencies-{{ checksum "package-lock.json" }}
                      - v1-dependencies-

            - run: npm ci

            - save_cache:
                  paths:
                      - node_modules
                      - ~/.cache/npm
                  key: v1-dependencies-{{ checksum "package-lock.json" }}

            - run: npm run semantic-release
