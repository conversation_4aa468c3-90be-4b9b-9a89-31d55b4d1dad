/*!
  * vue-i18n v9.2.2
  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
import{createVNode as e,Text as t,ref as n,computed as r,watch as a,getCurrentInstance as l,Fragment as o,h as s,effectScope as c,inject as u,onMounted as i,onUnmounted as f,shallowRef as m,onBeforeMount as p,isRef as g}from"vue";const _="undefined"!=typeof window,d="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,b=e=>d?Symbol(e):e,v=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),k=e=>"number"==typeof e&&isFinite(e),h=e=>"[object RegExp]"===w(e),L=e=>W(e)&&0===Object.keys(e).length;function y(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const T=Object.assign;function E(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const F=Object.prototype.hasOwnProperty;function N(e,t){return F.call(e,t)}const I=Array.isArray,O=e=>"function"==typeof e,R=e=>"string"==typeof e,P=e=>"boolean"==typeof e,C=e=>null!==e&&"object"==typeof e,M=Object.prototype.toString,w=e=>M.call(e),W=e=>"[object Object]"===w(e),S=15;function x(e,t,n={}){const{domain:r,messages:a,args:l}=n,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=r,o}function D(e){throw e}function A(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const $=String.fromCharCode(8232),U=String.fromCharCode(8233);function H(e){const t=e;let n=0,r=1,a=1,l=0;const o=e=>"\r"===t[e]&&"\n"===t[e+1],s=e=>t[e]===U,c=e=>t[e]===$,u=e=>o(e)||(e=>"\n"===t[e])(e)||s(e)||c(e),i=e=>o(e)||s(e)||c(e)?"\n":t[e];function f(){return l=0,u(n)&&(r++,a=0),o(n)&&n++,n++,a++,t[n]}return{index:()=>n,line:()=>r,column:()=>a,peekOffset:()=>l,charAt:i,currentChar:()=>i(n),currentPeek:()=>i(n+l),next:f,peek:function(){return o(n+l)&&l++,l++,t[n+l]},reset:function(){n=0,r=1,a=1,l=0},resetPeek:function(e=0){l=e},skipToPeek:function(){const e=n+l;for(;e!==n;)f();l=0}}}const j=void 0;function V(e,t={}){const n=!1!==t.location,r=H(e),a=()=>r.index(),l=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},o=l(),s=a(),c={currentType:14,offset:s,startLoc:o,endLoc:o,lastType:14,lastOffset:s,lastStartLoc:o,lastEndLoc:o,braceNest:0,inLinked:!1,text:""},u=()=>c,{onError:i}=t;function f(e,t,r){e.endLoc=l(),e.currentType=t;const a={type:t};return n&&(a.loc=A(e.startLoc,e.endLoc)),null!=r&&(a.value=r),a}const m=e=>f(e,14);function p(e,t){return e.currentChar()===t?(e.next(),t):(l(),"")}function g(e){let t="";for(;" "===e.currentPeek()||"\n"===e.currentPeek();)t+=e.currentPeek(),e.peek();return t}function _(e){const t=g(e);return e.skipToPeek(),t}function d(e){if(e===j)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function b(e,t){const{currentType:n}=t;if(2!==n)return!1;g(e);const r=function(e){if(e===j)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function v(e){g(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function k(e,t=!0){const n=(t=!1,r="",a=!1)=>{const l=e.currentPeek();return"{"===l?"%"!==r&&t:"@"!==l&&l?"%"===l?(e.peek(),n(t,"%",!0)):"|"===l?!("%"!==r&&!a)||!(" "===r||"\n"===r):" "===l?(e.peek(),n(!0," ",a)):"\n"!==l||(e.peek(),n(!0,"\n",a)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function h(e,t){const n=e.currentChar();return n===j?j:t(n)?(e.next(),n):null}function L(e){return h(e,(e=>{const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}))}function y(e){return h(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57}))}function T(e){return h(e,(e=>{const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}))}function E(e){let t="",n="";for(;t=y(e);)n+=t;return n}function F(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!k(e))break;t+=n,e.next()}else if(" "===n||"\n"===n)if(k(e))t+=n,e.next();else{if(v(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function N(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return I(e,t,4);case"U":return I(e,t,6);default:return l(),""}}function I(e,t,n){p(e,t);let r="";for(let t=0;t<n;t++){const t=T(e);if(!t){l(),e.currentChar();break}r+=t}return`\\${t}${r}`}function O(e){_(e);const t=p(e,"|");return _(e),t}function R(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&l(),e.next(),n=f(t,2,"{"),_(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&l(),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&_(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&l(),n=P(e,t)||m(t),t.braceNest=0,n;default:let r=!0,a=!0,o=!0;if(v(e))return t.braceNest>0&&l(),n=f(t,1,O(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return l(),t.braceNest=0,C(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;g(e);const r=d(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){_(e);let t="",n="";for(;t=L(e);)n+=t;return e.currentChar()===j&&l(),n}(e)),_(e),n;if(a=b(e,t))return n=f(t,6,function(e){_(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${E(e)}`):t+=E(e),e.currentChar()===j&&l(),t}(e)),_(e),n;if(o=function(e,t){const{currentType:n}=t;if(2!==n)return!1;g(e);const r="'"===e.currentPeek();return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){_(e),p(e,"'");let t="",n="";const r=e=>"'"!==e&&"\n"!==e;for(;t=h(e,r);)n+="\\"===t?N(e):t;const a=e.currentChar();return"\n"===a||a===j?(l(),"\n"===a&&(e.next(),p(e,"'")),n):(p(e,"'"),n)}(e)),_(e),n;if(!r&&!a&&!o)return n=f(t,13,function(e){_(e);let t="",n="";const r=e=>"{"!==e&&"}"!==e&&" "!==e&&"\n"!==e;for(;t=h(e,r);)n+=t;return n}(e)),l(),n.value,_(e),n}return n}function P(e,t){const{currentType:n}=t;let r=null;const a=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||"\n"!==a&&" "!==a||l(),a){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return _(e),e.next(),f(t,9,".");case":":return _(e),e.next(),f(t,10,":");default:return v(e)?(r=f(t,1,O(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;g(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;g(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(_(e),P(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;g(e);const r=d(e.currentPeek());return e.resetPeek(),r}(e,t)?(_(e),f(t,12,function(e){let t="",n="";for(;t=L(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?d(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||" "===t||!t)&&("\n"===t?(e.peek(),r()):d(t))},a=r();return e.resetPeek(),a}(e,t)?(_(e),"{"===a?R(e,t)||r:f(t,11,function(e){const t=(n=!1,r)=>{const a=e.currentChar();return"{"!==a&&"%"!==a&&"@"!==a&&"|"!==a&&a?" "===a?r:"\n"===a?(r+=a,e.next(),t(n,r)):(r+=a,e.next(),t(!0,r)):r};return t(!1,"")}(e))):(8===n&&l(),t.braceNest=0,t.inLinked=!1,C(e,t))}}function C(e,t){let n={type:14};if(t.braceNest>0)return R(e,t)||m(t);if(t.inLinked)return P(e,t)||m(t);switch(e.currentChar()){case"{":return R(e,t)||m(t);case"}":return l(),e.next(),f(t,3,"}");case"@":return P(e,t)||m(t);default:if(v(e))return n=f(t,1,O(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:a}=function(e){const t=g(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return a?f(t,0,F(e)):f(t,4,function(e){_(e);return"%"!==e.currentChar()&&l(),e.next(),"%"}(e));if(k(e))return f(t,0,F(e))}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:o}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=n,c.lastEndLoc=o,c.offset=a(),c.startLoc=l(),r.currentChar()===j?f(c,14):C(r,c)},currentOffset:a,currentPosition:l,context:u}}const G=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function B(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function Y(e={}){const t=!1!==e.location,{onError:n}=e;function r(e,n,r){const a={type:e,start:n,end:n};return t&&(a.loc={start:r,end:r}),a}function a(e,n,r,a){e.end=n,a&&(e.type=a),t&&e.loc&&(e.loc.end=r)}function l(e,t){const n=e.context(),l=r(3,n.offset,n.startLoc);return l.value=t,a(l,e.currentOffset(),e.currentPosition()),l}function o(e,t){const n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(5,l,o);return s.index=parseInt(t,10),e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function s(e,t){const n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(4,l,o);return s.key=t,e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function c(e,t){const n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(9,l,o);return s.value=t.replace(G,B),e.nextToken(),a(s,e.currentOffset(),e.currentPosition()),s}function u(e){const t=e.context(),n=r(6,t.offset,t.startLoc);let l=e.nextToken();if(9===l.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:l,lastStartLoc:o}=n,s=r(8,l,o);return 12!==t.type?(n.lastStartLoc,s.value="",a(s,l,o),{nextConsumeToken:t,node:s}):(null==t.value&&(n.lastStartLoc,X(t)),s.value=t.value||"",a(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);n.modifier=t.node,l=t.nextConsumeToken||e.nextToken()}switch(10!==l.type&&(t.lastStartLoc,X(l)),l=e.nextToken(),2===l.type&&(l=e.nextToken()),l.type){case 11:null==l.value&&(t.lastStartLoc,X(l)),n.key=function(e,t){const n=e.context(),l=r(7,n.offset,n.startLoc);return l.value=t,a(l,e.currentOffset(),e.currentPosition()),l}(e,l.value||"");break;case 5:null==l.value&&(t.lastStartLoc,X(l)),n.key=s(e,l.value||"");break;case 6:null==l.value&&(t.lastStartLoc,X(l)),n.key=o(e,l.value||"");break;case 7:null==l.value&&(t.lastStartLoc,X(l)),n.key=c(e,l.value||"");break;default:t.lastStartLoc;const u=e.context(),i=r(7,u.offset,u.startLoc);return i.value="",a(i,u.offset,u.startLoc),n.key=i,a(n,u.offset,u.startLoc),{nextConsumeToken:l,node:n}}return a(n,e.currentOffset(),e.currentPosition()),{node:n}}function i(e){const t=e.context(),n=r(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let i=null;do{const r=i||e.nextToken();switch(i=null,r.type){case 0:null==r.value&&(t.lastStartLoc,X(r)),n.items.push(l(e,r.value||""));break;case 6:null==r.value&&(t.lastStartLoc,X(r)),n.items.push(o(e,r.value||""));break;case 5:null==r.value&&(t.lastStartLoc,X(r)),n.items.push(s(e,r.value||""));break;case 7:null==r.value&&(t.lastStartLoc,X(r)),n.items.push(c(e,r.value||""));break;case 8:const a=u(e);n.items.push(a.node),i=a.nextConsumeToken||null}}while(14!==t.currentType&&1!==t.currentType);return a(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function f(e){const t=e.context(),{offset:n,startLoc:l}=t,o=i(e);return 14===t.currentType?o:function(e,t,n,l){const o=e.context();let s=0===l.items.length;const c=r(1,t,n);c.cases=[],c.cases.push(l);do{const t=i(e);s||(s=0===t.items.length),c.cases.push(t)}while(14!==o.currentType);return a(c,e.currentOffset(),e.currentPosition()),c}(e,n,l,o)}return{parse:function(n){const l=V(n,T({},e)),o=l.context(),s=r(0,o.offset,o.startLoc);return t&&s.loc&&(s.loc.source=n),s.body=f(l),14!==o.currentType&&(o.lastStartLoc,n[o.offset]),a(s,l.currentOffset(),l.currentPosition()),s}}}function X(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function z(e,t){for(let n=0;n<e.length;n++)J(e[n],t)}function J(e,t){switch(e.type){case 1:z(e.cases,t),t.helper("plural");break;case 2:z(e.items,t);break;case 6:J(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function q(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&J(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function Z(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?Z(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const a=t.cases.length;for(let n=0;n<a&&(Z(e,t.cases[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const a=t.items.length;for(let n=0;n<a&&(Z(e,t.items[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Z(e,t.key),t.modifier?(e.push(", "),Z(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}function K(e,t={}){const n=T({},t),r=Y(n).parse(e);return q(r,n),((e,t={})=>{const n=R(t.mode)?t.mode:"normal",r=R(t.filename)?t.filename:"message.intl",a=!!t.sourceMap,l=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",o=t.needIndent?t.needIndent:"arrow"!==n,s=e.helpers||[],c=function(e,t){const{sourceMap:n,filename:r,breakLineCode:a,needIndent:l}=t,o={source:e.loc.source,filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:a,needIndent:l,indentLevel:0};function s(e,t){o.code+=e}function c(e,t=!0){const n=t?a:"";s(l?n+"  ".repeat(e):n)}return{context:()=>o,push:s,indent:function(e=!0){const t=++o.indentLevel;e&&c(t)},deindent:function(e=!0){const t=--o.indentLevel;e&&c(t)},newline:function(){c(o.indentLevel)},helper:e=>`_${e}`,needIndent:()=>o.needIndent}}(e,{mode:n,filename:r,sourceMap:a,breakLineCode:l,needIndent:o});c.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(o),s.length>0&&(c.push(`const { ${s.map((e=>`${e}: _${e}`)).join(", ")} } = ctx`),c.newline()),c.push("return "),Z(c,e),c.deindent(o),c.push("}");const{code:u,map:i}=c.context();return{ast:e,code:u,map:i?i.toJSON():void 0}})(r,n)}const Q=[];Q[0]={w:[0],i:[3,0],"[":[4],o:[7]},Q[1]={w:[1],".":[2],"[":[4],o:[7]},Q[2]={w:[2],i:[3,0],0:[3,0]},Q[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},Q[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},Q[5]={"'":[4,0],o:8,l:[5,0]},Q[6]={'"':[4,0],o:8,l:[6,0]};const ee=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function te(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function ne(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,ee.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const re=new Map;function ae(e,t){return C(e)?e[t]:null}const le=e=>e,oe=e=>"",se=e=>0===e.length?"":e.join(""),ce=e=>null==e?"":I(e)||W(e)&&e.toString===M?JSON.stringify(e,null,2):String(e);function ue(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function ie(e={}){const t=e.locale,n=function(e){const t=k(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(k(e.named.count)||k(e.named.n))?k(e.named.count)?e.named.count:k(e.named.n)?e.named.n:t:t}(e),r=C(e.pluralRules)&&R(t)&&O(e.pluralRules[t])?e.pluralRules[t]:ue,a=C(e.pluralRules)&&R(t)&&O(e.pluralRules[t])?ue:void 0,l=e.list||[],o=e.named||{};k(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,o);function s(t){const n=O(e.messages)?e.messages(t):!!C(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):oe)}const c=W(e.processor)&&O(e.processor.normalize)?e.processor.normalize:se,u=W(e.processor)&&O(e.processor.interpolate)?e.processor.interpolate:ce,i={list:e=>l[e],named:e=>o[e],plural:e=>e[r(n,e.length,a)],linked:(t,...n)=>{const[r,a]=n;let l="text",o="";1===n.length?C(r)?(o=r.modifier||o,l=r.type||l):R(r)&&(o=r||o):2===n.length&&(R(r)&&(o=r||o),R(a)&&(l=a||l));let c=s(t)(i);return"vnode"===l&&I(c)&&o&&(c=c[0]),o?(u=o,e.modifiers?e.modifiers[u]:le)(c,l):c;var u},message:s,type:W(e.processor)&&R(e.processor.type)?e.processor.type:"text",interpolate:u,normalize:c};return i}function fe(e,t,n){return[...new Set([n,...I(t)?t:C(t)?Object.keys(t):R(t)?[t]:[n]])]}function me(e,t,n){const r=R(n)?n:de,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let l=a.__localeChainCache.get(r);if(!l){l=[];let e=[n];for(;I(e);)e=pe(l,e,t);const o=I(t)||!W(t)?t:t.default?t.default:null;e=R(o)?[o]:o,I(e)&&pe(l,e,!1),a.__localeChainCache.set(r,l)}return l}function pe(e,t,n){let r=!0;for(let a=0;a<t.length&&P(r);a++){const l=t[a];R(l)&&(r=ge(e,t[a],n))}return r}function ge(e,t,n){let r;const a=t.split("-");do{r=_e(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function _e(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(I(n)||W(n))&&n[a]&&(r=n[a])}return r}const de="en-US",be=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let ve,ke,he;let Le=0;function ye(e={}){const t=R(e.version)?e.version:"9.2.2",n=R(e.locale)?e.locale:de,r=I(e.fallbackLocale)||W(e.fallbackLocale)||R(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:n,a=W(e.messages)?e.messages:{[n]:{}},l=W(e.datetimeFormats)?e.datetimeFormats:{[n]:{}},o=W(e.numberFormats)?e.numberFormats:{[n]:{}},s=T({},e.modifiers||{},{upper:(e,t)=>"text"===t&&R(e)?e.toUpperCase():"vnode"===t&&C(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&R(e)?e.toLowerCase():"vnode"===t&&C(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&R(e)?be(e):"vnode"===t&&C(e)&&"__v_isVNode"in e?be(e.children):e}),c=e.pluralRules||{},u=O(e.missing)?e.missing:null,i=!P(e.missingWarn)&&!h(e.missingWarn)||e.missingWarn,f=!P(e.fallbackWarn)&&!h(e.fallbackWarn)||e.fallbackWarn,m=!!e.fallbackFormat,p=!!e.unresolving,g=O(e.postTranslation)?e.postTranslation:null,_=W(e.processor)?e.processor:null,d=!P(e.warnHtmlMessage)||e.warnHtmlMessage,b=!!e.escapeParameter,v=O(e.messageCompiler)?e.messageCompiler:ve,k=O(e.messageResolver)?e.messageResolver:ke||ae,L=O(e.localeFallbacker)?e.localeFallbacker:he||fe,E=C(e.fallbackContext)?e.fallbackContext:void 0,F=O(e.onWarn)?e.onWarn:y,N=e,M=C(N.__datetimeFormatters)?N.__datetimeFormatters:new Map,w=C(N.__numberFormatters)?N.__numberFormatters:new Map,S=C(N.__meta)?N.__meta:{};Le++;const x={version:t,cid:Le,locale:n,fallbackLocale:r,messages:a,modifiers:s,pluralRules:c,missing:u,missingWarn:i,fallbackWarn:f,fallbackFormat:m,unresolving:p,postTranslation:g,processor:_,warnHtmlMessage:d,escapeParameter:b,messageCompiler:v,messageResolver:k,localeFallbacker:L,fallbackContext:E,onWarn:F,__meta:S};return x.datetimeFormats=l,x.numberFormats=o,x.__datetimeFormatters=M,x.__numberFormatters=w,x}function Te(e,t,n,r,a){const{missing:l,onWarn:o}=e;if(null!==l){const r=l(e,n,t,a);return R(r)?r:t}return t}function Ee(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}const Fe=e=>e;let Ne=Object.create(null);let Ie=S;const Oe=()=>++Ie,Re={INVALID_ARGUMENT:Ie,INVALID_DATE_ARGUMENT:Oe(),INVALID_ISO_DATE_ARGUMENT:Oe(),__EXTEND_POINT__:Oe()},Pe=()=>"",Ce=e=>O(e);function Me(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:a,messageCompiler:l,fallbackLocale:o,messages:s}=e,[c,u]=Se(...t),i=P(u.missingWarn)?u.missingWarn:e.missingWarn,f=P(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,m=P(u.escapeParameter)?u.escapeParameter:e.escapeParameter,p=!!u.resolvedMessage,g=R(u.default)||P(u.default)?P(u.default)?l?c:()=>c:u.default:n?l?c:()=>c:"",_=n||""!==g,d=R(u.locale)?u.locale:e.locale;m&&function(e){I(e.list)?e.list=e.list.map((e=>R(e)?E(e):e)):C(e.named)&&Object.keys(e.named).forEach((t=>{R(e.named[t])&&(e.named[t]=E(e.named[t]))}))}(u);let[b,v,h]=p?[c,d,s[d]||{}]:we(e,c,d,o,f,i),L=b,y=c;if(p||R(L)||Ce(L)||_&&(L=g,y=L),!(p||(R(L)||Ce(L))&&R(v)))return a?-1:c;let T=!1;const F=Ce(L)?L:We(e,c,v,L,y,(()=>{T=!0}));if(T)return L;const N=function(e,t,n,r){const{modifiers:a,pluralRules:l,messageResolver:o,fallbackLocale:s,fallbackWarn:c,missingWarn:u,fallbackContext:i}=e,f=r=>{let a=o(n,r);if(null==a&&i){const[,,e]=we(i,r,t,s,c,u);a=o(e,r)}if(R(a)){let n=!1;const l=We(e,r,t,a,r,(()=>{n=!0}));return n?Pe:l}return Ce(a)?a:Pe},m={locale:t,modifiers:a,pluralRules:l,messages:f};e.processor&&(m.processor=e.processor);r.list&&(m.list=r.list);r.named&&(m.named=r.named);k(r.plural)&&(m.pluralIndex=r.plural);return m}(e,v,h,u),O=function(e,t,n){return t(n)}(0,F,ie(N));return r?r(O,c):O}function we(e,t,n,r,a,l){const{messages:o,onWarn:s,messageResolver:c,localeFallbacker:u}=e,i=u(e,r,n);let f,m={},p=null;for(let n=0;n<i.length&&(f=i[n],m=o[f]||{},null===(p=c(m,t))&&(p=m[t]),!R(p)&&!O(p));n++){const n=Te(e,t,f,0,"translate");n!==t&&(p=n)}return[p,f,m]}function We(e,t,n,r,a,l){const{messageCompiler:o,warnHtmlMessage:s}=e;if(Ce(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==o){const e=()=>r;return e.locale=n,e.key=t,e}const c=o(r,function(e,t,n,r,a,l){return{warnHtmlMessage:a,onError:e=>{throw l&&l(e),e},onCacheKey:e=>((e,t,n)=>v({l:e,k:t,s:n}))(t,n,e)}}(0,n,a,0,s,l));return c.locale=n,c.key=t,c.source=r,c}function Se(...e){const[t,n,r]=e,a={};if(!R(t)&&!k(t)&&!Ce(t))throw Error(Re.INVALID_ARGUMENT);const l=k(t)?String(t):(Ce(t),t);return k(n)?a.plural=n:R(n)?a.default=n:W(n)&&!L(n)?a.named=n:I(n)&&(a.list=n),k(r)?a.plural=r:R(r)?a.default=r:W(r)&&T(a,r),[l,a]}function xe(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a,onWarn:l,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[c,u,i,f]=Ae(...t);P(i.missingWarn)?i.missingWarn:e.missingWarn;P(i.fallbackWarn)?i.fallbackWarn:e.fallbackWarn;const m=!!i.part,p=R(i.locale)?i.locale:e.locale,g=o(e,a,p);if(!R(c)||""===c)return new Intl.DateTimeFormat(p,f).format(u);let _,d={},b=null;for(let t=0;t<g.length&&(_=g[t],d=n[_]||{},b=d[c],!W(b));t++)Te(e,c,_,0,"datetime format");if(!W(b)||!R(_))return r?-1:c;let v=`${_}__${c}`;L(f)||(v=`${v}__${JSON.stringify(f)}`);let k=s.get(v);return k||(k=new Intl.DateTimeFormat(_,T({},b,f)),s.set(v,k)),m?k.formatToParts(u):k.format(u)}const De=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function Ae(...e){const[t,n,r,a]=e,l={};let o,s={};if(R(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(Re.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();o=new Date(n);try{o.toISOString()}catch(e){throw Error(Re.INVALID_ISO_DATE_ARGUMENT)}}else if("[object Date]"===w(t)){if(isNaN(t.getTime()))throw Error(Re.INVALID_DATE_ARGUMENT);o=t}else{if(!k(t))throw Error(Re.INVALID_ARGUMENT);o=t}return R(n)?l.key=n:W(n)&&Object.keys(n).forEach((e=>{De.includes(e)?s[e]=n[e]:l[e]=n[e]})),R(r)?l.locale=r:W(r)&&(s=r),W(a)&&(s=a),[l.key||"",o,l,s]}function $e(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__datetimeFormatters.has(n)&&r.__datetimeFormatters.delete(n)}}function Ue(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a,onWarn:l,localeFallbacker:o}=e,{__numberFormatters:s}=e,[c,u,i,f]=je(...t);P(i.missingWarn)?i.missingWarn:e.missingWarn;P(i.fallbackWarn)?i.fallbackWarn:e.fallbackWarn;const m=!!i.part,p=R(i.locale)?i.locale:e.locale,g=o(e,a,p);if(!R(c)||""===c)return new Intl.NumberFormat(p,f).format(u);let _,d={},b=null;for(let t=0;t<g.length&&(_=g[t],d=n[_]||{},b=d[c],!W(b));t++)Te(e,c,_,0,"number format");if(!W(b)||!R(_))return r?-1:c;let v=`${_}__${c}`;L(f)||(v=`${v}__${JSON.stringify(f)}`);let k=s.get(v);return k||(k=new Intl.NumberFormat(_,T({},b,f)),s.set(v,k)),m?k.formatToParts(u):k.format(u)}const He=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function je(...e){const[t,n,r,a]=e,l={};let o={};if(!k(t))throw Error(Re.INVALID_ARGUMENT);const s=t;return R(n)?l.key=n:W(n)&&Object.keys(n).forEach((e=>{He.includes(e)?o[e]=n[e]:l[e]=n[e]})),R(r)?l.locale=r:W(r)&&(o=r),W(a)&&(o=a),[l.key||"",s,l,o]}function Ve(e,t,n){const r=e;for(const e in n){const n=`${t}__${e}`;r.__numberFormatters.has(n)&&r.__numberFormatters.delete(n)}}const Ge="9.2.2";let Be=S;const Ye=()=>++Be,Xe={UNEXPECTED_RETURN_TYPE:Be,INVALID_ARGUMENT:Ye(),MUST_BE_CALL_SETUP_TOP:Ye(),NOT_INSLALLED:Ye(),NOT_AVAILABLE_IN_LEGACY_MODE:Ye(),REQUIRED_VALUE:Ye(),INVALID_VALUE:Ye(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Ye(),NOT_INSLALLED_WITH_PROVIDE:Ye(),UNEXPECTED_ERROR:Ye(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Ye(),BRIDGE_SUPPORT_VUE_2_ONLY:Ye(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Ye(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Ye(),__EXTEND_POINT__:Ye()};const ze=b("__transrateVNode"),Je=b("__datetimeParts"),qe=b("__numberParts"),Ze=b("__setPluralRules"),Ke=b("__injectWithOption");function Qe(e){if(!C(e))return e;for(const t in e)if(N(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let a=e;for(let e=0;e<r;e++)n[e]in a||(a[n[e]]={}),a=a[n[e]];a[n[r]]=e[t],delete e[t],C(a[n[r]])&&Qe(a[n[r]])}else C(e[t])&&Qe(e[t]);return e}function et(e,t){const{messages:n,__i18n:r,messageResolver:a,flatJson:l}=t,o=W(n)?n:I(r)?{}:{[e]:{}};if(I(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(o[t]=o[t]||{},nt(n,o[t])):nt(n,o)}else R(e)&&nt(JSON.parse(e),o)})),null==a&&l)for(const e in o)N(o,e)&&Qe(o[e]);return o}const tt=e=>!C(e)||I(e);function nt(e,t){if(tt(e)||tt(t))throw Error(Xe.INVALID_VALUE);for(const n in e)N(e,n)&&(tt(e[n])||tt(t[n])?t[n]=e[n]:nt(e[n],t[n]))}function rt(e,t,n){let r=C(t.messages)?t.messages:{};"__i18nGlobal"in n&&(r=et(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const a=Object.keys(r);if(a.length&&a.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),C(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(C(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function at(n){return e(t,null,n,0)}let lt=0;function ot(e){return(t,n,r,a)=>e(n,r,l()||void 0,a)}function st(e={},t){const{__root:l}=e,o=void 0===l;let s=!P(e.inheritLocale)||e.inheritLocale;const c=n(l&&s?l.locale.value:R(e.locale)?e.locale:de),u=n(l&&s?l.fallbackLocale.value:R(e.fallbackLocale)||I(e.fallbackLocale)||W(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:c.value),i=n(et(c.value,e)),f=n(W(e.datetimeFormats)?e.datetimeFormats:{[c.value]:{}}),m=n(W(e.numberFormats)?e.numberFormats:{[c.value]:{}});let p=l?l.missingWarn:!P(e.missingWarn)&&!h(e.missingWarn)||e.missingWarn,g=l?l.fallbackWarn:!P(e.fallbackWarn)&&!h(e.fallbackWarn)||e.fallbackWarn,d=l?l.fallbackRoot:!P(e.fallbackRoot)||e.fallbackRoot,b=!!e.fallbackFormat,v=O(e.missing)?e.missing:null,L=O(e.missing)?ot(e.missing):null,y=O(e.postTranslation)?e.postTranslation:null,E=l?l.warnHtmlMessage:!P(e.warnHtmlMessage)||e.warnHtmlMessage,F=!!e.escapeParameter;const N=l?l.modifiers:W(e.modifiers)?e.modifiers:{};let M,w=e.pluralRules||l&&l.pluralRules;M=(()=>{const t={version:"9.2.2",locale:c.value,fallbackLocale:u.value,messages:i.value,modifiers:N,pluralRules:w,missing:null===L?void 0:L,missingWarn:p,fallbackWarn:g,fallbackFormat:b,unresolving:!0,postTranslation:null===y?void 0:y,warnHtmlMessage:E,escapeParameter:F,messageResolver:e.messageResolver,__meta:{framework:"vue"}};t.datetimeFormats=f.value,t.numberFormats=m.value,t.__datetimeFormatters=W(M)?M.__datetimeFormatters:void 0,t.__numberFormatters=W(M)?M.__numberFormatters:void 0;return ye(t)})(),Ee(M,c.value,u.value);const S=r({get:()=>c.value,set:e=>{c.value=e,M.locale=c.value}}),x=r({get:()=>u.value,set:e=>{u.value=e,M.fallbackLocale=u.value,Ee(M,c.value,e)}}),D=r((()=>i.value)),A=r((()=>f.value)),$=r((()=>m.value));const U=(e,t,n,r,a,o)=>{let s;if(c.value,u.value,i.value,f.value,m.value,s=e(M),k(s)&&-1===s){const[e,n]=t();return l&&d?r(l):a(e)}if(o(s))return s;throw Error(Xe.UNEXPECTED_RETURN_TYPE)};function H(...e){return U((t=>Reflect.apply(Me,null,[t,...e])),(()=>Se(...e)),0,(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>R(e)))}const j={normalize:function(e){return e.map((e=>R(e)||k(e)||P(e)?at(String(e)):e))},interpolate:e=>e,type:"vnode"};function V(e){return i.value[e]||{}}lt++,l&&_&&(a(l.locale,(e=>{s&&(c.value=e,M.locale=e,Ee(M,c.value,u.value))})),a(l.fallbackLocale,(e=>{s&&(u.value=e,M.fallbackLocale=e,Ee(M,c.value,u.value))})));const G={id:lt,locale:S,fallbackLocale:x,get inheritLocale(){return s},set inheritLocale(e){s=e,e&&l&&(c.value=l.locale.value,u.value=l.fallbackLocale.value,Ee(M,c.value,u.value))},get availableLocales(){return Object.keys(i.value).sort()},messages:D,get modifiers(){return N},get pluralRules(){return w||{}},get isGlobal(){return o},get missingWarn(){return p},set missingWarn(e){p=e,M.missingWarn=p},get fallbackWarn(){return g},set fallbackWarn(e){g=e,M.fallbackWarn=g},get fallbackRoot(){return d},set fallbackRoot(e){d=e},get fallbackFormat(){return b},set fallbackFormat(e){b=e,M.fallbackFormat=b},get warnHtmlMessage(){return E},set warnHtmlMessage(e){E=e,M.warnHtmlMessage=e},get escapeParameter(){return F},set escapeParameter(e){F=e,M.escapeParameter=e},t:H,getLocaleMessage:V,setLocaleMessage:function(e,t){i.value[e]=t,M.messages=i.value},mergeLocaleMessage:function(e,t){i.value[e]=i.value[e]||{},nt(t,i.value[e]),M.messages=i.value},getPostTranslationHandler:function(){return O(y)?y:null},setPostTranslationHandler:function(e){y=e,M.postTranslation=e},getMissingHandler:function(){return v},setMissingHandler:function(e){null!==e&&(L=ot(e)),v=e,M.missing=L},[Ze]:function(e){w=e,M.pluralRules=w}};return G.datetimeFormats=A,G.numberFormats=$,G.rt=function(...e){const[t,n,r]=e;if(r&&!C(r))throw Error(Xe.INVALID_ARGUMENT);return H(t,n,T({resolvedMessage:!0},r||{}))},G.te=function(e,t){const n=V(R(t)?t:c.value);return null!==M.messageResolver(n,e)},G.tm=function(e){const t=function(e){let t=null;const n=me(M,u.value,c.value);for(let r=0;r<n.length;r++){const a=i.value[n[r]]||{},l=M.messageResolver(a,e);if(null!=l){t=l;break}}return t}(e);return null!=t?t:l&&l.tm(e)||{}},G.d=function(...e){return U((t=>Reflect.apply(xe,null,[t,...e])),(()=>Ae(...e)),0,(t=>Reflect.apply(t.d,t,[...e])),(()=>""),(e=>R(e)))},G.n=function(...e){return U((t=>Reflect.apply(Ue,null,[t,...e])),(()=>je(...e)),0,(t=>Reflect.apply(t.n,t,[...e])),(()=>""),(e=>R(e)))},G.getDateTimeFormat=function(e){return f.value[e]||{}},G.setDateTimeFormat=function(e,t){f.value[e]=t,M.datetimeFormats=f.value,$e(M,e,t)},G.mergeDateTimeFormat=function(e,t){f.value[e]=T(f.value[e]||{},t),M.datetimeFormats=f.value,$e(M,e,t)},G.getNumberFormat=function(e){return m.value[e]||{}},G.setNumberFormat=function(e,t){m.value[e]=t,M.numberFormats=m.value,Ve(M,e,t)},G.mergeNumberFormat=function(e,t){m.value[e]=T(m.value[e]||{},t),M.numberFormats=m.value,Ve(M,e,t)},G[Ke]=e.__injectWithOption,G[ze]=function(...e){return U((t=>{let n;const r=t;try{r.processor=j,n=Reflect.apply(Me,null,[r,...e])}finally{r.processor=null}return n}),(()=>Se(...e)),0,(t=>t[ze](...e)),(e=>[at(e)]),(e=>I(e)))},G[Je]=function(...e){return U((t=>Reflect.apply(xe,null,[t,...e])),(()=>Ae(...e)),0,(t=>t[Je](...e)),(()=>[]),(e=>R(e)||I(e)))},G[qe]=function(...e){return U((t=>Reflect.apply(Ue,null,[t,...e])),(()=>je(...e)),0,(t=>t[qe](...e)),(()=>[]),(e=>R(e)||I(e)))},G}function ct(e={},t){{const t=st(function(e){const t=R(e.locale)?e.locale:de,n=R(e.fallbackLocale)||I(e.fallbackLocale)||W(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=O(e.missing)?e.missing:void 0,a=!P(e.silentTranslationWarn)&&!h(e.silentTranslationWarn)||!e.silentTranslationWarn,l=!P(e.silentFallbackWarn)&&!h(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!P(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,c=W(e.modifiers)?e.modifiers:{},u=e.pluralizationRules,i=O(e.postTranslation)?e.postTranslation:void 0,f=!R(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,m=!!e.escapeParameterHtml,p=!P(e.sync)||e.sync;let g=e.messages;if(W(e.sharedMessages)){const t=e.sharedMessages;g=Object.keys(t).reduce(((e,n)=>{const r=e[n]||(e[n]={});return T(r,t[n]),e}),g||{})}const{__i18n:_,__root:d,__injectWithOption:b}=e,v=e.datetimeFormats,k=e.numberFormats;return{locale:t,fallbackLocale:n,messages:g,flatJson:e.flatJson,datetimeFormats:v,numberFormats:k,missing:r,missingWarn:a,fallbackWarn:l,fallbackRoot:o,fallbackFormat:s,modifiers:c,pluralRules:u,postTranslation:i,warnHtmlMessage:f,escapeParameter:m,messageResolver:e.messageResolver,inheritLocale:p,__i18n:_,__root:d,__injectWithOption:b}}(e)),n={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return P(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=P(e)?!e:e},get silentFallbackWarn(){return P(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=P(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[n,r,a]=e,l={};let o=null,s=null;if(!R(n))throw Error(Xe.INVALID_ARGUMENT);const c=n;return R(r)?l.locale=r:I(r)?o=r:W(r)&&(s=r),I(a)?o=a:W(a)&&(s=a),Reflect.apply(t.t,t,[c,o||s||{},l])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[n,r,a]=e,l={plural:1};let o=null,s=null;if(!R(n))throw Error(Xe.INVALID_ARGUMENT);const c=n;return R(r)?l.locale=r:k(r)?l.plural=r:I(r)?o=r:W(r)&&(s=r),R(a)?l.locale=a:I(a)?o=a:W(a)&&(s=a),Reflect.apply(t.t,t,[c,o||s||{},l])},te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)},getChoiceIndex:(e,t)=>-1,__onComponentInstanceCreated(t){const{componentInstanceCreatedListener:r}=e;r&&r(t,n)}};return n}}const ut={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function it(e){return o}const ft={name:"i18n-t",props:T({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>k(e)||!isNaN(e)}},ut),setup(e,t){const{slots:n,attrs:r}=t,a=e.i18n||Lt({useScope:e.scope,__useComponent:!0});return()=>{const l=Object.keys(n).filter((e=>"_"!==e)),o={};e.locale&&(o.locale=e.locale),void 0!==e.plural&&(o.plural=R(e.plural)?+e.plural:e.plural);const c=function({slots:e},t){if(1===t.length&&"default"===t[0])return(e.default?e.default():[]).reduce(((e,t)=>[...e,...I(t.children)?t.children:[t]]),[]);return t.reduce(((t,n)=>{const r=e[n];return r&&(t[n]=r()),t}),{})}(t,l),u=a[ze](e.keypath,c,o),i=T({},r),f=R(e.tag)||C(e.tag)?e.tag:it();return s(f,i,u)}}};function mt(e,t,n,r){const{slots:a,attrs:l}=t;return()=>{const t={part:!0};let o={};e.locale&&(t.locale=e.locale),R(e.format)?t.key=e.format:C(e.format)&&(R(e.format.key)&&(t.key=e.format.key),o=Object.keys(e.format).reduce(((t,r)=>n.includes(r)?T({},t,{[r]:e.format[r]}):t),{}));const c=r(e.value,t,o);let u=[t.key];I(c)?u=c.map(((e,t)=>{const n=a[e.type],r=n?n({[e.type]:e.value,index:t,parts:c}):[e.value];var l;return I(l=r)&&!R(l[0])&&(r[0].key=`${e.type}-${t}`),r})):R(c)&&(u=[c]);const i=T({},l),f=R(e.tag)||C(e.tag)?e.tag:it();return s(f,i,u)}}const pt={name:"i18n-n",props:T({value:{type:Number,required:!0},format:{type:[String,Object]}},ut),setup(e,t){const n=e.i18n||Lt({useScope:"parent",__useComponent:!0});return mt(e,t,He,((...e)=>n[qe](...e)))}},gt={name:"i18n-d",props:T({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},ut),setup(e,t){const n=e.i18n||Lt({useScope:"parent",__useComponent:!0});return mt(e,t,De,((...e)=>n[Je](...e)))}};function _t(e){const t=t=>{const{instance:n,modifiers:r,value:a}=t;if(!n||!n.$)throw Error(Xe.UNEXPECTED_ERROR);const l=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),o=dt(a);return[Reflect.apply(l.t,l,[...bt(o)]),l]};return{created:(n,r)=>{const[l,o]=t(r);_&&e.global===o&&(n.__i18nWatcher=a(o.locale,(()=>{r.instance&&r.instance.$forceUpdate()}))),n.__composer=o,n.textContent=l},unmounted:e=>{_&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=dt(t);e.textContent=Reflect.apply(n.t,n,[...bt(r)])}},getSSRProps:e=>{const[n]=t(e);return{textContent:n}}}}function dt(e){if(R(e))return{path:e};if(W(e)){if(!("path"in e))throw Error(Xe.REQUIRED_VALUE,"path");return e}throw Error(Xe.INVALID_VALUE)}function bt(e){const{path:t,locale:n,args:r,choice:a,plural:l}=e,o={},s=r||{};return R(n)&&(o.locale=n),k(a)&&(o.plural=a),k(l)&&(o.plural=l),[t,s,o]}function vt(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Ze](t.pluralizationRules||e.pluralizationRules);const n=et(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const kt=b("global-vue-i18n");function ht(e={},t){const n=!P(e.legacy)||e.legacy,r=!P(e.globalInjection)||e.globalInjection,a=!n||!!e.allowComposition,o=new Map,[s,u]=function(e,t,n){const r=c();{const n=t?r.run((()=>ct(e))):r.run((()=>st(e)));if(null==n)throw Error(Xe.UNEXPECTED_ERROR);return[r,n]}}(e,n),i=b("");{const e={get mode(){return n?"legacy":"composition"},get allowComposition(){return a},async install(t,...a){t.__VUE_I18N_SYMBOL__=i,t.provide(t.__VUE_I18N_SYMBOL__,e),!n&&r&&function(e,t){const n=Object.create(null);Tt.forEach((e=>{const r=Object.getOwnPropertyDescriptor(t,e);if(!r)throw Error(Xe.UNEXPECTED_ERROR);const a=g(r.value)?{get:()=>r.value.value,set(e){r.value.value=e}}:{get:()=>r.get&&r.get()};Object.defineProperty(n,e,a)})),e.config.globalProperties.$i18n=n,Et.forEach((n=>{const r=Object.getOwnPropertyDescriptor(t,n);if(!r||!r.value)throw Error(Xe.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${n}`,r)}))}(t,e.global),function(e,t,...n){const r=W(n[0])?n[0]:{},a=!!r.useI18nComponentName;(!P(r.globalInstall)||r.globalInstall)&&(e.component(a?"i18n":ft.name,ft),e.component(pt.name,pt),e.component(gt.name,gt)),e.directive("t",_t(t))}(t,e,...a),n&&t.mixin(function(e,t,n){return{beforeCreate(){const r=l();if(!r)throw Error(Xe.UNEXPECTED_ERROR);const a=this.$options;if(a.i18n){const n=a.i18n;a.__i18n&&(n.__i18n=a.__i18n),n.__root=t,this===this.$root?this.$i18n=vt(e,n):(n.__injectWithOption=!0,this.$i18n=ct(n))}else a.__i18n?this===this.$root?this.$i18n=vt(e,a):this.$i18n=ct({__i18n:a.__i18n,__injectWithOption:!0,__root:t}):this.$i18n=e;a.__i18nGlobal&&rt(t,a,a),e.__onComponentInstanceCreated(this.$i18n),n.__setInstance(r,this.$i18n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e)},mounted(){},unmounted(){const e=l();if(!e)throw Error(Xe.UNEXPECTED_ERROR);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__deleteInstance(e),delete this.$i18n}}}(u,u.__composer,e));const o=t.unmount;t.unmount=()=>{e.dispose(),o()}},get global(){return u},dispose(){s.stop()},__instances:o,__getInstance:function(e){return o.get(e)||null},__setInstance:function(e,t){o.set(e,t)},__deleteInstance:function(e){o.delete(e)}};return e}}function Lt(e={}){const t=l();if(null==t)throw Error(Xe.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Error(Xe.NOT_INSLALLED);const a=function(e){{const t=u(e.isCE?kt:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw function(e,...t){return x(e,null,void 0)}(e.isCE?Xe.NOT_INSLALLED_WITH_PROVIDE:Xe.UNEXPECTED_ERROR);return t}}(t),o=function(e){return"composition"===e.mode?e.global:e.global.__composer}(a),s=function(e){return e.type}(t),c=function(e,t){return L(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,s);if("legacy"===a.mode&&!e.__useComponent){if(!a.allowComposition)throw Error(Xe.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,t,a,l={}){const o="local"===t,s=m(null);if(o&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(Xe.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const c=!P(l.inheritLocale)||l.inheritLocale,u=n(o&&c?a.locale.value:R(l.locale)?l.locale:de),i=n(o&&c?a.fallbackLocale.value:R(l.fallbackLocale)||I(l.fallbackLocale)||W(l.fallbackLocale)||!1===l.fallbackLocale?l.fallbackLocale:u.value),f=n(et(u.value,l)),g=n(W(l.datetimeFormats)?l.datetimeFormats:{[u.value]:{}}),_=n(W(l.numberFormats)?l.numberFormats:{[u.value]:{}}),d=o?a.missingWarn:!P(l.missingWarn)&&!h(l.missingWarn)||l.missingWarn,b=o?a.fallbackWarn:!P(l.fallbackWarn)&&!h(l.fallbackWarn)||l.fallbackWarn,v=o?a.fallbackRoot:!P(l.fallbackRoot)||l.fallbackRoot,k=!!l.fallbackFormat,L=O(l.missing)?l.missing:null,y=O(l.postTranslation)?l.postTranslation:null,T=o?a.warnHtmlMessage:!P(l.warnHtmlMessage)||l.warnHtmlMessage,E=!!l.escapeParameter,F=o?a.modifiers:W(l.modifiers)?l.modifiers:{},N=l.pluralRules||o&&a.pluralRules;function C(){return[u.value,i.value,f.value,g.value,_.value]}const M=r({get:()=>s.value?s.value.locale.value:u.value,set:e=>{s.value&&(s.value.locale.value=e),u.value=e}}),w=r({get:()=>s.value?s.value.fallbackLocale.value:i.value,set:e=>{s.value&&(s.value.fallbackLocale.value=e),i.value=e}}),S=r((()=>s.value?s.value.messages.value:f.value)),x=r((()=>g.value)),D=r((()=>_.value));function A(){return s.value?s.value.getPostTranslationHandler():y}function $(e){s.value&&s.value.setPostTranslationHandler(e)}function U(){return s.value?s.value.getMissingHandler():L}function H(e){s.value&&s.value.setMissingHandler(e)}function j(e){return C(),e()}function V(...e){return s.value?j((()=>Reflect.apply(s.value.t,null,[...e]))):j((()=>""))}function G(...e){return s.value?Reflect.apply(s.value.rt,null,[...e]):""}function B(...e){return s.value?j((()=>Reflect.apply(s.value.d,null,[...e]))):j((()=>""))}function Y(...e){return s.value?j((()=>Reflect.apply(s.value.n,null,[...e]))):j((()=>""))}function X(e){return s.value?s.value.tm(e):{}}function z(e,t){return!!s.value&&s.value.te(e,t)}function J(e){return s.value?s.value.getLocaleMessage(e):{}}function q(e,t){s.value&&(s.value.setLocaleMessage(e,t),f.value[e]=t)}function Z(e,t){s.value&&s.value.mergeLocaleMessage(e,t)}function K(e){return s.value?s.value.getDateTimeFormat(e):{}}function Q(e,t){s.value&&(s.value.setDateTimeFormat(e,t),g.value[e]=t)}function ee(e,t){s.value&&s.value.mergeDateTimeFormat(e,t)}function te(e){return s.value?s.value.getNumberFormat(e):{}}function ne(e,t){s.value&&(s.value.setNumberFormat(e,t),_.value[e]=t)}function re(e,t){s.value&&s.value.mergeNumberFormat(e,t)}const ae={get id(){return s.value?s.value.id:-1},locale:M,fallbackLocale:w,messages:S,datetimeFormats:x,numberFormats:D,get inheritLocale(){return s.value?s.value.inheritLocale:c},set inheritLocale(e){s.value&&(s.value.inheritLocale=e)},get availableLocales(){return s.value?s.value.availableLocales:Object.keys(f.value)},get modifiers(){return s.value?s.value.modifiers:F},get pluralRules(){return s.value?s.value.pluralRules:N},get isGlobal(){return!!s.value&&s.value.isGlobal},get missingWarn(){return s.value?s.value.missingWarn:d},set missingWarn(e){s.value&&(s.value.missingWarn=e)},get fallbackWarn(){return s.value?s.value.fallbackWarn:b},set fallbackWarn(e){s.value&&(s.value.missingWarn=e)},get fallbackRoot(){return s.value?s.value.fallbackRoot:v},set fallbackRoot(e){s.value&&(s.value.fallbackRoot=e)},get fallbackFormat(){return s.value?s.value.fallbackFormat:k},set fallbackFormat(e){s.value&&(s.value.fallbackFormat=e)},get warnHtmlMessage(){return s.value?s.value.warnHtmlMessage:T},set warnHtmlMessage(e){s.value&&(s.value.warnHtmlMessage=e)},get escapeParameter(){return s.value?s.value.escapeParameter:E},set escapeParameter(e){s.value&&(s.value.escapeParameter=e)},t:V,getPostTranslationHandler:A,setPostTranslationHandler:$,getMissingHandler:U,setMissingHandler:H,rt:G,d:B,n:Y,tm:X,te:z,getLocaleMessage:J,setLocaleMessage:q,mergeLocaleMessage:Z,getDateTimeFormat:K,setDateTimeFormat:Q,mergeDateTimeFormat:ee,getNumberFormat:te,setNumberFormat:ne,mergeNumberFormat:re};function le(e){e.locale.value=u.value,e.fallbackLocale.value=i.value,Object.keys(f.value).forEach((t=>{e.mergeLocaleMessage(t,f.value[t])})),Object.keys(g.value).forEach((t=>{e.mergeDateTimeFormat(t,g.value[t])})),Object.keys(_.value).forEach((t=>{e.mergeNumberFormat(t,_.value[t])})),e.escapeParameter=E,e.fallbackFormat=k,e.fallbackRoot=v,e.fallbackWarn=b,e.missingWarn=d,e.warnHtmlMessage=T}return p((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(Xe.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const n=s.value=e.proxy.$i18n.__composer;"global"===t?(u.value=n.locale.value,i.value=n.fallbackLocale.value,f.value=n.messages.value,g.value=n.datetimeFormats.value,_.value=n.numberFormats.value):o&&le(n)})),ae}(t,c,o,e)}if("global"===c)return rt(o,e,s),o;if("parent"===c){let n=function(e,t,n=!1){let r=null;const a=t.root;let l=t.parent;for(;null!=l;){const t=e;if("composition"===e.mode)r=t.__getInstance(l);else{const e=t.__getInstance(l);null!=e&&(r=e.__composer,n&&r&&!r[Ke]&&(r=null))}if(null!=r)break;if(a===l)break;l=l.parent}return r}(a,t,e.__useComponent);return null==n&&(n=o),n}const g=a;let _=g.__getInstance(t);if(null==_){const n=T({},e);"__i18n"in s&&(n.__i18n=s.__i18n),o&&(n.__root=o),_=st(n),function(e,t,n){i((()=>{}),t),f((()=>{e.__deleteInstance(t)}),t)}(g,t),g.__setInstance(t,_)}return _}const yt=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(Xe.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e};const Tt=["locale","fallbackLocale","availableLocales"],Et=["t","rt","d","n","tm"];var Ft;Ft=function(e,t={}){{const n=(t.onCacheKey||Fe)(e),r=Ne[n];if(r)return r;let a=!1;const l=t.onError||D;t.onError=e=>{a=!0,l(e)};const{code:o}=K(e,t),s=new Function(`return ${o}`)();return a?s:Ne[n]=s}},ve=Ft,ke=function(e,t){if(!C(e))return null;let n=re.get(t);if(n||(n=function(e){const t=[];let n,r,a,l,o,s,c,u=-1,i=0,f=0;const m=[];function p(){const t=e[u+1];if(5===i&&"'"===t||6===i&&'"'===t)return u++,a="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=a:r+=a},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,i=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=ne(r),!1===r)return!1;m[1]()}};null!==i;)if(u++,n=e[u],"\\"!==n||!p()){if(l=te(n),c=Q[i],o=c[l]||c.l||8,8===o)return;if(i=o[0],void 0!==o[1]&&(s=m[o[1]],s&&(a=n,!1===s())))return;if(7===i)return t}}(t),n&&re.set(t,n)),!n)return null;const r=n.length;let a=e,l=0;for(;l<r;){const e=a[n[l]];if(void 0===e)return null;a=e,l++}return a},he=me;export{gt as DatetimeFormat,kt as I18nInjectionKey,pt as NumberFormat,ft as Translation,Ge as VERSION,yt as castToVueI18n,ht as createI18n,Lt as useI18n,_t as vTDirective};
