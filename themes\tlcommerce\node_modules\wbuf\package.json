{"_args": [["wbuf@1.7.3", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "wbuf@1.7.3", "_id": "wbuf@1.7.3", "_inBundle": false, "_integrity": "sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==", "_location": "/wbuf", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "wbuf@1.7.3", "name": "wbuf", "escapedName": "wbuf", "rawSpec": "1.7.3", "saveSpec": null, "fetchSpec": "1.7.3"}, "_requiredBy": ["/hpack.js", "/spdy-transport"], "_resolved": "https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz", "_spec": "1.7.3", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/indutny/wbuf/issues"}, "dependencies": {"minimalistic-assert": "^1.0.0"}, "description": "Write buffer", "devDependencies": {"mocha": "^5.0.4"}, "homepage": "https://github.com/indutny/wbuf", "keywords": ["Write", "<PERSON><PERSON><PERSON>"], "license": "MIT", "main": "index.js", "name": "wbuf", "repository": {"type": "git", "url": "git+ssh://**************/indutny/wbuf.git"}, "scripts": {"test": "mocha test/**/*-test.js"}, "version": "1.7.3"}