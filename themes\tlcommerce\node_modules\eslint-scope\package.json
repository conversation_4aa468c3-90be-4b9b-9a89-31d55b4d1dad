{"_args": [["eslint-scope@5.1.1", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "eslint-scope@5.1.1", "_id": "eslint-scope@5.1.1", "_inBundle": false, "_integrity": "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==", "_location": "/eslint-scope", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "eslint-scope@5.1.1", "name": "eslint-scope", "escapedName": "eslint-scope", "rawSpec": "5.1.1", "saveSpec": null, "fetchSpec": "5.1.1"}, "_requiredBy": ["/webpack"], "_resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "_spec": "5.1.1", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "bugs": {"url": "https://github.com/eslint/eslint-scope/issues"}, "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "description": "ECMAScript scope analyzer for ESLint", "devDependencies": {"@typescript-eslint/parser": "^1.11.0", "chai": "^4.2.0", "eslint": "^6.0.1", "eslint-config-eslint": "^5.0.1", "eslint-plugin-node": "^9.1.0", "eslint-release": "^1.0.0", "eslint-visitor-keys": "^1.2.0", "espree": "^7.1.0", "istanbul": "^0.4.5", "mocha": "^6.1.4", "npm-license": "^0.3.3", "shelljs": "^0.8.3", "typescript": "^3.5.2"}, "engines": {"node": ">=8.0.0"}, "files": ["LICENSE", "README.md", "lib"], "homepage": "http://github.com/eslint/eslint-scope", "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "name": "eslint-scope", "repository": {"type": "git", "url": "git+https://github.com/eslint/eslint-scope.git"}, "scripts": {"generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-release": "eslint-generate-release", "lint": "node Makefile.js lint", "publish-release": "eslint-publish-release", "test": "node Makefile.js test"}, "version": "5.1.1"}