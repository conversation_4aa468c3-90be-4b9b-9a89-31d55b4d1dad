# Vue-cool-lightbox

Vue-cool-lightbox is a pretty Vue.js lightbox component, inspired by [fancybox](http://fancyapps.com/fancybox/3/) with zoom, swipe, captions and videos supported.

![Vue CoolLightbox screenshot](http://lucaspulliese.com/wp-content/themes/theme/images/screenshot-vue-cool-lightbox.jpg)

## Installation

Use npm to install vue-cool-lightbox.

```bash
npm install --save vue-cool-lightbox
```

and use the lightbox:
```javascript
import CoolLightBox from 'vue-cool-lightbox'
import 'vue-cool-lightbox/dist/vue-cool-lightbox.min.css'

export default {
  components: {
    CoolLightBox,
  },
}
```

## Docs
You can see the complete documentation with examples here: [https://vue-cool-lightbox.lucaspulliese.com/](https://vue-cool-lightbox.lucaspulliese.com/).

## Contributing
Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.

## License
[MIT](https://choosealicense.com/licenses/mit/)

## Donate
I am very happy with the feedback from the community and it makes me want to continue working on this component and create more, so any help is welcome.

[Donate here](http://paypal.me/lucaspdonations)