{"_args": [["evp_bytestokey@1.0.3", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "evp_bytestokey@1.0.3", "_id": "evp_bytestokey@1.0.3", "_inBundle": false, "_integrity": "sha512-/f2Go4TognH/KvCISP7OUsHn85hT9nUkxxA9BEWxFn+Oj9o8ZNLm/40hdlgSLyuOimsrTKLUMEorQexp/aPQeA==", "_location": "/evp_bytestokey", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "evp_bytestokey@1.0.3", "name": "evp_bytestokey", "escapedName": "evp_bytestokey", "rawSpec": "1.0.3", "saveSpec": null, "fetchSpec": "1.0.3"}, "_requiredBy": ["/browserify-aes", "/browserify-cipher", "/parse-asn1"], "_resolved": "https://registry.npmjs.org/evp_bytestokey/-/evp_bytestokey-1.0.3.tgz", "_spec": "1.0.3", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/crypto-browserify/EVP_BytesToKey/issues"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}, "description": "The insecure key derivation algorithm from OpenSSL", "devDependencies": {"bindings": "^1.2.1", "nan": "^2.4.0", "nyc": "^8.1.0", "standard": "^8.0.0", "tape": "^4.6.0"}, "files": ["index.js"], "gypfile": false, "homepage": "https://github.com/crypto-browserify/EVP_BytesToKey", "keywords": ["crypto", "openssl"], "license": "MIT", "main": "index.js", "name": "evp_bytestokey", "repository": {"type": "git", "url": "git+https://github.com/crypto-browserify/EVP_BytesToKey.git"}, "scripts": {"coverage": "nyc tape test/*.js", "lint": "standard", "test": "npm run lint && npm run unit", "test:prepare": "node-gyp rebuild", "unit": "tape test/*.js"}, "version": "1.0.3"}