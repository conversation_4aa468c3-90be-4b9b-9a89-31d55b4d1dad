{"version": 3, "file": "vue3-runtime-template.js", "sources": ["../index.js"], "sourcesContent": ["import {h} from 'vue';\n\nconst defineDescriptor = (src, dest, name) => {\n  // eslint-disable-next-line no-prototype-builtins\n  if (!dest.hasOwnProperty(name)) {\n    const descriptor = Object.getOwnPropertyDescriptor(src, name);\n    Object.defineProperty(dest, name, descriptor);\n  }\n};\n\nconst merge = (objs) => {\n  const res = {};\n  objs.forEach((obj) => {\n    obj &&\n      Object.getOwnPropertyNames(obj).forEach((name) =>\n        defineDescriptor(obj, res, name),\n      );\n  });\n  return res;\n};\n\nconst buildFromProps = (obj, props) => {\n  const res = {};\n  props.forEach((prop) => defineDescriptor(obj, res, prop));\n  return res;\n};\n\nexport default {\n  props: {\n    template: String,\n    parent: Object,\n    templateProps: {\n      type: Object,\n      default: () => ({}),\n    },\n  },\n  render() {\n    if (this.template) {\n      const parent = this.parent || this.$parent;\n      const {\n        $data: parentData = {},\n        $props: parentProps = {},\n        $options: parentOptions = {},\n      } = parent;\n      const {\n        components: parentComponents = {},\n        computed: parentComputed = {},\n        methods: parentMethods = {},\n      } = parentOptions;\n      const {\n        $data = {},\n        $props = {},\n        $options: {methods = {}, computed = {}, components = {}} = {},\n      } = this;\n      const passthrough = {\n        $data: {},\n        $props: {},\n        $options: {},\n        components: {},\n        computed: {},\n        methods: {},\n      };\n\n      // build new objects by removing keys if already exists (e.g. created by mixins)\n      Object.keys(parentData).forEach((e) => {\n        if (typeof $data[e] === 'undefined') {\n          passthrough.$data[e] = parentData[e];\n        }\n      });\n      Object.keys(parentProps).forEach((e) => {\n        if (typeof $props[e] === 'undefined') {\n          passthrough.$props[e] = parentProps[e];\n        }\n      });\n      Object.keys(parentMethods).forEach((e) => {\n        if (typeof methods[e] === 'undefined') {\n          passthrough.methods[e] = parentMethods[e];\n        }\n      });\n      Object.keys(parentComputed).forEach((e) => {\n        if (typeof computed[e] === 'undefined') {\n          passthrough.computed[e] = parentComputed[e];\n        }\n      });\n      Object.keys(parentComponents).forEach((e) => {\n        if (typeof components[e] === 'undefined') {\n          passthrough.components[e] = parentComponents[e];\n        }\n      });\n\n      const methodKeys = Object.keys(passthrough.methods || {});\n      const dataKeys = Object.keys(passthrough.$data || {});\n      const propKeys = Object.keys(passthrough.$props || {});\n      const templatePropKeys = Object.keys(this.templateProps);\n      const allKeys = dataKeys.concat(propKeys).concat(methodKeys).concat(templatePropKeys);\n      const methodsFromProps = buildFromProps(parent, methodKeys);\n      const finalProps = merge([\n        passthrough.$data,\n        passthrough.$props,\n        methodsFromProps,\n        this.templateProps,\n      ]);\n\n      const provide = this.$parent.$.provides ? this.$parent.$.provides : {}; // Avoids Vue warning\n\n      const dynamic = {\n        template: this.template || '<div></div>',\n        props: allKeys,\n        computed: passthrough.computed,\n        components: passthrough.components,\n        provide: provide,\n      };\n      // debugger;\n\n      return h(dynamic, {...finalProps});\n    }\n  },\n};\n"], "names": ["defineDescriptor", "src", "dest", "name", "hasOwnProperty", "descriptor", "Object", "getOwnPropertyDescriptor", "defineProperty", "props", "template", "String", "parent", "templateProps", "type", "default", "render", "this", "$parent", "passthrough", "$data", "$props", "$options", "components", "computed", "methods", "keys", "parentData", "for<PERSON>ach", "e", "parentProps", "parentMethods", "parentComputed", "parentComponents", "methodKeys", "dataKeys", "propKeys", "templatePropKeys", "allKeys", "concat", "methodsFromProps", "obj", "res", "prop", "finalProps", "objs", "getOwnPropertyNames", "merge", "h", "provide", "$", "provides"], "mappings": "qBAEMA,WAAoBC,EAAKC,EAAMC,OAE9BD,EAAKE,eAAeD,GAAO,KACxBE,EAAaC,OAAOC,yBAAyBN,EAAKE,GACxDG,OAAOE,eAAeN,EAAMC,EAAME,OAqBvB,CACbI,MAAO,CACLC,SAAUC,OACVC,OAAQN,OACRO,cAAe,CACbC,KAAMR,OACNS,+BAGJC,qBACMC,KAAKP,SAAU,KACXE,EAASK,KAAKL,QAAUK,KAAKC,iCAEb,kCACE,oCACI,sCAGK,oCACJ,mCACF,UAMvBD,0BAHM,UAGNA,2BAFO,UAEPA,6BADyD,IAAjD,+BAAW,oCAAe,sCAAiB,QAEjDE,EAAc,CAClBC,MAAO,GACPC,OAAQ,GACRC,SAAU,GACVC,WAAY,GACZC,SAAU,GACVC,QAAS,IAIXnB,OAAOoB,KAAKC,GAAYC,iBAASC,QACP,IAAbT,EAAMS,KACfV,EAAYC,MAAMS,GAAKF,EAAWE,MAGtCvB,OAAOoB,KAAKI,GAAaF,iBAASC,QACP,IAAdR,EAAOQ,KAChBV,EAAYE,OAAOQ,GAAKC,EAAYD,MAGxCvB,OAAOoB,KAAKK,GAAeH,iBAASC,QACR,IAAfJ,EAAQI,KACjBV,EAAYM,QAAQI,GAAKE,EAAcF,MAG3CvB,OAAOoB,KAAKM,GAAgBJ,iBAASC,QACR,IAAhBL,EAASK,KAClBV,EAAYK,SAASK,GAAKG,EAAeH,MAG7CvB,OAAOoB,KAAKO,GAAkBL,iBAASC,QACR,IAAlBN,EAAWM,KACpBV,EAAYI,WAAWM,GAAKI,EAAiBJ,UAI3CK,EAAa5B,OAAOoB,KAAKP,EAAYM,SAAW,IAChDU,EAAW7B,OAAOoB,KAAKP,EAAYC,OAAS,IAC5CgB,EAAW9B,OAAOoB,KAAKP,EAAYE,QAAU,IAC7CgB,EAAmB/B,OAAOoB,KAAKT,KAAKJ,eACpCyB,EAAUH,EAASI,OAAOH,GAAUG,OAAOL,GAAYK,OAAOF,GAC9DG,GA1EYC,EA0EsB7B,EAzEtC8B,EAAM,GAyEwCR,EAxE9CN,iBAASe,UAAS3C,EAAiByC,EAAKC,EAAKC,KAC5CD,GAwEGE,WAtFGC,OACPH,EAAM,UACZG,EAAKjB,iBAASa,GACZA,GACEnC,OAAOwC,oBAAoBL,GAAKb,iBAASzB,UACvCH,EAAiByC,EAAKC,EAAKvC,OAG1BuC,EA8EgBK,CAAM,CACvB5B,EAAYC,MACZD,EAAYE,OACZmB,EACAvB,KAAKJ,uBAcAmC,IATS,CACdtC,SAAUO,KAAKP,UAAY,cAC3BD,MAAO6B,EACPd,SAAUL,EAAYK,SACtBD,WAAYJ,EAAYI,WACxB0B,QAPchC,KAAKC,QAAQgC,EAAEC,SAAWlC,KAAKC,QAAQgC,EAAEC,SAAW,IAWlD7C,iBAAIsC,QA7FJH,EAChBC"}