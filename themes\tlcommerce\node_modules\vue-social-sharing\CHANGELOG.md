<a name="4.0.0-alpha4"></a>
# [4.0.0-alpha3](https://github.com/nicolasbeauvais/vue-social-sharing/compare/4.0.0-alpha2...4.0.0-alpha4) (2021-06-09)
- Fix npm release

<a name="4.0.0-alpha2"></a>
# [4.0.0-alpha2](https://github.com/nicolasbeauvais/vue-social-sharing/compare/4.0.0-alpha1...4.0.0-alpha2) (2021-06-09)
- Fix component rendering
  
<a name="4.0.0-alpha1"></a>
# [4.0.0-alpha1](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.7...4.0.0-alpha1) (2021-06-07)
- Vue 3 compatibility

<a name="3.0.7"></a>
# [3.0.7](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.6...3.0.7) (2021-03-18)
- Fix dependencies

<a name="3.0.6"></a>
# [3.0.6](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.5...3.0.6) (2021-03-18)
- Add share to Messenger

<a name="3.0.5"></a>
# [3.0.5](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.4...3.0.5) (2021-02-15)
- Change SocialSharing nodes href attributes to `javascript:void(0)` to support Vue-router history
- Update LinkedIn sharing URL

<a name="3.0.4"></a>
# [3.0.4](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.3...3.0.4) (2020-11-25)
- Add `href` attribute to SocialSharing nodes with `a` tag for accessibility

<a name="3.0.3"></a>
# [3.0.3](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.2...3.0.3) (2020-10-24)
- Update Xing sharing url 

<a name="3.0.2"></a>
# [3.0.2](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.1...3.0.2) (2020-10-07)
- Use a unique name for each network's popup window

<a name="3.0.1"></a>
# [3.0.1](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0...3.0.1) (2020-10-06)
- Add props for popup size customization

<a name="3.0.0"></a>
# [3.0.0](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.11...3.0.0) (2020-10-02)
- Fix error triggered by popup when prevented by AdBlocker or mobile context

<a name="3.0.0-beta.11"></a>
# [3.0.0-beta.11](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.10...3.0.0-beta.11) (2020-06-05)
- Add share to Yammer
- Update Readme.md

<a name="3.0.0-beta.10"></a>
# [3.0.0-beta.10](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.9...3.0.0-beta.10) (2020-05-22)
- Update LinkedIn sharer URL
- Update Readme.md

<a name="3.0.0-beta.9"></a>
# [3.0.0-beta.9](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.8...3.0.0-beta.9) (2020-05-21)
- Fix wrong text displayed on Twitter when sharing without a TwitterUser

<a name="3.0.0-beta.8"></a>
# [3.0.0-beta.8](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.7...3.0.0-beta.8) (2020-05-20)
- Fix Twitter title duplicated

<a name="3.0.0-beta.7"></a>
# [3.0.0-beta.7](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.6...3.0.0-beta.7) (2020-05-19)
- 🐛Fix another error when no custom networks are used

<a name="3.0.0-beta.6"></a>
# [3.0.0-beta.6](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.5...3.0.0-beta.6) (2020-05-19)
- 🐛Fix error when no custom networks are used

<a name="3.0.0-beta.5"></a>
# [3.0.0-beta.5](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.4...3.0.0-beta.5) (2020-05-19)
- Add share to Baidu
- Add share to Buffer
- Add share to EverNote
- Add share to Flipboard
- Add share to HackerNews
- Add share to InstaPaper
- Add share to Pocket
- Add share to Quora
- Add share to StumbleUpon
- Add share to Tumblr
- Add share to Wordpress
- Add share to Xing
- Fix Whatsapp sharing link
- Reduce bundle size
- Simplify network schema in src/network.js
- Events now send the network key as the first parameter
- Improved Readme.md

<a name="3.0.0-beta.4"></a>
# [3.0.0-beta.4](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.3...3.0.0-beta.4) (2020-05-19)
- Make shared properties more consistent between networks
- Improved Readme.md

<a name="3.0.0-beta.3"></a>
# [3.0.0-beta.3](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.2...3.0.0-beta.3) (2020-05-17)
- Make ShareNetwork component tree shakable
- Fix IOS SMS sharing link
- Update Whatsapp sharing link

<a name="3.0.0-beta.2"></a>
# [3.0.0-beta.2](https://github.com/nicolasbeauvais/vue-social-sharing/compare/3.0.0-beta.1...3.0.0-beta.2) (2020-05-16)
- Fix Nuxt support

<a name="3.0.0-beta.1"></a>
# [3.0.0-beta.1](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.4.7...3.0.0-beta.1) (2020-05-15)
- Complete rewrite of the package
- Updated Readme.md
- Only one component exported (`ShareNetwork`)
- New examples `yarn run example`
- Storybook stories `yarn run storybook`

<a name="2.4.7"></a>
# [2.4.7](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.4.6...2.4.7) (2019-11-07)
- Fix undefined popup error

<a name="2.4.6"></a>
# [2.4.6](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.4.5...2.4.6) (2019-08-11)
- Fix IOS Twitter sharing with empty hashtags, again

<a name="2.4.5"></a>
# [2.4.5](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.4.4...2.4.5) (2019-05-31)
- Fix IOS Twitter sharing with empty hashtags

<a name="2.4.4"></a>
# [2.4.4](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.4.3...2.4.4) (2019-05-29)
- Update Whatsapp share url to use API
- Fix IOS SMS sharing format (using ; instead of ?)
- Add tabindex attribute

<a name="2.4.3"></a>
# [2.4.3](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.4.2...2.4.3) (2019-04-09)
- Fix IE11 popup.window bug

<a name="2.4.2"></a>
# [2.4.2](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.4.1...2.4.2) (2019-02-21)
- Fix build

<a name="2.4.1"></a>
# [2.4.1](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.4.0...2.4.1) (2019-02-20)
- Fix issue #123

<a name="2.4.0"></a>
# [2.4.0](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.3.4...2.4.0) (2019-02-19)
- Fix facebook hashtags
- Move rollup-plugin-json to devDependencies
- Improve readme

<a name="2.3.4"></a>
# [2.3.4](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.3.3...2.3.4) (2019-02-11)
- Add support for facebook hashtags (PR #120)

<a name="2.3.3"></a>
# [2.3.3](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.3.2...2.3.3) (2017-12-11)
- Tag 2.3.3

<a name="2.3.2"></a>
# [2.3.2](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.3.1...2.3.2) (2017-12-04)
- Add Viber

<a name="2.3.1"></a>
# [2.3.1](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.11...2.3.1) (2017-12-03)
- Possibility to add custom networks or override existing ones

<a name="2.2.11"></a>
# [2.2.11](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.10...2.2.11) (2017-12-03)
- Merge #61 - Add local events
- Silently fail when trying to use a nonexistent network

<a name="2.2.10"></a>
# [2.2.10](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.9...2.2.10) (2017-10-09)
- Fix dist

<a name="2.2.9"></a>
# [2.2.9](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.8...2.2.9) (2017-10-09)
- Allow v-bind usage for classes and styles on the network component

<a name="2.2.8"></a>
# [2.2.8](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.7...2.2.8) (2017-10-05)
- Revert fix twitter sharing

<a name="2.2.7"></a>
# [2.2.7](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.6...2.2.7) (2017-10-03)
- Add link to SMS and Email sharing
- Fix twitter sharing (missing "via=")

<a name="2.2.6"></a>
# [2.2.6](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.5...2.2.6) (2017-09-29)
- Add SMS

<a name="2.2.5"></a>
# [2.2.5](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.4...2.2.5) (2017-08-30)
- Add Email

<a name="2.2.4"></a>
# [2.2.4](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.3...2.2.4) (2017-08-26)
- Add Odnoklassniki

<a name="2.2.3"></a>
# [2.2.3](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.2...2.2.3) (2017-06-21)
- Fix telegram sharing

<a name="2.2.2"></a>
# [2.2.2](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.1...2.2.2) (2017-06-19)
- Add Skype & Line

<a name="2.2.1"></a>
# [2.2.1](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.2.0...2.2.1) (2017-06-17)
- Add telegram

<a name="2.2.0"></a>
# [2.2.0](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.1.5...2.2.0) (2017-03-29)
- Internal methods refactor
- Rename `social_shares_click` event to `social_shares_open`
- Add `social_shares_change` and `social_shares_close` event

<a name="2.1.5"></a>
# [2.1.5](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.1.4...2.1.5) (2017-03-18)
- Vue 2.2.4 compatibility

<a name="2.1.4"></a>
# [2.1.4](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.1.3...2.1.4) (2017-03-18)
- Center popup on dual screen

<a name="2.1.3"></a>
# [2.1.3](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.1.2...2.1.3) (2017-03-18)
- Add Weibo and VK
- Refactor unit tests using loops
- Convert networks.js to json format (network.json)

<a name="2.1.2"></a>
# [2.1.2](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.1.1...2.1.2) (2017-03-18)
- Encode title / description / code to allow use of special chars

<a name="2.1.1"></a>
# [2.1.1](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.1.0...2.1.1) (2017-02-21)
- Update version

<a name="2.1.0"></a>
# [2.1.0](https://github.com/nicolasbeauvais/vue-social-sharing/compare/2.0.0...2.1.0) (2017-02-21)
- Use of span tag instead of a to prevent history change
- Fix event emission on $root instance

<a name="2.0.0"></a>
# [2.0.0](https://github.com/nicolasbeauvais/vue-social-sharing/compare/1.1.2...2.0.0) (2017-02-20)
- Add support for server side rendering

<a name="1.1.2"></a>
# [1.1.2](https://github.com/nicolasbeauvais/vue-social-sharing/compare/1.1.1...1.1.2) (2017-02-18)
- Add media option for pinterest

<a name="1.1.1"></a>
# [1.1.1](https://github.com/nicolasbeauvais/vue-social-sharing/compare/1.1.0...1.1.1) (2017-01-18)
- Twitter parameter `via` is now optional

<a name="1.1.0"></a>
# [1.1.0](https://github.com/nicolasbeauvais/vue-social-sharing/compare/1.0.0...1.1.0) (2017-01-18)
- Fix development build to avoid loading 2 vuejs instance on users production env
- Improve the use of the vue.js 2 API to respect the same component syntax used in v0.0.4

<a name="1.0.0"></a>
# [1.0.0](https://github.com/nicolasbeauvais/vue-social-sharing/compare/0.0.4...1.0.0) (2017-01-12)
- Migrate the package to Vue.js v2

<a name="0.0.4"></a>
# [0.0.4](https://github.com/nicolasbeauvais/vue-social-sharing/compare/0.0.3...0.0.4) (2016-11-03)
- Add twitter hashtag property
- Add title description and quote parameters for facebook share

<a name="0.0.3"></a>
# [0.0.3](https://github.com/nicolasbeauvais/vue-social-sharing/compare/0.0.2...0.0.3) (2016-11-01)
- Add property to fill twitter content

<a name="0.0.2"></a>
# [0.0.2](https://github.com/nicolasbeauvais/vue-social-sharing/compare/0.0.1...0.0.2) (2016-10-21)
- Small fixes
