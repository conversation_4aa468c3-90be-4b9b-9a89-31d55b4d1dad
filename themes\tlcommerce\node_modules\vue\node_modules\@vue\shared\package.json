{"_args": [["@vue/shared@3.2.36", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "@vue/shared@3.2.36", "_id": "@vue/shared@3.2.36", "_inBundle": false, "_integrity": "sha512-JtB41wXl7Au3+Nl3gD16Cfpj7k/6aCroZ6BbOiCMFCMvrOpkg/qQUXTso2XowaNqBbnkuGHurLAqkLBxNGc1hQ==", "_location": "/vue/@vue/shared", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/shared@3.2.36", "name": "@vue/shared", "escapedName": "@vue%2fshared", "scope": "@vue", "rawSpec": "3.2.36", "saveSpec": null, "fetchSpec": "3.2.36"}, "_requiredBy": ["/vue", "/vue/@vue/compiler-core", "/vue/@vue/compiler-dom", "/vue/@vue/compiler-ssr", "/vue/@vue/server-renderer"], "_resolved": "https://registry.npmjs.org/@vue/shared/-/shared-3.2.36.tgz", "_spec": "3.2.36", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"formats": ["esm-bundler", "cjs"]}, "description": "internal utils shared across @vue packages", "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/shared#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/shared.esm-bundler.js", "name": "@vue/shared", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/shared"}, "sideEffects": false, "types": "dist/shared.d.ts", "version": "3.2.36"}