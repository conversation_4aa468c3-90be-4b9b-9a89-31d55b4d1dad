#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/Vue/vue/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="$NODE_PATH:/Users/<USER>/Vue/vue/node_modules/.pnpm/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/@babel+parser@7.19.4/node_modules/@babel/parser/bin/babel-parser.js" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/@babel+parser@7.19.4/node_modules/@babel/parser/bin/babel-parser.js" "$@"
fi
