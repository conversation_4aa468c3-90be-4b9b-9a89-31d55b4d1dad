{"_args": [["eslint-visitor-keys@2.1.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "eslint-visitor-keys@2.1.0", "_id": "eslint-visitor-keys@2.1.0", "_inBundle": false, "_integrity": "sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==", "_location": "/eslint-utils/eslint-visitor-keys", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "eslint-visitor-keys@2.1.0", "name": "eslint-visitor-keys", "escapedName": "eslint-visitor-keys", "rawSpec": "2.1.0", "saveSpec": null, "fetchSpec": "2.1.0"}, "_requiredBy": ["/eslint-utils"], "_resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "_spec": "2.1.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/mysticatea"}, "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "dependencies": {}, "description": "Constants and utilities about visitor keys to traverse AST.", "devDependencies": {"eslint": "^4.7.2", "eslint-config-eslint": "^4.0.0", "eslint-release": "^1.0.0", "mocha": "^3.5.3", "nyc": "^11.2.1", "opener": "^1.4.3"}, "engines": {"node": ">=10"}, "files": ["lib"], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "keywords": [], "license": "Apache-2.0", "main": "lib/index.js", "name": "eslint-visitor-keys", "repository": {"type": "git", "url": "git+https://github.com/eslint/eslint-visitor-keys.git"}, "scripts": {"coverage": "nyc report --reporter lcov && opener coverage/lcov-report/index.html", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-release": "eslint-generate-release", "lint": "eslint lib tests/lib", "publish-release": "eslint-publish-release", "test": "nyc mocha tests/lib"}, "version": "2.1.0"}