{"_args": [["vue-i18n@9.2.2", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue-i18n@9.2.2", "_id": "vue-i18n@9.2.2", "_inBundle": false, "_integrity": "sha512-yswpwtj89rTBhegUAv9Mu37LNznyu3NpyLQmozF3i1hYOhwpG8RjcjIFIIfnu+2MDZJGSZPXaKWvnQA71Yv9TQ==", "_location": "/vue-i18n", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-i18n@9.2.2", "name": "vue-i18n", "escapedName": "vue-i18n", "rawSpec": "9.2.2", "saveSpec": null, "fetchSpec": "9.2.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue-i18n/-/vue-i18n-9.2.2.tgz", "_spec": "9.2.2", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/intlify/vue-i18n-next/issues"}, "buildOptions": {"name": "VueI18n", "formats": ["esm-bundler", "esm-bundler-runtime", "esm-browser", "esm-browser-runtime", "cjs", "global", "global-runtime"]}, "dependencies": {"@intlify/core-base": "9.2.2", "@intlify/shared": "9.2.2", "@intlify/vue-devtools": "9.2.2", "@vue/devtools-api": "^6.2.1"}, "description": "Internationalization plugin for Vue.js", "devDependencies": {"@intlify/devtools-if": "9.2.2"}, "engines": {"node": ">= 14"}, "exports": {".": {"import": {"node": "./index.mjs", "default": "./dist/vue-i18n.esm-bundler.js"}, "require": "./index.js"}, "./dist/*": "./dist/*", "./index.mjs": "./index.mjs", "./package.json": "./package.json"}, "files": ["index.js", "index.mjs", "dist", "vetur"], "homepage": "https://github.com/intlify/vue-i18n-next/tree/master/packages/vue-i18n#readme", "jsdelivr": "dist/vue-i18n.global.js", "keywords": ["i18n", "internationalization", "intlify", "plugin", "vue", "vue.js"], "license": "MIT", "main": "index.js", "module": "dist/vue-i18n.esm-bundler.js", "name": "vue-i18n", "peerDependencies": {"vue": "^3.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/intlify/vue-i18n-next.git", "directory": "packages/vue-i18n"}, "sideEffects": false, "types": "dist/vue-i18n.d.ts", "unpkg": "dist/vue-i18n.global.js", "version": "9.2.2", "vetur": {"tags": "vetur/tags.json", "attributes": "vetur/attributes.json"}}