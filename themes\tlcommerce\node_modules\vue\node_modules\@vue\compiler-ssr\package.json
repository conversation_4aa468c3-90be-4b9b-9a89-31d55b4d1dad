{"_args": [["@vue/compiler-ssr@3.2.36", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "@vue/compiler-ssr@3.2.36", "_id": "@vue/compiler-ssr@3.2.36", "_inBundle": false, "_integrity": "sha512-+KugInUFRvOxEdLkZwE+W43BqHyhBh0jpYXhmqw1xGq2dmE6J9eZ8UUSOKNhdHtQ/iNLWWeK/wPZkVLUf3YGaw==", "_location": "/vue/@vue/compiler-ssr", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-ssr@3.2.36", "name": "@vue/compiler-ssr", "escapedName": "@vue%2fcompiler-ssr", "scope": "@vue", "rawSpec": "3.2.36", "saveSpec": null, "fetchSpec": "3.2.36"}, "_requiredBy": ["/vue/@vue/server-renderer"], "_resolved": "https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.2.36.tgz", "_spec": "3.2.36", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"prod": false, "formats": ["cjs"]}, "dependencies": {"@vue/compiler-dom": "3.2.36", "@vue/shared": "3.2.36"}, "description": "@vue/compiler-ssr", "files": ["dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-ssr#readme", "keywords": ["vue"], "license": "MIT", "main": "dist/compiler-ssr.cjs.js", "name": "@vue/compiler-ssr", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-ssr"}, "types": "dist/compiler-ssr.d.ts", "version": "3.2.36"}