{"version": 3, "file": "esquery.min.js", "sources": ["../node_modules/estraverse/estraverse.js", "../parser.js", "../esquery.js"], "sourcesContent": ["/*\n  Copyright (C) 2012-2013 <PERSON><PERSON> <<EMAIL>>\n  Copyright (C) 2012 <PERSON>ya Hidayat <<EMAIL>>\n\n  Redistribution and use in source and binary forms, with or without\n  modification, are permitted provided that the following conditions are met:\n\n    * Redistributions of source code must retain the above copyright\n      notice, this list of conditions and the following disclaimer.\n    * Redistributions in binary form must reproduce the above copyright\n      notice, this list of conditions and the following disclaimer in the\n      documentation and/or other materials provided with the distribution.\n\n  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n  AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n  IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n  ARE DISCLAIMED. IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE FOR ANY\n  DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n  LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND\n  ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n  (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF\n  THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n*/\n/*jslint vars:false, bitwise:true*/\n/*jshint indent:4*/\n/*global exports:true*/\n(function clone(exports) {\n    'use strict';\n\n    var Syntax,\n        VisitorOption,\n        VisitorKeys,\n        BREAK,\n        SKIP,\n        REMOVE;\n\n    function deepCopy(obj) {\n        var ret = {}, key, val;\n        for (key in obj) {\n            if (obj.hasOwnProperty(key)) {\n                val = obj[key];\n                if (typeof val === 'object' && val !== null) {\n                    ret[key] = deepCopy(val);\n                } else {\n                    ret[key] = val;\n                }\n            }\n        }\n        return ret;\n    }\n\n    // based on LLVM libc++ upper_bound / lower_bound\n    // MIT License\n\n    function upperBound(array, func) {\n        var diff, len, i, current;\n\n        len = array.length;\n        i = 0;\n\n        while (len) {\n            diff = len >>> 1;\n            current = i + diff;\n            if (func(array[current])) {\n                len = diff;\n            } else {\n                i = current + 1;\n                len -= diff + 1;\n            }\n        }\n        return i;\n    }\n\n    Syntax = {\n        AssignmentExpression: 'AssignmentExpression',\n        AssignmentPattern: 'AssignmentPattern',\n        ArrayExpression: 'ArrayExpression',\n        ArrayPattern: 'ArrayPattern',\n        ArrowFunctionExpression: 'ArrowFunctionExpression',\n        AwaitExpression: 'AwaitExpression', // CAUTION: It's deferred to ES7.\n        BlockStatement: 'BlockStatement',\n        BinaryExpression: 'BinaryExpression',\n        BreakStatement: 'BreakStatement',\n        CallExpression: 'CallExpression',\n        CatchClause: 'CatchClause',\n        ChainExpression: 'ChainExpression',\n        ClassBody: 'ClassBody',\n        ClassDeclaration: 'ClassDeclaration',\n        ClassExpression: 'ClassExpression',\n        ComprehensionBlock: 'ComprehensionBlock',  // CAUTION: It's deferred to ES7.\n        ComprehensionExpression: 'ComprehensionExpression',  // CAUTION: It's deferred to ES7.\n        ConditionalExpression: 'ConditionalExpression',\n        ContinueStatement: 'ContinueStatement',\n        DebuggerStatement: 'DebuggerStatement',\n        DirectiveStatement: 'DirectiveStatement',\n        DoWhileStatement: 'DoWhileStatement',\n        EmptyStatement: 'EmptyStatement',\n        ExportAllDeclaration: 'ExportAllDeclaration',\n        ExportDefaultDeclaration: 'ExportDefaultDeclaration',\n        ExportNamedDeclaration: 'ExportNamedDeclaration',\n        ExportSpecifier: 'ExportSpecifier',\n        ExpressionStatement: 'ExpressionStatement',\n        ForStatement: 'ForStatement',\n        ForInStatement: 'ForInStatement',\n        ForOfStatement: 'ForOfStatement',\n        FunctionDeclaration: 'FunctionDeclaration',\n        FunctionExpression: 'FunctionExpression',\n        GeneratorExpression: 'GeneratorExpression',  // CAUTION: It's deferred to ES7.\n        Identifier: 'Identifier',\n        IfStatement: 'IfStatement',\n        ImportExpression: 'ImportExpression',\n        ImportDeclaration: 'ImportDeclaration',\n        ImportDefaultSpecifier: 'ImportDefaultSpecifier',\n        ImportNamespaceSpecifier: 'ImportNamespaceSpecifier',\n        ImportSpecifier: 'ImportSpecifier',\n        Literal: 'Literal',\n        LabeledStatement: 'LabeledStatement',\n        LogicalExpression: 'LogicalExpression',\n        MemberExpression: 'MemberExpression',\n        MetaProperty: 'MetaProperty',\n        MethodDefinition: 'MethodDefinition',\n        ModuleSpecifier: 'ModuleSpecifier',\n        NewExpression: 'NewExpression',\n        ObjectExpression: 'ObjectExpression',\n        ObjectPattern: 'ObjectPattern',\n        PrivateIdentifier: 'PrivateIdentifier',\n        Program: 'Program',\n        Property: 'Property',\n        PropertyDefinition: 'PropertyDefinition',\n        RestElement: 'RestElement',\n        ReturnStatement: 'ReturnStatement',\n        SequenceExpression: 'SequenceExpression',\n        SpreadElement: 'SpreadElement',\n        Super: 'Super',\n        SwitchStatement: 'SwitchStatement',\n        SwitchCase: 'SwitchCase',\n        TaggedTemplateExpression: 'TaggedTemplateExpression',\n        TemplateElement: 'TemplateElement',\n        TemplateLiteral: 'TemplateLiteral',\n        ThisExpression: 'ThisExpression',\n        ThrowStatement: 'ThrowStatement',\n        TryStatement: 'TryStatement',\n        UnaryExpression: 'UnaryExpression',\n        UpdateExpression: 'UpdateExpression',\n        VariableDeclaration: 'VariableDeclaration',\n        VariableDeclarator: 'VariableDeclarator',\n        WhileStatement: 'WhileStatement',\n        WithStatement: 'WithStatement',\n        YieldExpression: 'YieldExpression'\n    };\n\n    VisitorKeys = {\n        AssignmentExpression: ['left', 'right'],\n        AssignmentPattern: ['left', 'right'],\n        ArrayExpression: ['elements'],\n        ArrayPattern: ['elements'],\n        ArrowFunctionExpression: ['params', 'body'],\n        AwaitExpression: ['argument'], // CAUTION: It's deferred to ES7.\n        BlockStatement: ['body'],\n        BinaryExpression: ['left', 'right'],\n        BreakStatement: ['label'],\n        CallExpression: ['callee', 'arguments'],\n        CatchClause: ['param', 'body'],\n        ChainExpression: ['expression'],\n        ClassBody: ['body'],\n        ClassDeclaration: ['id', 'superClass', 'body'],\n        ClassExpression: ['id', 'superClass', 'body'],\n        ComprehensionBlock: ['left', 'right'],  // CAUTION: It's deferred to ES7.\n        ComprehensionExpression: ['blocks', 'filter', 'body'],  // CAUTION: It's deferred to ES7.\n        ConditionalExpression: ['test', 'consequent', 'alternate'],\n        ContinueStatement: ['label'],\n        DebuggerStatement: [],\n        DirectiveStatement: [],\n        DoWhileStatement: ['body', 'test'],\n        EmptyStatement: [],\n        ExportAllDeclaration: ['source'],\n        ExportDefaultDeclaration: ['declaration'],\n        ExportNamedDeclaration: ['declaration', 'specifiers', 'source'],\n        ExportSpecifier: ['exported', 'local'],\n        ExpressionStatement: ['expression'],\n        ForStatement: ['init', 'test', 'update', 'body'],\n        ForInStatement: ['left', 'right', 'body'],\n        ForOfStatement: ['left', 'right', 'body'],\n        FunctionDeclaration: ['id', 'params', 'body'],\n        FunctionExpression: ['id', 'params', 'body'],\n        GeneratorExpression: ['blocks', 'filter', 'body'],  // CAUTION: It's deferred to ES7.\n        Identifier: [],\n        IfStatement: ['test', 'consequent', 'alternate'],\n        ImportExpression: ['source'],\n        ImportDeclaration: ['specifiers', 'source'],\n        ImportDefaultSpecifier: ['local'],\n        ImportNamespaceSpecifier: ['local'],\n        ImportSpecifier: ['imported', 'local'],\n        Literal: [],\n        LabeledStatement: ['label', 'body'],\n        LogicalExpression: ['left', 'right'],\n        MemberExpression: ['object', 'property'],\n        MetaProperty: ['meta', 'property'],\n        MethodDefinition: ['key', 'value'],\n        ModuleSpecifier: [],\n        NewExpression: ['callee', 'arguments'],\n        ObjectExpression: ['properties'],\n        ObjectPattern: ['properties'],\n        PrivateIdentifier: [],\n        Program: ['body'],\n        Property: ['key', 'value'],\n        PropertyDefinition: ['key', 'value'],\n        RestElement: [ 'argument' ],\n        ReturnStatement: ['argument'],\n        SequenceExpression: ['expressions'],\n        SpreadElement: ['argument'],\n        Super: [],\n        SwitchStatement: ['discriminant', 'cases'],\n        SwitchCase: ['test', 'consequent'],\n        TaggedTemplateExpression: ['tag', 'quasi'],\n        TemplateElement: [],\n        TemplateLiteral: ['quasis', 'expressions'],\n        ThisExpression: [],\n        ThrowStatement: ['argument'],\n        TryStatement: ['block', 'handler', 'finalizer'],\n        UnaryExpression: ['argument'],\n        UpdateExpression: ['argument'],\n        VariableDeclaration: ['declarations'],\n        VariableDeclarator: ['id', 'init'],\n        WhileStatement: ['test', 'body'],\n        WithStatement: ['object', 'body'],\n        YieldExpression: ['argument']\n    };\n\n    // unique id\n    BREAK = {};\n    SKIP = {};\n    REMOVE = {};\n\n    VisitorOption = {\n        Break: BREAK,\n        Skip: SKIP,\n        Remove: REMOVE\n    };\n\n    function Reference(parent, key) {\n        this.parent = parent;\n        this.key = key;\n    }\n\n    Reference.prototype.replace = function replace(node) {\n        this.parent[this.key] = node;\n    };\n\n    Reference.prototype.remove = function remove() {\n        if (Array.isArray(this.parent)) {\n            this.parent.splice(this.key, 1);\n            return true;\n        } else {\n            this.replace(null);\n            return false;\n        }\n    };\n\n    function Element(node, path, wrap, ref) {\n        this.node = node;\n        this.path = path;\n        this.wrap = wrap;\n        this.ref = ref;\n    }\n\n    function Controller() { }\n\n    // API:\n    // return property path array from root to current node\n    Controller.prototype.path = function path() {\n        var i, iz, j, jz, result, element;\n\n        function addToPath(result, path) {\n            if (Array.isArray(path)) {\n                for (j = 0, jz = path.length; j < jz; ++j) {\n                    result.push(path[j]);\n                }\n            } else {\n                result.push(path);\n            }\n        }\n\n        // root node\n        if (!this.__current.path) {\n            return null;\n        }\n\n        // first node is sentinel, second node is root element\n        result = [];\n        for (i = 2, iz = this.__leavelist.length; i < iz; ++i) {\n            element = this.__leavelist[i];\n            addToPath(result, element.path);\n        }\n        addToPath(result, this.__current.path);\n        return result;\n    };\n\n    // API:\n    // return type of current node\n    Controller.prototype.type = function () {\n        var node = this.current();\n        return node.type || this.__current.wrap;\n    };\n\n    // API:\n    // return array of parent elements\n    Controller.prototype.parents = function parents() {\n        var i, iz, result;\n\n        // first node is sentinel\n        result = [];\n        for (i = 1, iz = this.__leavelist.length; i < iz; ++i) {\n            result.push(this.__leavelist[i].node);\n        }\n\n        return result;\n    };\n\n    // API:\n    // return current node\n    Controller.prototype.current = function current() {\n        return this.__current.node;\n    };\n\n    Controller.prototype.__execute = function __execute(callback, element) {\n        var previous, result;\n\n        result = undefined;\n\n        previous  = this.__current;\n        this.__current = element;\n        this.__state = null;\n        if (callback) {\n            result = callback.call(this, element.node, this.__leavelist[this.__leavelist.length - 1].node);\n        }\n        this.__current = previous;\n\n        return result;\n    };\n\n    // API:\n    // notify control skip / break\n    Controller.prototype.notify = function notify(flag) {\n        this.__state = flag;\n    };\n\n    // API:\n    // skip child nodes of current node\n    Controller.prototype.skip = function () {\n        this.notify(SKIP);\n    };\n\n    // API:\n    // break traversals\n    Controller.prototype['break'] = function () {\n        this.notify(BREAK);\n    };\n\n    // API:\n    // remove node\n    Controller.prototype.remove = function () {\n        this.notify(REMOVE);\n    };\n\n    Controller.prototype.__initialize = function(root, visitor) {\n        this.visitor = visitor;\n        this.root = root;\n        this.__worklist = [];\n        this.__leavelist = [];\n        this.__current = null;\n        this.__state = null;\n        this.__fallback = null;\n        if (visitor.fallback === 'iteration') {\n            this.__fallback = Object.keys;\n        } else if (typeof visitor.fallback === 'function') {\n            this.__fallback = visitor.fallback;\n        }\n\n        this.__keys = VisitorKeys;\n        if (visitor.keys) {\n            this.__keys = Object.assign(Object.create(this.__keys), visitor.keys);\n        }\n    };\n\n    function isNode(node) {\n        if (node == null) {\n            return false;\n        }\n        return typeof node === 'object' && typeof node.type === 'string';\n    }\n\n    function isProperty(nodeType, key) {\n        return (nodeType === Syntax.ObjectExpression || nodeType === Syntax.ObjectPattern) && 'properties' === key;\n    }\n  \n    function candidateExistsInLeaveList(leavelist, candidate) {\n        for (var i = leavelist.length - 1; i >= 0; --i) {\n            if (leavelist[i].node === candidate) {\n                return true;\n            }\n        }\n        return false;\n    }\n\n    Controller.prototype.traverse = function traverse(root, visitor) {\n        var worklist,\n            leavelist,\n            element,\n            node,\n            nodeType,\n            ret,\n            key,\n            current,\n            current2,\n            candidates,\n            candidate,\n            sentinel;\n\n        this.__initialize(root, visitor);\n\n        sentinel = {};\n\n        // reference\n        worklist = this.__worklist;\n        leavelist = this.__leavelist;\n\n        // initialize\n        worklist.push(new Element(root, null, null, null));\n        leavelist.push(new Element(null, null, null, null));\n\n        while (worklist.length) {\n            element = worklist.pop();\n\n            if (element === sentinel) {\n                element = leavelist.pop();\n\n                ret = this.__execute(visitor.leave, element);\n\n                if (this.__state === BREAK || ret === BREAK) {\n                    return;\n                }\n                continue;\n            }\n\n            if (element.node) {\n\n                ret = this.__execute(visitor.enter, element);\n\n                if (this.__state === BREAK || ret === BREAK) {\n                    return;\n                }\n\n                worklist.push(sentinel);\n                leavelist.push(element);\n\n                if (this.__state === SKIP || ret === SKIP) {\n                    continue;\n                }\n\n                node = element.node;\n                nodeType = node.type || element.wrap;\n                candidates = this.__keys[nodeType];\n                if (!candidates) {\n                    if (this.__fallback) {\n                        candidates = this.__fallback(node);\n                    } else {\n                        throw new Error('Unknown node type ' + nodeType + '.');\n                    }\n                }\n\n                current = candidates.length;\n                while ((current -= 1) >= 0) {\n                    key = candidates[current];\n                    candidate = node[key];\n                    if (!candidate) {\n                        continue;\n                    }\n\n                    if (Array.isArray(candidate)) {\n                        current2 = candidate.length;\n                        while ((current2 -= 1) >= 0) {\n                            if (!candidate[current2]) {\n                                continue;\n                            }\n\n                            if (candidateExistsInLeaveList(leavelist, candidate[current2])) {\n                              continue;\n                            }\n\n                            if (isProperty(nodeType, candidates[current])) {\n                                element = new Element(candidate[current2], [key, current2], 'Property', null);\n                            } else if (isNode(candidate[current2])) {\n                                element = new Element(candidate[current2], [key, current2], null, null);\n                            } else {\n                                continue;\n                            }\n                            worklist.push(element);\n                        }\n                    } else if (isNode(candidate)) {\n                        if (candidateExistsInLeaveList(leavelist, candidate)) {\n                          continue;\n                        }\n\n                        worklist.push(new Element(candidate, key, null, null));\n                    }\n                }\n            }\n        }\n    };\n\n    Controller.prototype.replace = function replace(root, visitor) {\n        var worklist,\n            leavelist,\n            node,\n            nodeType,\n            target,\n            element,\n            current,\n            current2,\n            candidates,\n            candidate,\n            sentinel,\n            outer,\n            key;\n\n        function removeElem(element) {\n            var i,\n                key,\n                nextElem,\n                parent;\n\n            if (element.ref.remove()) {\n                // When the reference is an element of an array.\n                key = element.ref.key;\n                parent = element.ref.parent;\n\n                // If removed from array, then decrease following items' keys.\n                i = worklist.length;\n                while (i--) {\n                    nextElem = worklist[i];\n                    if (nextElem.ref && nextElem.ref.parent === parent) {\n                        if  (nextElem.ref.key < key) {\n                            break;\n                        }\n                        --nextElem.ref.key;\n                    }\n                }\n            }\n        }\n\n        this.__initialize(root, visitor);\n\n        sentinel = {};\n\n        // reference\n        worklist = this.__worklist;\n        leavelist = this.__leavelist;\n\n        // initialize\n        outer = {\n            root: root\n        };\n        element = new Element(root, null, null, new Reference(outer, 'root'));\n        worklist.push(element);\n        leavelist.push(element);\n\n        while (worklist.length) {\n            element = worklist.pop();\n\n            if (element === sentinel) {\n                element = leavelist.pop();\n\n                target = this.__execute(visitor.leave, element);\n\n                // node may be replaced with null,\n                // so distinguish between undefined and null in this place\n                if (target !== undefined && target !== BREAK && target !== SKIP && target !== REMOVE) {\n                    // replace\n                    element.ref.replace(target);\n                }\n\n                if (this.__state === REMOVE || target === REMOVE) {\n                    removeElem(element);\n                }\n\n                if (this.__state === BREAK || target === BREAK) {\n                    return outer.root;\n                }\n                continue;\n            }\n\n            target = this.__execute(visitor.enter, element);\n\n            // node may be replaced with null,\n            // so distinguish between undefined and null in this place\n            if (target !== undefined && target !== BREAK && target !== SKIP && target !== REMOVE) {\n                // replace\n                element.ref.replace(target);\n                element.node = target;\n            }\n\n            if (this.__state === REMOVE || target === REMOVE) {\n                removeElem(element);\n                element.node = null;\n            }\n\n            if (this.__state === BREAK || target === BREAK) {\n                return outer.root;\n            }\n\n            // node may be null\n            node = element.node;\n            if (!node) {\n                continue;\n            }\n\n            worklist.push(sentinel);\n            leavelist.push(element);\n\n            if (this.__state === SKIP || target === SKIP) {\n                continue;\n            }\n\n            nodeType = node.type || element.wrap;\n            candidates = this.__keys[nodeType];\n            if (!candidates) {\n                if (this.__fallback) {\n                    candidates = this.__fallback(node);\n                } else {\n                    throw new Error('Unknown node type ' + nodeType + '.');\n                }\n            }\n\n            current = candidates.length;\n            while ((current -= 1) >= 0) {\n                key = candidates[current];\n                candidate = node[key];\n                if (!candidate) {\n                    continue;\n                }\n\n                if (Array.isArray(candidate)) {\n                    current2 = candidate.length;\n                    while ((current2 -= 1) >= 0) {\n                        if (!candidate[current2]) {\n                            continue;\n                        }\n                        if (isProperty(nodeType, candidates[current])) {\n                            element = new Element(candidate[current2], [key, current2], 'Property', new Reference(candidate, current2));\n                        } else if (isNode(candidate[current2])) {\n                            element = new Element(candidate[current2], [key, current2], null, new Reference(candidate, current2));\n                        } else {\n                            continue;\n                        }\n                        worklist.push(element);\n                    }\n                } else if (isNode(candidate)) {\n                    worklist.push(new Element(candidate, key, null, new Reference(node, key)));\n                }\n            }\n        }\n\n        return outer.root;\n    };\n\n    function traverse(root, visitor) {\n        var controller = new Controller();\n        return controller.traverse(root, visitor);\n    }\n\n    function replace(root, visitor) {\n        var controller = new Controller();\n        return controller.replace(root, visitor);\n    }\n\n    function extendCommentRange(comment, tokens) {\n        var target;\n\n        target = upperBound(tokens, function search(token) {\n            return token.range[0] > comment.range[0];\n        });\n\n        comment.extendedRange = [comment.range[0], comment.range[1]];\n\n        if (target !== tokens.length) {\n            comment.extendedRange[1] = tokens[target].range[0];\n        }\n\n        target -= 1;\n        if (target >= 0) {\n            comment.extendedRange[0] = tokens[target].range[1];\n        }\n\n        return comment;\n    }\n\n    function attachComments(tree, providedComments, tokens) {\n        // At first, we should calculate extended comment ranges.\n        var comments = [], comment, len, i, cursor;\n\n        if (!tree.range) {\n            throw new Error('attachComments needs range information');\n        }\n\n        // tokens array is empty, we attach comments to tree as 'leadingComments'\n        if (!tokens.length) {\n            if (providedComments.length) {\n                for (i = 0, len = providedComments.length; i < len; i += 1) {\n                    comment = deepCopy(providedComments[i]);\n                    comment.extendedRange = [0, tree.range[0]];\n                    comments.push(comment);\n                }\n                tree.leadingComments = comments;\n            }\n            return tree;\n        }\n\n        for (i = 0, len = providedComments.length; i < len; i += 1) {\n            comments.push(extendCommentRange(deepCopy(providedComments[i]), tokens));\n        }\n\n        // This is based on John Freeman's implementation.\n        cursor = 0;\n        traverse(tree, {\n            enter: function (node) {\n                var comment;\n\n                while (cursor < comments.length) {\n                    comment = comments[cursor];\n                    if (comment.extendedRange[1] > node.range[0]) {\n                        break;\n                    }\n\n                    if (comment.extendedRange[1] === node.range[0]) {\n                        if (!node.leadingComments) {\n                            node.leadingComments = [];\n                        }\n                        node.leadingComments.push(comment);\n                        comments.splice(cursor, 1);\n                    } else {\n                        cursor += 1;\n                    }\n                }\n\n                // already out of owned node\n                if (cursor === comments.length) {\n                    return VisitorOption.Break;\n                }\n\n                if (comments[cursor].extendedRange[0] > node.range[1]) {\n                    return VisitorOption.Skip;\n                }\n            }\n        });\n\n        cursor = 0;\n        traverse(tree, {\n            leave: function (node) {\n                var comment;\n\n                while (cursor < comments.length) {\n                    comment = comments[cursor];\n                    if (node.range[1] < comment.extendedRange[0]) {\n                        break;\n                    }\n\n                    if (node.range[1] === comment.extendedRange[0]) {\n                        if (!node.trailingComments) {\n                            node.trailingComments = [];\n                        }\n                        node.trailingComments.push(comment);\n                        comments.splice(cursor, 1);\n                    } else {\n                        cursor += 1;\n                    }\n                }\n\n                // already out of owned node\n                if (cursor === comments.length) {\n                    return VisitorOption.Break;\n                }\n\n                if (comments[cursor].extendedRange[0] > node.range[1]) {\n                    return VisitorOption.Skip;\n                }\n            }\n        });\n\n        return tree;\n    }\n\n    exports.Syntax = Syntax;\n    exports.traverse = traverse;\n    exports.replace = replace;\n    exports.attachComments = attachComments;\n    exports.VisitorKeys = VisitorKeys;\n    exports.VisitorOption = VisitorOption;\n    exports.Controller = Controller;\n    exports.cloneEnvironment = function () { return clone({}); };\n\n    return exports;\n}(exports));\n/* vim: set sw=4 ts=4 et tw=80 : */\n", "/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n(function(root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], factory);\n  } else if (typeof module === \"object\" && module.exports) {\n    module.exports = factory();\n  }\n})(this, function() {\n  \"use strict\";\n\n  function peg$subclass(child, parent) {\n    function ctor() { this.constructor = child; }\n    ctor.prototype = parent.prototype;\n    child.prototype = new ctor();\n  }\n\n  function peg$SyntaxError(message, expected, found, location) {\n    this.message  = message;\n    this.expected = expected;\n    this.found    = found;\n    this.location = location;\n    this.name     = \"SyntaxError\";\n\n    if (typeof Error.captureStackTrace === \"function\") {\n      Error.captureStackTrace(this, peg$SyntaxError);\n    }\n  }\n\n  peg$subclass(peg$SyntaxError, Error);\n\n  peg$SyntaxError.buildMessage = function(expected, found) {\n    var DESCRIBE_EXPECTATION_FNS = {\n          literal: function(expectation) {\n            return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n          },\n\n          \"class\": function(expectation) {\n            var escapedParts = \"\",\n                i;\n\n            for (i = 0; i < expectation.parts.length; i++) {\n              escapedParts += expectation.parts[i] instanceof Array\n                ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1])\n                : classEscape(expectation.parts[i]);\n            }\n\n            return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n          },\n\n          any: function(expectation) {\n            return \"any character\";\n          },\n\n          end: function(expectation) {\n            return \"end of input\";\n          },\n\n          other: function(expectation) {\n            return expectation.description;\n          }\n        };\n\n    function hex(ch) {\n      return ch.charCodeAt(0).toString(16).toUpperCase();\n    }\n\n    function literalEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\"/g,  '\\\\\"')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function classEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\\]/g, '\\\\]')\n        .replace(/\\^/g, '\\\\^')\n        .replace(/-/g,  '\\\\-')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function describeExpectation(expectation) {\n      return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n    }\n\n    function describeExpected(expected) {\n      var descriptions = new Array(expected.length),\n          i, j;\n\n      for (i = 0; i < expected.length; i++) {\n        descriptions[i] = describeExpectation(expected[i]);\n      }\n\n      descriptions.sort();\n\n      if (descriptions.length > 0) {\n        for (i = 1, j = 1; i < descriptions.length; i++) {\n          if (descriptions[i - 1] !== descriptions[i]) {\n            descriptions[j] = descriptions[i];\n            j++;\n          }\n        }\n        descriptions.length = j;\n      }\n\n      switch (descriptions.length) {\n        case 1:\n          return descriptions[0];\n\n        case 2:\n          return descriptions[0] + \" or \" + descriptions[1];\n\n        default:\n          return descriptions.slice(0, -1).join(\", \")\n            + \", or \"\n            + descriptions[descriptions.length - 1];\n      }\n    }\n\n    function describeFound(found) {\n      return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n    }\n\n    return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n  };\n\n  function peg$parse(input, options) {\n    options = options !== void 0 ? options : {};\n\n    var peg$FAILED = {},\n\n        peg$startRuleFunctions = { start: peg$parsestart },\n        peg$startRuleFunction  = peg$parsestart,\n\n        peg$c0 = function(ss) {\n            return ss.length === 1 ? ss[0] : { type: 'matches', selectors: ss };\n          },\n        peg$c1 = function() { return void 0; },\n        peg$c2 = \" \",\n        peg$c3 = peg$literalExpectation(\" \", false),\n        peg$c4 = /^[^ [\\],():#!=><~+.]/,\n        peg$c5 = peg$classExpectation([\" \", \"[\", \"]\", \",\", \"(\", \")\", \":\", \"#\", \"!\", \"=\", \">\", \"<\", \"~\", \"+\", \".\"], true, false),\n        peg$c6 = function(i) { return i.join(''); },\n        peg$c7 = \">\",\n        peg$c8 = peg$literalExpectation(\">\", false),\n        peg$c9 = function() { return 'child'; },\n        peg$c10 = \"~\",\n        peg$c11 = peg$literalExpectation(\"~\", false),\n        peg$c12 = function() { return 'sibling'; },\n        peg$c13 = \"+\",\n        peg$c14 = peg$literalExpectation(\"+\", false),\n        peg$c15 = function() { return 'adjacent'; },\n        peg$c16 = function() { return 'descendant'; },\n        peg$c17 = \",\",\n        peg$c18 = peg$literalExpectation(\",\", false),\n        peg$c19 = function(s, ss) {\n          return [s].concat(ss.map(function (s) { return s[3]; }));\n        },\n        peg$c20 = function(a, ops) {\n            return ops.reduce(function (memo, rhs) {\n              return { type: rhs[0], left: memo, right: rhs[1] };\n            }, a);\n          },\n        peg$c21 = \"!\",\n        peg$c22 = peg$literalExpectation(\"!\", false),\n        peg$c23 = function(subject, as) {\n            const b = as.length === 1 ? as[0] : { type: 'compound', selectors: as };\n            if(subject) b.subject = true;\n            return b;\n          },\n        peg$c24 = \"*\",\n        peg$c25 = peg$literalExpectation(\"*\", false),\n        peg$c26 = function(a) { return { type: 'wildcard', value: a }; },\n        peg$c27 = \"#\",\n        peg$c28 = peg$literalExpectation(\"#\", false),\n        peg$c29 = function(i) { return { type: 'identifier', value: i }; },\n        peg$c30 = \"[\",\n        peg$c31 = peg$literalExpectation(\"[\", false),\n        peg$c32 = \"]\",\n        peg$c33 = peg$literalExpectation(\"]\", false),\n        peg$c34 = function(v) { return v; },\n        peg$c35 = /^[><!]/,\n        peg$c36 = peg$classExpectation([\">\", \"<\", \"!\"], false, false),\n        peg$c37 = \"=\",\n        peg$c38 = peg$literalExpectation(\"=\", false),\n        peg$c39 = function(a) { return (a || '') + '='; },\n        peg$c40 = /^[><]/,\n        peg$c41 = peg$classExpectation([\">\", \"<\"], false, false),\n        peg$c42 = \".\",\n        peg$c43 = peg$literalExpectation(\".\", false),\n        peg$c44 = function(a, as) {\n            return [].concat.apply([a], as).join('');\n          },\n        peg$c45 = function(name, op, value) {\n              return { type: 'attribute', name: name, operator: op, value: value };\n            },\n        peg$c46 = function(name) { return { type: 'attribute', name: name }; },\n        peg$c47 = \"\\\"\",\n        peg$c48 = peg$literalExpectation(\"\\\"\", false),\n        peg$c49 = /^[^\\\\\"]/,\n        peg$c50 = peg$classExpectation([\"\\\\\", \"\\\"\"], true, false),\n        peg$c51 = \"\\\\\",\n        peg$c52 = peg$literalExpectation(\"\\\\\", false),\n        peg$c53 = peg$anyExpectation(),\n        peg$c54 = function(a, b) { return a + b; },\n        peg$c55 = function(d) {\n                return { type: 'literal', value: strUnescape(d.join('')) };\n              },\n        peg$c56 = \"'\",\n        peg$c57 = peg$literalExpectation(\"'\", false),\n        peg$c58 = /^[^\\\\']/,\n        peg$c59 = peg$classExpectation([\"\\\\\", \"'\"], true, false),\n        peg$c60 = /^[0-9]/,\n        peg$c61 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n        peg$c62 = function(a, b) {\n                // Can use `a.flat().join('')` once supported\n                const leadingDecimals = a ? [].concat.apply([], a).join('') : '';\n                return { type: 'literal', value: parseFloat(leadingDecimals + b.join('')) };\n              },\n        peg$c63 = function(i) { return { type: 'literal', value: i }; },\n        peg$c64 = \"type(\",\n        peg$c65 = peg$literalExpectation(\"type(\", false),\n        peg$c66 = /^[^ )]/,\n        peg$c67 = peg$classExpectation([\" \", \")\"], true, false),\n        peg$c68 = \")\",\n        peg$c69 = peg$literalExpectation(\")\", false),\n        peg$c70 = function(t) { return { type: 'type', value: t.join('') }; },\n        peg$c71 = /^[imsu]/,\n        peg$c72 = peg$classExpectation([\"i\", \"m\", \"s\", \"u\"], false, false),\n        peg$c73 = \"/\",\n        peg$c74 = peg$literalExpectation(\"/\", false),\n        peg$c75 = /^[^\\/]/,\n        peg$c76 = peg$classExpectation([\"/\"], true, false),\n        peg$c77 = function(d, flgs) { return {\n              type: 'regexp', value: new RegExp(d.join(''), flgs ? flgs.join('') : '') };\n            },\n        peg$c78 = function(i, is) {\n          return { type: 'field', name: is.reduce(function(memo, p){ return memo + p[0] + p[1]; }, i)};\n        },\n        peg$c79 = \":not(\",\n        peg$c80 = peg$literalExpectation(\":not(\", false),\n        peg$c81 = function(ss) { return { type: 'not', selectors: ss }; },\n        peg$c82 = \":matches(\",\n        peg$c83 = peg$literalExpectation(\":matches(\", false),\n        peg$c84 = function(ss) { return { type: 'matches', selectors: ss }; },\n        peg$c85 = \":has(\",\n        peg$c86 = peg$literalExpectation(\":has(\", false),\n        peg$c87 = function(ss) { return { type: 'has', selectors: ss }; },\n        peg$c88 = \":first-child\",\n        peg$c89 = peg$literalExpectation(\":first-child\", false),\n        peg$c90 = function() { return nth(1); },\n        peg$c91 = \":last-child\",\n        peg$c92 = peg$literalExpectation(\":last-child\", false),\n        peg$c93 = function() { return nthLast(1); },\n        peg$c94 = \":nth-child(\",\n        peg$c95 = peg$literalExpectation(\":nth-child(\", false),\n        peg$c96 = function(n) { return nth(parseInt(n.join(''), 10)); },\n        peg$c97 = \":nth-last-child(\",\n        peg$c98 = peg$literalExpectation(\":nth-last-child(\", false),\n        peg$c99 = function(n) { return nthLast(parseInt(n.join(''), 10)); },\n        peg$c100 = \":\",\n        peg$c101 = peg$literalExpectation(\":\", false),\n        peg$c102 = function(c) {\n          return { type: 'class', name: c };\n        },\n\n        peg$currPos          = 0,\n        peg$savedPos         = 0,\n        peg$posDetailsCache  = [{ line: 1, column: 1 }],\n        peg$maxFailPos       = 0,\n        peg$maxFailExpected  = [],\n        peg$silentFails      = 0,\n\n        peg$resultsCache = {},\n\n        peg$result;\n\n    if (\"startRule\" in options) {\n      if (!(options.startRule in peg$startRuleFunctions)) {\n        throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n      }\n\n      peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n    }\n\n    function text() {\n      return input.substring(peg$savedPos, peg$currPos);\n    }\n\n    function location() {\n      return peg$computeLocation(peg$savedPos, peg$currPos);\n    }\n\n    function expected(description, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildStructuredError(\n        [peg$otherExpectation(description)],\n        input.substring(peg$savedPos, peg$currPos),\n        location\n      );\n    }\n\n    function error(message, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildSimpleError(message, location);\n    }\n\n    function peg$literalExpectation(text, ignoreCase) {\n      return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n    }\n\n    function peg$classExpectation(parts, inverted, ignoreCase) {\n      return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n    }\n\n    function peg$anyExpectation() {\n      return { type: \"any\" };\n    }\n\n    function peg$endExpectation() {\n      return { type: \"end\" };\n    }\n\n    function peg$otherExpectation(description) {\n      return { type: \"other\", description: description };\n    }\n\n    function peg$computePosDetails(pos) {\n      var details = peg$posDetailsCache[pos], p;\n\n      if (details) {\n        return details;\n      } else {\n        p = pos - 1;\n        while (!peg$posDetailsCache[p]) {\n          p--;\n        }\n\n        details = peg$posDetailsCache[p];\n        details = {\n          line:   details.line,\n          column: details.column\n        };\n\n        while (p < pos) {\n          if (input.charCodeAt(p) === 10) {\n            details.line++;\n            details.column = 1;\n          } else {\n            details.column++;\n          }\n\n          p++;\n        }\n\n        peg$posDetailsCache[pos] = details;\n        return details;\n      }\n    }\n\n    function peg$computeLocation(startPos, endPos) {\n      var startPosDetails = peg$computePosDetails(startPos),\n          endPosDetails   = peg$computePosDetails(endPos);\n\n      return {\n        start: {\n          offset: startPos,\n          line:   startPosDetails.line,\n          column: startPosDetails.column\n        },\n        end: {\n          offset: endPos,\n          line:   endPosDetails.line,\n          column: endPosDetails.column\n        }\n      };\n    }\n\n    function peg$fail(expected) {\n      if (peg$currPos < peg$maxFailPos) { return; }\n\n      if (peg$currPos > peg$maxFailPos) {\n        peg$maxFailPos = peg$currPos;\n        peg$maxFailExpected = [];\n      }\n\n      peg$maxFailExpected.push(expected);\n    }\n\n    function peg$buildSimpleError(message, location) {\n      return new peg$SyntaxError(message, null, null, location);\n    }\n\n    function peg$buildStructuredError(expected, found, location) {\n      return new peg$SyntaxError(\n        peg$SyntaxError.buildMessage(expected, found),\n        expected,\n        found,\n        location\n      );\n    }\n\n    function peg$parsestart() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 0,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseselectors();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c0(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c1();\n        }\n        s0 = s1;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parse_() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 1,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (input.charCodeAt(peg$currPos) === 32) {\n        s1 = peg$c2;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c3); }\n      }\n      while (s1 !== peg$FAILED) {\n        s0.push(s1);\n        if (input.charCodeAt(peg$currPos) === 32) {\n          s1 = peg$c2;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c3); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifierName() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 2,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      if (peg$c4.test(input.charAt(peg$currPos))) {\n        s2 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c5); }\n      }\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          if (peg$c4.test(input.charAt(peg$currPos))) {\n            s2 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c5); }\n          }\n        }\n      } else {\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c6(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsebinaryOp() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 3,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 62) {\n          s2 = peg$c7;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c8); }\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c9();\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 126) {\n            s2 = peg$c10;\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c11); }\n          }\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parse_();\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c12();\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parse_();\n          if (s1 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 43) {\n              s2 = peg$c13;\n              peg$currPos++;\n            } else {\n              s2 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c14); }\n            }\n            if (s2 !== peg$FAILED) {\n              s3 = peg$parse_();\n              if (s3 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c15();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n          if (s0 === peg$FAILED) {\n            s0 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 32) {\n              s1 = peg$c2;\n              peg$currPos++;\n            } else {\n              s1 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c3); }\n            }\n            if (s1 !== peg$FAILED) {\n              s2 = peg$parse_();\n              if (s2 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c16();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselectors() {\n      var s0, s1, s2, s3, s4, s5, s6, s7;\n\n      var key    = peg$currPos * 30 + 4,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseselector();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parse_();\n        if (s4 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s5 = peg$c17;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c18); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parse_();\n            if (s6 !== peg$FAILED) {\n              s7 = peg$parseselector();\n              if (s7 !== peg$FAILED) {\n                s4 = [s4, s5, s6, s7];\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parse_();\n          if (s4 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 44) {\n              s5 = peg$c17;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c18); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parse_();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parseselector();\n                if (s7 !== peg$FAILED) {\n                  s4 = [s4, s5, s6, s7];\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c19(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselector() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 5,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsesequence();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parsebinaryOp();\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsesequence();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parsebinaryOp();\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsesequence();\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c20(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsesequence() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 6,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c21;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c22); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseatom();\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            s3 = peg$parseatom();\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c23(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseatom() {\n      var s0;\n\n      var key    = peg$currPos * 30 + 7,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$parsewildcard();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parseidentifier();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parseattr();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parsefield();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parsenegation();\n              if (s0 === peg$FAILED) {\n                s0 = peg$parsematches();\n                if (s0 === peg$FAILED) {\n                  s0 = peg$parsehas();\n                  if (s0 === peg$FAILED) {\n                    s0 = peg$parsefirstChild();\n                    if (s0 === peg$FAILED) {\n                      s0 = peg$parselastChild();\n                      if (s0 === peg$FAILED) {\n                        s0 = peg$parsenthChild();\n                        if (s0 === peg$FAILED) {\n                          s0 = peg$parsenthLastChild();\n                          if (s0 === peg$FAILED) {\n                            s0 = peg$parseclass();\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsewildcard() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 8,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 42) {\n        s1 = peg$c24;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c25); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c26(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifier() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 9,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 35) {\n        s1 = peg$c27;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c28); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c29(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattr() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 10,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 91) {\n        s1 = peg$c30;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c31); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrValue();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 93) {\n                s5 = peg$c32;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c33); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c34(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 11,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (peg$c35.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c36); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c37;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c38); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c39(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        if (peg$c40.test(input.charAt(peg$currPos))) {\n          s0 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s0 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c41); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrEqOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 12,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c21;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c22); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c37;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c38); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c39(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrName() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 13,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseidentifierName();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s4 = peg$c42;\n          peg$currPos++;\n        } else {\n          s4 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c43); }\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parseidentifierName();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s4 = peg$c42;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c43); }\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parseidentifierName();\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c44(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrValue() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 14,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseattrName();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrEqOps();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parsetype();\n              if (s5 === peg$FAILED) {\n                s5 = peg$parseregex();\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c45(s1, s3, s5);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parseattrName();\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parse_();\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parseattrOps();\n            if (s3 !== peg$FAILED) {\n              s4 = peg$parse_();\n              if (s4 !== peg$FAILED) {\n                s5 = peg$parsestring();\n                if (s5 === peg$FAILED) {\n                  s5 = peg$parsenumber();\n                  if (s5 === peg$FAILED) {\n                    s5 = peg$parsepath();\n                  }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c45(s1, s3, s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parseattrName();\n          if (s1 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c46(s1);\n          }\n          s0 = s1;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsestring() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 15,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 34) {\n        s1 = peg$c47;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c48); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c49.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c50); }\n        }\n        if (s3 === peg$FAILED) {\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 92) {\n            s4 = peg$c51;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c52); }\n          }\n          if (s4 !== peg$FAILED) {\n            if (input.length > peg$currPos) {\n              s5 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c53); }\n            }\n            if (s5 !== peg$FAILED) {\n              peg$savedPos = s3;\n              s4 = peg$c54(s4, s5);\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          if (peg$c49.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c50); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c51;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c52); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c53); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c54(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 34) {\n            s3 = peg$c47;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c48); }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c55(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 39) {\n          s1 = peg$c56;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c57); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = [];\n          if (peg$c58.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c59); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c51;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c52); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c53); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c54(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c58.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c59); }\n            }\n            if (s3 === peg$FAILED) {\n              s3 = peg$currPos;\n              if (input.charCodeAt(peg$currPos) === 92) {\n                s4 = peg$c51;\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c52); }\n              }\n              if (s4 !== peg$FAILED) {\n                if (input.length > peg$currPos) {\n                  s5 = input.charAt(peg$currPos);\n                  peg$currPos++;\n                } else {\n                  s5 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c53); }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s3;\n                  s4 = peg$c54(s4, s5);\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            }\n          }\n          if (s2 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 39) {\n              s3 = peg$c56;\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c57); }\n            }\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c55(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenumber() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 16,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = [];\n      if (peg$c60.test(input.charAt(peg$currPos))) {\n        s3 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c61); }\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        if (peg$c60.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c61); }\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s3 = peg$c42;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c43); }\n        }\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c60.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c61); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c60.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c61); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c62(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsepath() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 17,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseidentifierName();\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c63(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetype() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 18,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c64) {\n        s1 = peg$c64;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c65); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c66.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c67); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c66.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c67); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c70(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseflags() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 19,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (peg$c71.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c72); }\n      }\n      if (s1 !== peg$FAILED) {\n        while (s1 !== peg$FAILED) {\n          s0.push(s1);\n          if (peg$c71.test(input.charAt(peg$currPos))) {\n            s1 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c72); }\n          }\n        }\n      } else {\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseregex() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 30 + 20,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 47) {\n        s1 = peg$c73;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c74); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c75.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c76); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c75.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c76); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 47) {\n            s3 = peg$c73;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c74); }\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parseflags();\n            if (s4 === peg$FAILED) {\n              s4 = null;\n            }\n            if (s4 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c77(s2, s4);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefield() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      var key    = peg$currPos * 30 + 21,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s1 = peg$c42;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c43); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s5 = peg$c42;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c43); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parseidentifierName();\n            if (s6 !== peg$FAILED) {\n              s5 = [s5, s6];\n              s4 = s5;\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s4;\n            s4 = peg$FAILED;\n          }\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 46) {\n              s5 = peg$c42;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c43); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parseidentifierName();\n              if (s6 !== peg$FAILED) {\n                s5 = [s5, s6];\n                s4 = s5;\n              } else {\n                peg$currPos = s4;\n                s4 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c78(s2, s3);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenegation() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 22,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c79) {\n        s1 = peg$c79;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c80); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c81(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsematches() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 23,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 9) === peg$c82) {\n        s1 = peg$c82;\n        peg$currPos += 9;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c83); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c84(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsehas() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 24,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c85) {\n        s1 = peg$c85;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c86); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c87(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefirstChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 25,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 12) === peg$c88) {\n        s1 = peg$c88;\n        peg$currPos += 12;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c89); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c90();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parselastChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 26,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c91) {\n        s1 = peg$c91;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c92); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c93();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 27,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c94) {\n        s1 = peg$c94;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c95); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c60.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c61); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c60.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c61); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c96(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthLastChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 28,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 16) === peg$c97) {\n        s1 = peg$c97;\n        peg$currPos += 16;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c98); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c60.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c61); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c60.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c61); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c99(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseclass() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 29,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 58) {\n        s1 = peg$c100;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c101); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c102(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n\n      function nth(n) { return { type: 'nth-child', index: { type: 'literal', value: n } }; }\n      function nthLast(n) { return { type: 'nth-last-child', index: { type: 'literal', value: n } }; }\n      function strUnescape(s) {\n        return s.replace(/\\\\(.)/g, function(match, ch) {\n          switch(ch) {\n            case 'b': return '\\b';\n            case 'f': return '\\f';\n            case 'n': return '\\n';\n            case 'r': return '\\r';\n            case 't': return '\\t';\n            case 'v': return '\\v';\n            default: return ch;\n          }\n        });\n      }\n\n\n    peg$result = peg$startRuleFunction();\n\n    if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n      return peg$result;\n    } else {\n      if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n        peg$fail(peg$endExpectation());\n      }\n\n      throw peg$buildStructuredError(\n        peg$maxFailExpected,\n        peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n        peg$maxFailPos < input.length\n          ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n          : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n      );\n    }\n  }\n\n  return {\n    SyntaxError: peg$SyntaxError,\n    parse:       peg$parse\n  };\n});\n", "/* vim: set sw=4 sts=4 : */\nimport estraverse from 'estraverse';\nimport parser from './parser.js';\n\n/**\n* @typedef {\"LEFT_SIDE\"|\"RIGHT_SIDE\"} Side\n*/\n\nconst LEFT_SIDE = 'LEFT_SIDE';\nconst RIGHT_SIDE = 'RIGHT_SIDE';\n\n/**\n * @external AST\n * @see https://esprima.readthedocs.io/en/latest/syntax-tree-format.html\n */\n\n/**\n * One of the rules of `grammar.pegjs`\n * @typedef {PlainObject} SelectorAST\n * @see grammar.pegjs\n*/\n\n/**\n * The `sequence` production of `grammar.pegjs`\n * @typedef {PlainObject} SelectorSequenceAST\n*/\n\n/**\n * Get the value of a property which may be multiple levels down\n * in the object.\n * @param {?PlainObject} obj\n * @param {string[]} keys\n * @returns {undefined|boolean|string|number|external:AST}\n */\nfunction getPath(obj, keys) {\n    for (let i = 0; i < keys.length; ++i) {\n        if (obj == null) { return obj; }\n        obj = obj[keys[i]];\n    }\n    return obj;\n}\n\n/**\n * Determine whether `node` can be reached by following `path`,\n * starting at `ancestor`.\n * @param {?external:AST} node\n * @param {?external:AST} ancestor\n * @param {string[]} path\n * @param {Integer} fromPathIndex\n * @returns {boolean}\n */\nfunction inPath(node, ancestor, path, fromPathIndex) {\n    let current = ancestor;\n    for (let i = fromPathIndex; i < path.length; ++i) {\n        if (current == null) {\n            return false;\n        }\n        const field = current[path[i]];\n        if (Array.isArray(field)) {\n            for (let k = 0; k < field.length; ++k) {\n                if (inPath(node, field[k], path, i + 1)) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        current = field;\n    }\n    return node === current;\n}\n\n/**\n * A generated matcher function for a selector.\n * @typedef {function} SelectorMatcher\n*/\n\n/**\n * A WeakMap for holding cached matcher functions for selectors.\n * @type {WeakMap<SelectorAST, SelectorMatcher>}\n*/\nconst MATCHER_CACHE = typeof WeakMap === 'function' ? new WeakMap : null;\n\n/**\n * Look up a matcher function for `selector` in the cache.\n * If it does not exist, generate it with `generateMatcher` and add it to the cache.\n * In engines without WeakMap, the caching is skipped and matchers are generated with every call.\n * @param {?SelectorAST} selector\n * @returns {SelectorMatcher}\n */\nfunction getMatcher(selector) {\n    if (selector == null) {\n        return () => true;\n    }\n\n    if (MATCHER_CACHE != null) {\n        let matcher = MATCHER_CACHE.get(selector);\n        if (matcher != null) {\n            return matcher;\n        }\n        matcher = generateMatcher(selector);\n        MATCHER_CACHE.set(selector, matcher);\n        return matcher;\n    }\n\n    return generateMatcher(selector);\n}\n\n/**\n * Create a matcher function for `selector`,\n * @param {?SelectorAST} selector\n * @returns {SelectorMatcher}\n */\nfunction generateMatcher(selector) {\n    switch(selector.type) {\n        case 'wildcard':\n            return () => true;\n\n        case 'identifier': {\n            const value = selector.value.toLowerCase();\n            return (node, ancestry, options) => {\n                const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n                return value === node[nodeTypeKey].toLowerCase();\n            };\n        }\n\n        case 'field': {\n            const path = selector.name.split('.');\n            return (node, ancestry) => {\n                const ancestor = ancestry[path.length - 1];\n                return inPath(node, ancestor, path, 0);\n            };\n        }\n\n        case 'matches': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (matchers[i](node, ancestry, options)) { return true; }\n                }\n                return false;\n            };\n        }\n\n        case 'compound': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (!matchers[i](node, ancestry, options)) { return false; }\n                }\n                return true;\n            };\n        }\n\n        case 'not': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (matchers[i](node, ancestry, options)) { return false; }\n                }\n                return true;\n            };\n        }\n\n        case 'has': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                let result = false;\n\n                const a = [];\n                estraverse.traverse(node, {\n                    enter (node, parent) {\n                        if (parent != null) { a.unshift(parent); }\n\n                        for (let i = 0; i < matchers.length; ++i) {\n                            if (matchers[i](node, a, options)) {\n                                result = true;\n                                this.break();\n                                return;\n                            }\n                        }\n                    },\n                    leave () { a.shift(); },\n                    keys: options && options.visitorKeys,\n                    fallback: options && options.fallback || 'iteration'\n                });\n\n                return result;\n            };\n        }\n\n        case 'child': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) => {\n                if (ancestry.length > 0 && right(node, ancestry, options)) {\n                    return left(ancestry[0], ancestry.slice(1), options);\n                }\n                return false;\n            };\n        }\n\n        case 'descendant': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) => {\n                if (right(node, ancestry, options)) {\n                    for (let i = 0, l = ancestry.length; i < l; ++i) {\n                        if (left(ancestry[i], ancestry.slice(i + 1), options)) {\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            };\n        }\n\n        case 'attribute': {\n            const path = selector.name.split('.');\n            switch (selector.operator) {\n                case void 0:\n                    return (node) => getPath(node, path) != null;\n                case '=':\n                    switch (selector.value.type) {\n                        case 'regexp':\n                            return (node) => {\n                                const p = getPath(node, path);\n                                return typeof p === 'string' && selector.value.value.test(p);\n                            };\n                        case 'literal': {\n                            const literal = `${selector.value.value}`;\n                            return (node) => literal === `${getPath(node, path)}`;\n                        }\n                        case 'type':\n                            return (node) => selector.value.value === typeof getPath(node, path);\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '!=':\n                    switch (selector.value.type) {\n                        case 'regexp':\n                            return (node) => !selector.value.value.test(getPath(node, path));\n                        case 'literal': {\n                            const literal = `${selector.value.value}`;\n                            return (node) => literal !== `${getPath(node, path)}`;\n                        }\n                        case 'type':\n                            return (node) => selector.value.value !== typeof getPath(node, path);\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '<=':\n                    return (node) => getPath(node, path) <= selector.value.value;\n                case '<':\n                    return (node) => getPath(node, path) < selector.value.value;\n                case '>':\n                    return (node) => getPath(node, path) > selector.value.value;\n                case '>=':\n                    return (node) => getPath(node, path) >= selector.value.value;\n            }\n            throw new Error(`Unknown operator: ${selector.operator}`);\n        }\n\n        case 'sibling': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    sibling(node, left, ancestry, LEFT_SIDE, options) ||\n                    selector.left.subject &&\n                    left(node, ancestry, options) &&\n                    sibling(node, right, ancestry, RIGHT_SIDE, options);\n        }\n\n        case 'adjacent': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    adjacent(node, left, ancestry, LEFT_SIDE, options) ||\n                    selector.right.subject &&\n                    left(node, ancestry, options) &&\n                    adjacent(node, right, ancestry, RIGHT_SIDE, options);\n        }\n\n        case 'nth-child': {\n            const nth = selector.index.value;\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    nthChild(node, ancestry, nth, options);\n        }\n\n        case 'nth-last-child': {\n            const nth = -selector.index.value;\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    nthChild(node, ancestry, nth, options);\n        }\n\n        case 'class': {\n\n            return (node, ancestry, options) => {\n                \n                if (options && options.matchClass) {\n                    return options.matchClass(selector.name, node, ancestry);\n                }\n                \n                if (options && options.nodeTypeKey) return false;\n                \n                const name = selector.name.toLowerCase();\n\n                switch(name){\n                    case 'statement':\n                        if(node.type.slice(-9) === 'Statement') return true;\n                        // fallthrough: interface Declaration <: Statement { }\n                    case 'declaration':\n                        return node.type.slice(-11) === 'Declaration';\n                    case 'pattern':\n                        if(node.type.slice(-7) === 'Pattern') return true;\n                        // fallthrough: interface Expression <: Node, Pattern { }\n                    case 'expression':\n                        return node.type.slice(-10) === 'Expression' ||\n                            node.type.slice(-7) === 'Literal' ||\n                            (\n                                node.type === 'Identifier' &&\n                                (ancestry.length === 0 || ancestry[0].type !== 'MetaProperty')\n                            ) ||\n                            node.type === 'MetaProperty';\n                    case 'function':\n                        return node.type === 'FunctionDeclaration' ||\n                            node.type === 'FunctionExpression' ||\n                            node.type === 'ArrowFunctionExpression';\n                }\n                throw new Error(`Unknown class name: ${selector.name}`);\n            };\n        }\n    }\n\n    throw new Error(`Unknown selector type: ${selector.type}`);\n}\n\n/**\n * @callback TraverseOptionFallback\n * @param {external:AST} node The given node.\n * @returns {string[]} An array of visitor keys for the given node.\n */\n\n/**\n * @callback ClassMatcher\n * @param {string} className The name of the class to match.\n * @param {external:AST} node The node to match against.\n * @param {Array<external:AST>} ancestry The ancestry of the node.\n * @returns {boolean} True if the node matches the class, false if not.\n */\n\n/**\n * @typedef {object} ESQueryOptions\n * @property {string} [nodeTypeKey=\"type\"] By passing `nodeTypeKey`, we can allow other ASTs to use ESQuery.\n * @property { { [nodeType: string]: string[] } } [visitorKeys] By passing `visitorKeys` mapping, we can extend the properties of the nodes that traverse the node.\n * @property {TraverseOptionFallback} [fallback] By passing `fallback` option, we can control the properties of traversing nodes when encountering unknown nodes.\n * @property {ClassMatcher} [matchClass] By passing `matchClass` option, we can customize the interpretation of classes.\n */\n\n/**\n * Given a `node` and its ancestors, determine if `node` is matched\n * by `selector`.\n * @param {?external:AST} node\n * @param {?SelectorAST} selector\n * @param {external:AST[]} [ancestry=[]]\n * @param {ESQueryOptions} [options]\n * @throws {Error} Unknowns (operator, class name, selector type, or\n * selector value type)\n * @returns {boolean}\n */\nfunction matches(node, selector, ancestry, options) {\n    if (!selector) { return true; }\n    if (!node) { return false; }\n    if (!ancestry) { ancestry = []; }\n\n    return getMatcher(selector)(node, ancestry, options);\n}\n\n/**\n * Get visitor keys of a given node.\n * @param {external:AST} node The AST node to get keys.\n * @param {ESQueryOptions|undefined} options\n * @returns {string[]} Visitor keys of the node.\n */\nfunction getVisitorKeys(node, options) {\n    const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n\n    const nodeType = node[nodeTypeKey];\n    if (options && options.visitorKeys && options.visitorKeys[nodeType]) {\n        return options.visitorKeys[nodeType];\n    }\n    if (estraverse.VisitorKeys[nodeType]) {\n        return estraverse.VisitorKeys[nodeType];\n    }\n    if (options && typeof options.fallback === 'function') {\n        return options.fallback(node);\n    }\n    // 'iteration' fallback\n    return Object.keys(node).filter(function (key) {\n        return key !== nodeTypeKey;\n    });\n}\n\n\n/**\n * Check whether the given value is an ASTNode or not.\n * @param {any} node The value to check.\n * @param {ESQueryOptions|undefined} options The options to use.\n * @returns {boolean} `true` if the value is an ASTNode.\n */\nfunction isNode(node, options) {\n    const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n    return node !== null && typeof node === 'object' && typeof node[nodeTypeKey] === 'string';\n}\n\n/**\n * Determines if the given node has a sibling that matches the\n * given selector matcher.\n * @param {external:AST} node\n * @param {SelectorMatcher} matcher\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction sibling(node, matcher, ancestry, side, options) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const startIndex = listProp.indexOf(node);\n            if (startIndex < 0) { continue; }\n            let lowerBound, upperBound;\n            if (side === LEFT_SIDE) {\n                lowerBound = 0;\n                upperBound = startIndex;\n            } else {\n                lowerBound = startIndex + 1;\n                upperBound = listProp.length;\n            }\n            for (let k = lowerBound; k < upperBound; ++k) {\n                if (isNode(listProp[k], options) && matcher(listProp[k], ancestry, options)) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * Determines if the given node has an adjacent sibling that matches\n * the given selector matcher.\n * @param {external:AST} node\n * @param {SelectorMatcher} matcher\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction adjacent(node, matcher, ancestry, side, options) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const idx = listProp.indexOf(node);\n            if (idx < 0) { continue; }\n            if (side === LEFT_SIDE && idx > 0 && isNode(listProp[idx - 1], options) && matcher(listProp[idx - 1], ancestry, options)) {\n                return true;\n            }\n            if (side === RIGHT_SIDE && idx < listProp.length - 1 && isNode(listProp[idx + 1], options) &&  matcher(listProp[idx + 1], ancestry, options)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * Determines if the given node is the `nth` child.\n * If `nth` is negative then the position is counted\n * from the end of the list of children.\n * @param {external:AST} node\n * @param {external:AST[]} ancestry\n * @param {Integer} nth\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction nthChild(node, ancestry, nth, options) {\n    if (nth === 0) { return false; }\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)){\n            const idx = nth < 0 ? listProp.length + nth : nth - 1;\n            if (idx >= 0 && idx < listProp.length && listProp[idx] === node) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * For each selector node marked as a subject, find the portion of the\n * selector that the subject must match.\n * @param {SelectorAST} selector\n * @param {SelectorAST} [ancestor] Defaults to `selector`\n * @returns {SelectorAST[]}\n */\nfunction subjects(selector, ancestor) {\n    if (selector == null || typeof selector != 'object') { return []; }\n    if (ancestor == null) { ancestor = selector; }\n    const results = selector.subject ? [ancestor] : [];\n    const keys = Object.keys(selector);\n    for (let i = 0; i < keys.length; ++i) {\n        const p = keys[i];\n        const sel = selector[p];\n        results.push(...subjects(sel, p === 'left' ? sel : ancestor));\n    }\n    return results;\n}\n\n/**\n* @callback TraverseVisitor\n* @param {?external:AST} node\n* @param {?external:AST} parent\n* @param {external:AST[]} ancestry\n*/\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @param {TraverseVisitor} visitor\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction traverse(ast, selector, visitor, options) {\n    if (!selector) { return; }\n    const ancestry = [];\n    const matcher = getMatcher(selector);\n    const altSubjects = subjects(selector).map(getMatcher);\n    estraverse.traverse(ast, {\n        enter (node, parent) {\n            if (parent != null) { ancestry.unshift(parent); }\n            if (matcher(node, ancestry, options)) {\n                if (altSubjects.length) {\n                    for (let i = 0, l = altSubjects.length; i < l; ++i) {\n                        if (altSubjects[i](node, ancestry, options)) {\n                            visitor(node, parent, ancestry);\n                        }\n                        for (let k = 0, m = ancestry.length; k < m; ++k) {\n                            const succeedingAncestry = ancestry.slice(k + 1);\n                            if (altSubjects[i](ancestry[k], succeedingAncestry, options)) {\n                                visitor(ancestry[k], parent, succeedingAncestry);\n                            }\n                        }\n                    }\n                } else {\n                    visitor(node, parent, ancestry);\n                }\n            }\n        },\n        leave () { ancestry.shift(); },\n        keys: options && options.visitorKeys,\n        fallback: options && options.fallback || 'iteration'\n    });\n}\n\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction match(ast, selector, options) {\n    const results = [];\n    traverse(ast, selector, function (node) {\n        results.push(node);\n    }, options);\n    return results;\n}\n\n/**\n * Parse a selector string and return its AST.\n * @param {string} selector\n * @returns {SelectorAST}\n */\nfunction parse(selector) {\n    return parser.parse(selector);\n}\n\n/**\n * Query the code AST using the selector string.\n * @param {external:AST} ast\n * @param {string} selector\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction query(ast, selector, options) {\n    return match(ast, parse(selector), options);\n}\n\nquery.parse = parse;\nquery.match = match;\nquery.traverse = traverse;\nquery.matches = matches;\nquery.query = query;\n\nexport default query;\n"], "names": ["clone", "exports", "Syntax", "VisitorOption", "VisitorKeys", "BREAK", "SKIP", "REMOVE", "deepCopy", "obj", "key", "val", "ret", "hasOwnProperty", "Reference", "parent", "this", "Element", "node", "path", "wrap", "ref", "Controller", "isNode", "type", "isProperty", "nodeType", "ObjectExpression", "ObjectPattern", "candidateExistsInLeaveList", "leavelist", "candidate", "i", "length", "traverse", "root", "visitor", "extendCommentRange", "comment", "tokens", "target", "array", "func", "diff", "len", "current", "upperBound", "token", "range", "extendedRange", "AssignmentExpression", "AssignmentPattern", "ArrayExpression", "ArrayPattern", "ArrowFunctionExpression", "AwaitExpression", "BlockStatement", "BinaryExpression", "BreakStatement", "CallExpression", "CatchClause", "ChainExpression", "ClassBody", "ClassDeclaration", "ClassExpression", "ComprehensionBlock", "ComprehensionExpression", "ConditionalExpression", "ContinueStatement", "DebuggerStatement", "DirectiveStatement", "DoWhileStatement", "EmptyStatement", "ExportAllDeclaration", "ExportDefaultDeclaration", "ExportNamedDeclaration", "ExportSpecifier", "ExpressionStatement", "ForStatement", "ForInStatement", "ForOfStatement", "FunctionDeclaration", "FunctionExpression", "GeneratorExpression", "Identifier", "IfStatement", "ImportExpression", "ImportDeclaration", "ImportDefaultSpecifier", "ImportNamespaceSpecifier", "ImportSpecifier", "Literal", "LabeledStatement", "LogicalExpression", "MemberExpression", "MetaProperty", "MethodDefinition", "ModuleSpecifier", "NewExpression", "PrivateIdentifier", "Program", "Property", "PropertyDefinition", "RestElement", "ReturnStatement", "SequenceExpression", "SpreadElement", "Super", "SwitchStatement", "SwitchCase", "TaggedTemplateExpression", "TemplateElement", "TemplateLiteral", "ThisExpression", "ThrowStatement", "TryStatement", "UnaryExpression", "UpdateExpression", "VariableDeclaration", "VariableDeclarator", "WhileStatement", "WithStatement", "YieldExpression", "Break", "<PERSON><PERSON>", "Remove", "prototype", "replace", "remove", "Array", "isArray", "splice", "iz", "j", "jz", "result", "addToPath", "push", "__current", "__leavelist", "parents", "__execute", "callback", "element", "previous", "undefined", "__state", "call", "notify", "flag", "skip", "__initialize", "__worklist", "__fallback", "fallback", "Object", "keys", "__keys", "assign", "create", "worklist", "current2", "candidates", "sentinel", "pop", "enter", "Error", "leave", "outer", "removeElem", "nextElem", "attachComments", "tree", "providedComments", "cursor", "comments", "leadingComments", "trailingComments", "cloneEnvironment", "module", "peg$SyntaxError", "message", "expected", "found", "location", "name", "captureStackTrace", "child", "ctor", "constructor", "peg$subclass", "buildMessage", "DESCRIBE_EXPECTATION_FNS", "literal", "expectation", "literalEscape", "text", "class", "escapedParts", "parts", "classEscape", "inverted", "any", "end", "other", "description", "hex", "ch", "charCodeAt", "toString", "toUpperCase", "s", "descriptions", "sort", "slice", "join", "describeExpected", "describeFound", "SyntaxError", "parse", "input", "options", "peg$result", "peg$FAILED", "peg$startRuleFunctions", "start", "peg$parsestart", "peg$startRuleFunction", "peg$c3", "peg$literalExpectation", "peg$c4", "peg$c5", "peg$classExpectation", "peg$c8", "peg$c11", "peg$c14", "peg$c18", "peg$c22", "peg$c25", "peg$c28", "peg$c31", "peg$c33", "peg$c35", "peg$c36", "peg$c38", "peg$c39", "a", "peg$c40", "peg$c41", "peg$c43", "peg$c45", "op", "value", "operator", "peg$c48", "peg$c49", "peg$c50", "peg$c52", "peg$c53", "peg$c54", "b", "peg$c55", "d", "match", "peg$c57", "peg$c58", "peg$c59", "peg$c60", "peg$c61", "peg$c65", "peg$c66", "peg$c67", "peg$c69", "peg$c71", "peg$c72", "peg$c74", "peg$c75", "peg$c76", "peg$c80", "peg$c83", "peg$c86", "peg$c89", "peg$c92", "peg$c95", "peg$c98", "peg$c101", "peg$currPos", "peg$posDetailsCache", "line", "column", "peg$maxFailPos", "peg$maxFailExpected", "peg$silentFails", "startRule", "ignoreCase", "peg$computePosDetails", "pos", "p", "details", "peg$computeLocation", "startPos", "endPos", "startPosDetails", "endPosDetails", "offset", "peg$fail", "s0", "s1", "s2", "ss", "cached", "peg$resultsCache", "nextPos", "peg$parse_", "peg$parseselectors", "selectors", "peg$c1", "peg$parseidentifierName", "test", "char<PERSON>t", "peg$parsebinaryOp", "s3", "s4", "s5", "s6", "s7", "peg$parseselector", "concat", "map", "peg$parsesequence", "reduce", "memo", "rhs", "left", "right", "subject", "as", "peg$parseatom", "peg$parsewildcard", "peg$parseidentifier", "peg$parseattrName", "peg$parseattrEqOps", "substr", "peg$parsetype", "flgs", "peg$parseflags", "RegExp", "peg$parseregex", "peg$parseattrOps", "peg$parsestring", "leadingDecimals", "apply", "parseFloat", "peg$parsenumber", "peg$parsepath", "peg$parseattrValue", "peg$parseattr", "peg$parsefield", "peg$parsenegation", "peg$parsematches", "peg$parsehas", "nth", "peg$parsefirstChild", "nthLast", "peg$parselastChild", "parseInt", "peg$parsenthChild", "peg$parsenthLastChild", "peg$parseclass", "n", "index", "factory", "<PERSON><PERSON><PERSON>", "MATCHER_CACHE", "WeakMap", "getMatcher", "selector", "matcher", "get", "generateMatcher", "set", "toLowerCase", "ancestry", "nodeTypeKey", "split", "inPath", "ancestor", "fromPathIndex", "field", "k", "matchers", "estraverse", "unshift", "shift", "visitorKeys", "l", "sibling", "adjacent", "nthChild", "matchClass", "getVisitorKeys", "filter", "_typeof", "side", "listProp", "startIndex", "indexOf", "lowerBound", "idx", "ast", "altSubjects", "subjects", "results", "sel", "m", "succeedingAncestry", "parser", "query", "matches"], "mappings": "qgEA2BC,SAASA,EAAMC,GAGZ,IAAIC,EACAC,EACAC,EACAC,EACAC,EACAC,EAEJ,SAASC,EAASC,GACd,IAAcC,EAAKC,EAAfC,EAAM,GACV,IAAKF,KAAOD,EACJA,EAAII,eAAeH,KACnBC,EAAMF,EAAIC,GAENE,EAAIF,GADW,iBAARC,GAA4B,OAARA,EAChBH,EAASG,GAETA,GAIvB,OAAOC,EAgMX,SAASE,EAAUC,EAAQL,GACvBM,KAAKD,OAASA,EACdC,KAAKN,IAAMA,EAiBf,SAASO,EAAQC,EAAMC,EAAMC,EAAMC,GAC/BL,KAAKE,KAAOA,EACZF,KAAKG,KAAOA,EACZH,KAAKI,KAAOA,EACZJ,KAAKK,IAAMA,EAGf,SAASC,KAuHT,SAASC,EAAOL,GACZ,OAAY,MAARA,IAGmB,iBAATA,GAA0C,iBAAdA,EAAKM,MAGnD,SAASC,EAAWC,EAAUhB,GAC1B,OAAQgB,IAAaxB,EAAOyB,kBAAoBD,IAAaxB,EAAO0B,gBAAkB,eAAiBlB,EAG3G,SAASmB,EAA2BC,EAAWC,GAC3C,IAAK,IAAIC,EAAIF,EAAUG,OAAS,EAAGD,GAAK,IAAKA,EACzC,GAAIF,EAAUE,GAAGd,OAASa,EACtB,OAAO,EAGf,OAAO,EAwQX,SAASG,EAASC,EAAMC,GAEpB,OADiB,IAAId,GACHY,SAASC,EAAMC,GAQrC,SAASC,EAAmBC,EAASC,GACjC,IAAIC,EAiBJ,OAfAA,EAjnBJ,SAAoBC,EAAOC,GACvB,IAAIC,EAAMC,EAAKZ,EAAGa,EAKlB,IAHAD,EAAMH,EAAMR,OACZD,EAAI,EAEGY,GAGCF,EAAKD,EADTI,EAAUb,GADVW,EAAOC,IAAQ,KAGXA,EAAMD,GAENX,EAAIa,EAAU,EACdD,GAAOD,EAAO,GAGtB,OAAOX,EAimBEc,CAAWP,GAAQ,SAAgBQ,GACxC,OAAOA,EAAMC,MAAM,GAAKV,EAAQU,MAAM,MAG1CV,EAAQW,cAAgB,CAACX,EAAQU,MAAM,GAAIV,EAAQU,MAAM,IAErDR,IAAWD,EAAON,SAClBK,EAAQW,cAAc,GAAKV,EAAOC,GAAQQ,MAAM,KAGpDR,GAAU,IACI,IACVF,EAAQW,cAAc,GAAKV,EAAOC,GAAQQ,MAAM,IAG7CV,EA2GX,OAxtBApC,EAAS,CACLgD,qBAAsB,uBACtBC,kBAAmB,oBACnBC,gBAAiB,kBACjBC,aAAc,eACdC,wBAAyB,0BACzBC,gBAAiB,kBACjBC,eAAgB,iBAChBC,iBAAkB,mBAClBC,eAAgB,iBAChBC,eAAgB,iBAChBC,YAAa,cACbC,gBAAiB,kBACjBC,UAAW,YACXC,iBAAkB,mBAClBC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,wBAAyB,0BACzBC,sBAAuB,wBACvBC,kBAAmB,oBACnBC,kBAAmB,oBACnBC,mBAAoB,qBACpBC,iBAAkB,mBAClBC,eAAgB,iBAChBC,qBAAsB,uBACtBC,yBAA0B,2BAC1BC,uBAAwB,yBACxBC,gBAAiB,kBACjBC,oBAAqB,sBACrBC,aAAc,eACdC,eAAgB,iBAChBC,eAAgB,iBAChBC,oBAAqB,sBACrBC,mBAAoB,qBACpBC,oBAAqB,sBACrBC,WAAY,aACZC,YAAa,cACbC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,uBAAwB,yBACxBC,yBAA0B,2BAC1BC,gBAAiB,kBACjBC,QAAS,UACTC,iBAAkB,mBAClBC,kBAAmB,oBACnBC,iBAAkB,mBAClBC,aAAc,eACdC,iBAAkB,mBAClBC,gBAAiB,kBACjBC,cAAe,gBACfvE,iBAAkB,mBAClBC,cAAe,gBACfuE,kBAAmB,oBACnBC,QAAS,UACTC,SAAU,WACVC,mBAAoB,qBACpBC,YAAa,cACbC,gBAAiB,kBACjBC,mBAAoB,qBACpBC,cAAe,gBACfC,MAAO,QACPC,gBAAiB,kBACjBC,WAAY,aACZC,yBAA0B,2BAC1BC,gBAAiB,kBACjBC,gBAAiB,kBACjBC,eAAgB,iBAChBC,eAAgB,iBAChBC,aAAc,eACdC,gBAAiB,kBACjBC,iBAAkB,mBAClBC,oBAAqB,sBACrBC,mBAAoB,qBACpBC,eAAgB,iBAChBC,cAAe,gBACfC,gBAAiB,mBAGrBtH,EAAc,CACV8C,qBAAsB,CAAC,OAAQ,SAC/BC,kBAAmB,CAAC,OAAQ,SAC5BC,gBAAiB,CAAC,YAClBC,aAAc,CAAC,YACfC,wBAAyB,CAAC,SAAU,QACpCC,gBAAiB,CAAC,YAClBC,eAAgB,CAAC,QACjBC,iBAAkB,CAAC,OAAQ,SAC3BC,eAAgB,CAAC,SACjBC,eAAgB,CAAC,SAAU,aAC3BC,YAAa,CAAC,QAAS,QACvBC,gBAAiB,CAAC,cAClBC,UAAW,CAAC,QACZC,iBAAkB,CAAC,KAAM,aAAc,QACvCC,gBAAiB,CAAC,KAAM,aAAc,QACtCC,mBAAoB,CAAC,OAAQ,SAC7BC,wBAAyB,CAAC,SAAU,SAAU,QAC9CC,sBAAuB,CAAC,OAAQ,aAAc,aAC9CC,kBAAmB,CAAC,SACpBC,kBAAmB,GACnBC,mBAAoB,GACpBC,iBAAkB,CAAC,OAAQ,QAC3BC,eAAgB,GAChBC,qBAAsB,CAAC,UACvBC,yBAA0B,CAAC,eAC3BC,uBAAwB,CAAC,cAAe,aAAc,UACtDC,gBAAiB,CAAC,WAAY,SAC9BC,oBAAqB,CAAC,cACtBC,aAAc,CAAC,OAAQ,OAAQ,SAAU,QACzCC,eAAgB,CAAC,OAAQ,QAAS,QAClCC,eAAgB,CAAC,OAAQ,QAAS,QAClCC,oBAAqB,CAAC,KAAM,SAAU,QACtCC,mBAAoB,CAAC,KAAM,SAAU,QACrCC,oBAAqB,CAAC,SAAU,SAAU,QAC1CC,WAAY,GACZC,YAAa,CAAC,OAAQ,aAAc,aACpCC,iBAAkB,CAAC,UACnBC,kBAAmB,CAAC,aAAc,UAClCC,uBAAwB,CAAC,SACzBC,yBAA0B,CAAC,SAC3BC,gBAAiB,CAAC,WAAY,SAC9BC,QAAS,GACTC,iBAAkB,CAAC,QAAS,QAC5BC,kBAAmB,CAAC,OAAQ,SAC5BC,iBAAkB,CAAC,SAAU,YAC7BC,aAAc,CAAC,OAAQ,YACvBC,iBAAkB,CAAC,MAAO,SAC1BC,gBAAiB,GACjBC,cAAe,CAAC,SAAU,aAC1BvE,iBAAkB,CAAC,cACnBC,cAAe,CAAC,cAChBuE,kBAAmB,GACnBC,QAAS,CAAC,QACVC,SAAU,CAAC,MAAO,SAClBC,mBAAoB,CAAC,MAAO,SAC5BC,YAAa,CAAE,YACfC,gBAAiB,CAAC,YAClBC,mBAAoB,CAAC,eACrBC,cAAe,CAAC,YAChBC,MAAO,GACPC,gBAAiB,CAAC,eAAgB,SAClCC,WAAY,CAAC,OAAQ,cACrBC,yBAA0B,CAAC,MAAO,SAClCC,gBAAiB,GACjBC,gBAAiB,CAAC,SAAU,eAC5BC,eAAgB,GAChBC,eAAgB,CAAC,YACjBC,aAAc,CAAC,QAAS,UAAW,aACnCC,gBAAiB,CAAC,YAClBC,iBAAkB,CAAC,YACnBC,oBAAqB,CAAC,gBACtBC,mBAAoB,CAAC,KAAM,QAC3BC,eAAgB,CAAC,OAAQ,QACzBC,cAAe,CAAC,SAAU,QAC1BC,gBAAiB,CAAC,aAQtBvH,EAAgB,CACZwH,MALJtH,EAAQ,GAMJuH,KALJtH,EAAO,GAMHuH,OALJtH,EAAS,IAaTO,EAAUgH,UAAUC,QAAU,SAAiB7G,GAC3CF,KAAKD,OAAOC,KAAKN,KAAOQ,GAG5BJ,EAAUgH,UAAUE,OAAS,WACzB,OAAIC,MAAMC,QAAQlH,KAAKD,SACnBC,KAAKD,OAAOoH,OAAOnH,KAAKN,IAAK,IACtB,IAEPM,KAAK+G,QAAQ,OACN,IAefzG,EAAWwG,UAAU3G,KAAO,WACxB,IAAIa,EAAGoG,EAAIC,EAAGC,EAAIC,EAElB,SAASC,EAAUD,EAAQpH,GACvB,GAAI8G,MAAMC,QAAQ/G,GACd,IAAKkH,EAAI,EAAGC,EAAKnH,EAAKc,OAAQoG,EAAIC,IAAMD,EACpCE,EAAOE,KAAKtH,EAAKkH,SAGrBE,EAAOE,KAAKtH,GAKpB,IAAKH,KAAK0H,UAAUvH,KAChB,OAAO,KAKX,IADAoH,EAAS,GACJvG,EAAI,EAAGoG,EAAKpH,KAAK2H,YAAY1G,OAAQD,EAAIoG,IAAMpG,EAEhDwG,EAAUD,EADAvH,KAAK2H,YAAY3G,GACDb,MAG9B,OADAqH,EAAUD,EAAQvH,KAAK0H,UAAUvH,MAC1BoH,GAKXjH,EAAWwG,UAAUtG,KAAO,WAExB,OADWR,KAAK6B,UACJrB,MAAQR,KAAK0H,UAAUtH,MAKvCE,EAAWwG,UAAUc,QAAU,WAC3B,IAAI5G,EAAGoG,EAAIG,EAIX,IADAA,EAAS,GACJvG,EAAI,EAAGoG,EAAKpH,KAAK2H,YAAY1G,OAAQD,EAAIoG,IAAMpG,EAChDuG,EAAOE,KAAKzH,KAAK2H,YAAY3G,GAAGd,MAGpC,OAAOqH,GAKXjH,EAAWwG,UAAUjF,QAAU,WAC3B,OAAO7B,KAAK0H,UAAUxH,MAG1BI,EAAWwG,UAAUe,UAAY,SAAmBC,EAAUC,GAC1D,IAAIC,EAAUT,EAYd,OAVAA,OAASU,EAETD,EAAYhI,KAAK0H,UACjB1H,KAAK0H,UAAYK,EACjB/H,KAAKkI,QAAU,KACXJ,IACAP,EAASO,EAASK,KAAKnI,KAAM+H,EAAQ7H,KAAMF,KAAK2H,YAAY3H,KAAK2H,YAAY1G,OAAS,GAAGf,OAE7FF,KAAK0H,UAAYM,EAEVT,GAKXjH,EAAWwG,UAAUsB,OAAS,SAAgBC,GAC1CrI,KAAKkI,QAAUG,GAKnB/H,EAAWwG,UAAUwB,KAAO,WACxBtI,KAAKoI,OAAO9I,IAKhBgB,EAAWwG,UAAiB,MAAI,WAC5B9G,KAAKoI,OAAO/I,IAKhBiB,EAAWwG,UAAUE,OAAS,WAC1BhH,KAAKoI,OAAO7I,IAGhBe,EAAWwG,UAAUyB,aAAe,SAASpH,EAAMC,GAC/CpB,KAAKoB,QAAUA,EACfpB,KAAKmB,KAAOA,EACZnB,KAAKwI,WAAa,GAClBxI,KAAK2H,YAAc,GACnB3H,KAAK0H,UAAY,KACjB1H,KAAKkI,QAAU,KACflI,KAAKyI,WAAa,KACO,cAArBrH,EAAQsH,SACR1I,KAAKyI,WAAaE,OAAOC,KACU,mBAArBxH,EAAQsH,WACtB1I,KAAKyI,WAAarH,EAAQsH,UAG9B1I,KAAK6I,OAASzJ,EACVgC,EAAQwH,OACR5I,KAAK6I,OAASF,OAAOG,OAAOH,OAAOI,OAAO/I,KAAK6I,QAASzH,EAAQwH,QAwBxEtI,EAAWwG,UAAU5F,SAAW,SAAkBC,EAAMC,GACpD,IAAI4H,EACAlI,EACAiH,EACA7H,EACAQ,EACAd,EACAF,EACAmC,EACAoH,EACAC,EACAnI,EACAoI,EAcJ,IAZAnJ,KAAKuI,aAAapH,EAAMC,GAExB+H,EAAW,GAGXH,EAAWhJ,KAAKwI,WAChB1H,EAAYd,KAAK2H,YAGjBqB,EAASvB,KAAK,IAAIxH,EAAQkB,EAAM,KAAM,KAAM,OAC5CL,EAAU2G,KAAK,IAAIxH,EAAQ,KAAM,KAAM,KAAM,OAEtC+I,EAAS/H,QAGZ,IAFA8G,EAAUiB,EAASI,SAEHD,GAWhB,GAAIpB,EAAQ7H,KAAM,CAId,GAFAN,EAAMI,KAAK6H,UAAUzG,EAAQiI,MAAOtB,GAEhC/H,KAAKkI,UAAY7I,GAASO,IAAQP,EAClC,OAMJ,GAHA2J,EAASvB,KAAK0B,GACdrI,EAAU2G,KAAKM,GAEX/H,KAAKkI,UAAY5I,GAAQM,IAAQN,EACjC,SAMJ,GAFAoB,GADAR,EAAO6H,EAAQ7H,MACCM,MAAQuH,EAAQ3H,OAChC8I,EAAalJ,KAAK6I,OAAOnI,IACR,CACb,IAAIV,KAAKyI,WAGL,MAAM,IAAIa,MAAM,qBAAuB5I,EAAW,KAFlDwI,EAAalJ,KAAKyI,WAAWvI,GAOrC,IADA2B,EAAUqH,EAAWjI,QACbY,GAAW,IAAM,GAGrB,GADAd,EAAYb,EADZR,EAAMwJ,EAAWrH,IAMjB,GAAIoF,MAAMC,QAAQnG,IAEd,IADAkI,EAAWlI,EAAUE,QACbgI,GAAY,IAAM,GACtB,GAAKlI,EAAUkI,KAIXpI,EAA2BC,EAAWC,EAAUkI,IAApD,CAIA,GAAIxI,EAAWC,EAAUwI,EAAWrH,IAChCkG,EAAU,IAAI9H,EAAQc,EAAUkI,GAAW,CAACvJ,EAAKuJ,GAAW,WAAY,UACrE,CAAA,IAAI1I,EAAOQ,EAAUkI,IAGxB,SAFAlB,EAAU,IAAI9H,EAAQc,EAAUkI,GAAW,CAACvJ,EAAKuJ,GAAW,KAAM,MAItED,EAASvB,KAAKM,SAEf,GAAIxH,EAAOQ,GAAY,CAC1B,GAAIF,EAA2BC,EAAWC,GACxC,SAGFiI,EAASvB,KAAK,IAAIxH,EAAQc,EAAWrB,EAAK,KAAM,cAjExD,GAJAqI,EAAUjH,EAAUsI,MAEpBxJ,EAAMI,KAAK6H,UAAUzG,EAAQmI,MAAOxB,GAEhC/H,KAAKkI,UAAY7I,GAASO,IAAQP,EAClC,QAuEhBiB,EAAWwG,UAAUC,QAAU,SAAiB5F,EAAMC,GAClD,IAAI4H,EACAlI,EACAZ,EACAQ,EACAc,EACAuG,EACAlG,EACAoH,EACAC,EACAnI,EACAoI,EACAK,EACA9J,EAEJ,SAAS+J,EAAW1B,GAChB,IAAI/G,EACAtB,EACAgK,EACA3J,EAEJ,GAAIgI,EAAQ1H,IAAI2G,SAOZ,IALAtH,EAAMqI,EAAQ1H,IAAIX,IAClBK,EAASgI,EAAQ1H,IAAIN,OAGrBiB,EAAIgI,EAAS/H,OACND,KAEH,IADA0I,EAAWV,EAAShI,IACPX,KAAOqJ,EAASrJ,IAAIN,SAAWA,EAAQ,CAChD,GAAK2J,EAASrJ,IAAIX,IAAMA,EACpB,QAEFgK,EAASrJ,IAAIX,KAsB/B,IAhBAM,KAAKuI,aAAapH,EAAMC,GAExB+H,EAAW,GAGXH,EAAWhJ,KAAKwI,WAChB1H,EAAYd,KAAK2H,YAMjBI,EAAU,IAAI9H,EAAQkB,EAAM,KAAM,KAAM,IAAIrB,EAH5C0J,EAAQ,CACJrI,KAAMA,GAEmD,SAC7D6H,EAASvB,KAAKM,GACdjH,EAAU2G,KAAKM,GAERiB,EAAS/H,QAGZ,IAFA8G,EAAUiB,EAASI,SAEHD,EAAhB,CAqCA,QAXelB,KAJfzG,EAASxB,KAAK6H,UAAUzG,EAAQiI,MAAOtB,KAIXvG,IAAWnC,GAASmC,IAAWlC,GAAQkC,IAAWjC,IAE1EwI,EAAQ1H,IAAI0G,QAAQvF,GACpBuG,EAAQ7H,KAAOsB,GAGfxB,KAAKkI,UAAY3I,GAAUiC,IAAWjC,IACtCkK,EAAW1B,GACXA,EAAQ7H,KAAO,MAGfF,KAAKkI,UAAY7I,GAASmC,IAAWnC,EACrC,OAAOmK,EAAMrI,KAKjB,IADAjB,EAAO6H,EAAQ7H,QAKf8I,EAASvB,KAAK0B,GACdrI,EAAU2G,KAAKM,GAEX/H,KAAKkI,UAAY5I,GAAQkC,IAAWlC,GAAxC,CAMA,GAFAoB,EAAWR,EAAKM,MAAQuH,EAAQ3H,OAChC8I,EAAalJ,KAAK6I,OAAOnI,IACR,CACb,IAAIV,KAAKyI,WAGL,MAAM,IAAIa,MAAM,qBAAuB5I,EAAW,KAFlDwI,EAAalJ,KAAKyI,WAAWvI,GAOrC,IADA2B,EAAUqH,EAAWjI,QACbY,GAAW,IAAM,GAGrB,GADAd,EAAYb,EADZR,EAAMwJ,EAAWrH,IAMjB,GAAIoF,MAAMC,QAAQnG,IAEd,IADAkI,EAAWlI,EAAUE,QACbgI,GAAY,IAAM,GACtB,GAAKlI,EAAUkI,GAAf,CAGA,GAAIxI,EAAWC,EAAUwI,EAAWrH,IAChCkG,EAAU,IAAI9H,EAAQc,EAAUkI,GAAW,CAACvJ,EAAKuJ,GAAW,WAAY,IAAInJ,EAAUiB,EAAWkI,QAC9F,CAAA,IAAI1I,EAAOQ,EAAUkI,IAGxB,SAFAlB,EAAU,IAAI9H,EAAQc,EAAUkI,GAAW,CAACvJ,EAAKuJ,GAAW,KAAM,IAAInJ,EAAUiB,EAAWkI,IAI/FD,EAASvB,KAAKM,SAEXxH,EAAOQ,IACdiI,EAASvB,KAAK,IAAIxH,EAAQc,EAAWrB,EAAK,KAAM,IAAII,EAAUI,EAAMR,WAxExE,GAfAqI,EAAUjH,EAAUsI,WAMLnB,KAJfzG,EAASxB,KAAK6H,UAAUzG,EAAQmI,MAAOxB,KAIXvG,IAAWnC,GAASmC,IAAWlC,GAAQkC,IAAWjC,GAE1EwI,EAAQ1H,IAAI0G,QAAQvF,GAGpBxB,KAAKkI,UAAY3I,GAAUiC,IAAWjC,GACtCkK,EAAW1B,GAGX/H,KAAKkI,UAAY7I,GAASmC,IAAWnC,EACrC,OAAOmK,EAAMrI,KA4EzB,OAAOqI,EAAMrI,MAiIjBlC,EAAQC,OAASA,EACjBD,EAAQiC,SAAWA,EACnBjC,EAAQ8H,QA3HR,SAAiB5F,EAAMC,GAEnB,OADiB,IAAId,GACHyG,QAAQ5F,EAAMC,IA0HpCnC,EAAQ0K,eAlGR,SAAwBC,EAAMC,EAAkBtI,GAE5C,IAAmBD,EAASM,EAAKZ,EAAG8I,EAAhCC,EAAW,GAEf,IAAKH,EAAK5H,MACN,MAAM,IAAIsH,MAAM,0CAIpB,IAAK/H,EAAON,OAAQ,CAChB,GAAI4I,EAAiB5I,OAAQ,CACzB,IAAKD,EAAI,EAAGY,EAAMiI,EAAiB5I,OAAQD,EAAIY,EAAKZ,GAAK,GACrDM,EAAU9B,EAASqK,EAAiB7I,KAC5BiB,cAAgB,CAAC,EAAG2H,EAAK5H,MAAM,IACvC+H,EAAStC,KAAKnG,GAElBsI,EAAKI,gBAAkBD,EAE3B,OAAOH,EAGX,IAAK5I,EAAI,EAAGY,EAAMiI,EAAiB5I,OAAQD,EAAIY,EAAKZ,GAAK,EACrD+I,EAAStC,KAAKpG,EAAmB7B,EAASqK,EAAiB7I,IAAKO,IAsEpE,OAlEAuI,EAAS,EACT5I,EAAS0I,EAAM,CACXP,MAAO,SAAUnJ,GAGb,IAFA,IAAIoB,EAEGwI,EAASC,EAAS9I,WACrBK,EAAUyI,EAASD,IACP7H,cAAc,GAAK/B,EAAK8B,MAAM,KAItCV,EAAQW,cAAc,KAAO/B,EAAK8B,MAAM,IACnC9B,EAAK8J,kBACN9J,EAAK8J,gBAAkB,IAE3B9J,EAAK8J,gBAAgBvC,KAAKnG,GAC1ByI,EAAS5C,OAAO2C,EAAQ,IAExBA,GAAU,EAKlB,OAAIA,IAAWC,EAAS9I,OACb9B,EAAcwH,MAGrBoD,EAASD,GAAQ7H,cAAc,GAAK/B,EAAK8B,MAAM,GACxC7C,EAAcyH,UADzB,KAMRkD,EAAS,EACT5I,EAAS0I,EAAM,CACXL,MAAO,SAAUrJ,GAGb,IAFA,IAAIoB,EAEGwI,EAASC,EAAS9I,SACrBK,EAAUyI,EAASD,KACf5J,EAAK8B,MAAM,GAAKV,EAAQW,cAAc,MAItC/B,EAAK8B,MAAM,KAAOV,EAAQW,cAAc,IACnC/B,EAAK+J,mBACN/J,EAAK+J,iBAAmB,IAE5B/J,EAAK+J,iBAAiBxC,KAAKnG,GAC3ByI,EAAS5C,OAAO2C,EAAQ,IAExBA,GAAU,EAKlB,OAAIA,IAAWC,EAAS9I,OACb9B,EAAcwH,MAGrBoD,EAASD,GAAQ7H,cAAc,GAAK/B,EAAK8B,MAAM,GACxC7C,EAAcyH,UADzB,KAMDgD,GAOX3K,EAAQG,YAAcA,EACtBH,EAAQE,cAAgBA,EACxBF,EAAQqB,WAAaA,EACrBrB,EAAQiL,iBAAmB,WAAc,OAAOlL,EAAM,KAE/CC,EAvwBV,CAwwBCA,uBC3xByCkL,EAAOlL,UAC9CkL,UAEK,WASP,SAASC,EAAgBC,EAASC,EAAUC,EAAOC,GACjDxK,KAAKqK,QAAWA,EAChBrK,KAAKsK,SAAWA,EAChBtK,KAAKuK,MAAWA,EAChBvK,KAAKwK,SAAWA,EAChBxK,KAAKyK,KAAW,cAEuB,mBAA5BnB,MAAMoB,mBACfpB,MAAMoB,kBAAkB1K,KAAMoK,GAq9ElC,OAn+EA,SAAsBO,EAAO5K,GAC3B,SAAS6K,IAAS5K,KAAK6K,YAAcF,EACrCC,EAAK9D,UAAY/G,EAAO+G,UACxB6D,EAAM7D,UAAY,IAAI8D,EAexBE,CAAaV,EAAiBd,OAE9Bc,EAAgBW,aAAe,SAAST,EAAUC,GAChD,IAAIS,EAA2B,CACzBC,QAAS,SAASC,GAChB,MAAO,IAAOC,EAAcD,EAAYE,MAAQ,KAGlDC,MAAS,SAASH,GAChB,IACIlK,EADAsK,EAAe,GAGnB,IAAKtK,EAAI,EAAGA,EAAIkK,EAAYK,MAAMtK,OAAQD,IACxCsK,GAAgBJ,EAAYK,MAAMvK,aAAciG,MAC5CuE,EAAYN,EAAYK,MAAMvK,GAAG,IAAM,IAAMwK,EAAYN,EAAYK,MAAMvK,GAAG,IAC9EwK,EAAYN,EAAYK,MAAMvK,IAGpC,MAAO,KAAOkK,EAAYO,SAAW,IAAM,IAAMH,EAAe,KAGlEI,IAAK,SAASR,GACZ,MAAO,iBAGTS,IAAK,SAAST,GACZ,MAAO,gBAGTU,MAAO,SAASV,GACd,OAAOA,EAAYW,cAI3B,SAASC,EAAIC,GACX,OAAOA,EAAGC,WAAW,GAAGC,SAAS,IAAIC,cAGvC,SAASf,EAAcgB,GACrB,OAAOA,EACJpF,QAAQ,MAAO,QACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASgF,GAAM,MAAO,OAASD,EAAIC,MACpEhF,QAAQ,yBAAyB,SAASgF,GAAM,MAAO,MAASD,EAAIC,MAGzE,SAASP,EAAYW,GACnB,OAAOA,EACJpF,QAAQ,MAAO,QACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASgF,GAAM,MAAO,OAASD,EAAIC,MACpEhF,QAAQ,yBAAyB,SAASgF,GAAM,MAAO,MAASD,EAAIC,MA6CzE,MAAO,YAtCP,SAA0BzB,GACxB,IACItJ,EAAGqG,EANoB6D,EAKvBkB,EAAe,IAAInF,MAAMqD,EAASrJ,QAGtC,IAAKD,EAAI,EAAGA,EAAIsJ,EAASrJ,OAAQD,IAC/BoL,EAAapL,IATYkK,EASaZ,EAAStJ,GAR1CgK,EAAyBE,EAAY1K,MAAM0K,IAalD,GAFAkB,EAAaC,OAETD,EAAanL,OAAS,EAAG,CAC3B,IAAKD,EAAI,EAAGqG,EAAI,EAAGrG,EAAIoL,EAAanL,OAAQD,IACtCoL,EAAapL,EAAI,KAAOoL,EAAapL,KACvCoL,EAAa/E,GAAK+E,EAAapL,GAC/BqG,KAGJ+E,EAAanL,OAASoG,EAGxB,OAAQ+E,EAAanL,QACnB,KAAK,EACH,OAAOmL,EAAa,GAEtB,KAAK,EACH,OAAOA,EAAa,GAAK,OAASA,EAAa,GAEjD,QACE,OAAOA,EAAaE,MAAM,GAAI,GAAGC,KAAK,MAClC,QACAH,EAAaA,EAAanL,OAAS,IAQxBuL,CAAiBlC,GAAY,QAJlD,SAAuBC,GACrB,OAAOA,EAAQ,IAAOY,EAAcZ,GAAS,IAAO,eAGMkC,CAAclC,GAAS,WAu2E9E,CACLmC,YAAatC,EACbuC,MAt2EF,SAAmBC,EAAOC,GACxBA,OAAsB,IAAZA,EAAqBA,EAAU,GAEzC,IAkJIC,EAwH8BxC,EAAUC,EAAOC,EA1Q/CuC,EAAa,GAEbC,EAAyB,CAAEC,MAAOC,IAClCC,EAAyBD,GAOzBE,EAASC,GAAuB,KAAK,GACrCC,EAAS,uBACTC,EAASC,GAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAAM,GAAM,GAGjHC,EAASJ,GAAuB,KAAK,GAGrCK,EAAUL,GAAuB,KAAK,GAGtCM,EAAUN,GAAuB,KAAK,GAItCO,EAAUP,GAAuB,KAAK,GAUtCQ,EAAUR,GAAuB,KAAK,GAOtCS,EAAUT,GAAuB,KAAK,GAGtCU,EAAUV,GAAuB,KAAK,GAGtCW,EAAUX,GAAuB,KAAK,GAEtCY,EAAUZ,GAAuB,KAAK,GAEtCa,EAAU,SACVC,EAAUX,GAAqB,CAAC,IAAK,IAAK,MAAM,GAAO,GAEvDY,EAAUf,GAAuB,KAAK,GACtCgB,EAAU,SAASC,GAAK,OAAQA,GAAK,IAAM,KAC3CC,EAAU,QACVC,EAAUhB,GAAqB,CAAC,IAAK,MAAM,GAAO,GAElDiB,EAAUpB,GAAuB,KAAK,GAItCqB,EAAU,SAASjE,EAAMkE,EAAIC,GACvB,MAAO,CAAEpO,KAAM,YAAaiK,KAAMA,EAAMoE,SAAUF,EAAIC,MAAOA,IAInEE,EAAUzB,GAAuB,KAAM,GACvC0B,EAAU,UACVC,EAAUxB,GAAqB,CAAC,KAAM,MAAO,GAAM,GAEnDyB,EAAU5B,GAAuB,MAAM,GACvC6B,EAmHK,CAAE1O,KAAM,OAlHb2O,EAAU,SAASb,EAAGc,GAAK,OAAOd,EAAIc,GACtCC,EAAU,SAASC,GACX,MAAO,CAAE9O,KAAM,UAAWoO,OAkvEfzC,EAlvEkCmD,EAAE/C,KAAK,IAmvErDJ,EAAEpF,QAAQ,UAAU,SAASwI,EAAOxD,GACzC,OAAOA,GACL,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,QAAS,OAAOA,QATtB,IAAqBI,GA/uEnBqD,EAAUnC,GAAuB,KAAK,GACtCoC,EAAU,UACVC,EAAUlC,GAAqB,CAAC,KAAM,MAAM,GAAM,GAClDmC,EAAU,SACVC,EAAUpC,GAAqB,CAAC,CAAC,IAAK,OAAO,GAAO,GAQpDqC,EAAUxC,GAAuB,SAAS,GAC1CyC,EAAU,SACVC,EAAUvC,GAAqB,CAAC,IAAK,MAAM,GAAM,GAEjDwC,EAAU3C,GAAuB,KAAK,GAEtC4C,EAAU,UACVC,EAAU1C,GAAqB,CAAC,IAAK,IAAK,IAAK,MAAM,GAAO,GAE5D2C,EAAU9C,GAAuB,KAAK,GACtC+C,EAAU,SACVC,EAAU7C,GAAqB,CAAC,MAAM,GAAM,GAQ5C8C,EAAUjD,GAAuB,SAAS,GAG1CkD,EAAUlD,GAAuB,aAAa,GAG9CmD,EAAUnD,GAAuB,SAAS,GAG1CoD,GAAUpD,GAAuB,gBAAgB,GAGjDqD,GAAUrD,GAAuB,eAAe,GAGhDsD,GAAUtD,GAAuB,eAAe,GAGhDuD,GAAUvD,GAAuB,oBAAoB,GAGrDwD,GAAWxD,GAAuB,KAAK,GAKvCyD,GAAuB,EAEvBC,GAAuB,CAAC,CAAEC,KAAM,EAAGC,OAAQ,IAC3CC,GAAuB,EACvBC,GAAuB,GACvBC,GAEmB,GAIvB,GAAI,cAAevE,EAAS,CAC1B,KAAMA,EAAQwE,aAAarE,GACzB,MAAM,IAAI1D,MAAM,mCAAqCuD,EAAQwE,UAAY,MAG3ElE,EAAwBH,EAAuBH,EAAQwE,WA2BzD,SAAShE,GAAuBjC,EAAMkG,GACpC,MAAO,CAAE9Q,KAAM,UAAW4K,KAAMA,EAAMkG,WAAYA,GAGpD,SAAS9D,GAAqBjC,EAAOE,EAAU6F,GAC7C,MAAO,CAAE9Q,KAAM,QAAS+K,MAAOA,EAAOE,SAAUA,EAAU6F,WAAYA,GAexE,SAASC,GAAsBC,GAC7B,IAAwCC,EAApCC,EAAUX,GAAoBS,GAElC,GAAIE,EACF,OAAOA,EAGP,IADAD,EAAID,EAAM,GACFT,GAAoBU,IAC1BA,IASF,IALAC,EAAU,CACRV,MAFFU,EAAUX,GAAoBU,IAEZT,KAChBC,OAAQS,EAAQT,QAGXQ,EAAID,GACmB,KAAxB5E,EAAMZ,WAAWyF,IACnBC,EAAQV,OACRU,EAAQT,OAAS,GAEjBS,EAAQT,SAGVQ,IAIF,OADAV,GAAoBS,GAAOE,EACpBA,EAIX,SAASC,GAAoBC,EAAUC,GACrC,IAAIC,EAAkBP,GAAsBK,GACxCG,EAAkBR,GAAsBM,GAE5C,MAAO,CACL5E,MAAO,CACL+E,OAAQJ,EACRZ,KAAQc,EAAgBd,KACxBC,OAAQa,EAAgBb,QAE1BtF,IAAK,CACHqG,OAAQH,EACRb,KAAQe,EAAcf,KACtBC,OAAQc,EAAcd,SAK5B,SAASgB,GAAS3H,GACZwG,GAAcI,KAEdJ,GAAcI,KAChBA,GAAiBJ,GACjBK,GAAsB,IAGxBA,GAAoB1J,KAAK6C,IAgB3B,SAAS4C,KACP,IAAIgF,EAAIC,EAAIC,EA/QQC,EAiRhB3S,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,IACLqB,EAAKM,QACM1F,IACTqF,EAAKM,QACM3F,GACJ0F,OACM1F,EAGTmF,EADAC,EAjSqB,KADPE,EAkSFD,GAjSFnR,OAAeoR,EAAG,GAAK,CAAE7R,KAAM,UAAWmS,UAAWN,IA4SnEvB,GAAcoB,EACdA,EAAKnF,GAEHmF,IAAOnF,IACTmF,EAAKpB,IACLqB,EAAKM,QACM1F,IAEToF,OAAKS,GAEPV,EAAKC,GAGPI,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GAGT,SAASO,KACP,IAAIP,EAAIC,EAEJzS,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAWhB,IARA2K,EAAK,GACiC,KAAlCtF,EAAMZ,WAAW8E,KACnBqB,EAzUS,IA0UTrB,OAEAqB,EAAKpF,EACwBkF,GAAS7E,IAEjC+E,IAAOpF,GACZmF,EAAGzK,KAAK0K,GAC8B,KAAlCvF,EAAMZ,WAAW8E,KACnBqB,EAlVO,IAmVPrB,OAEAqB,EAAKpF,EACwBkF,GAAS7E,IAM1C,OAFAmF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAGT,SAASW,KACP,IAAIX,EAAIC,EAAIC,EAER1S,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAYhB,GARA4K,EAAK,GACD7E,EAAOwF,KAAKlG,EAAMmG,OAAOjC,MAC3BsB,EAAKxF,EAAMmG,OAAOjC,IAClBA,OAEAsB,EAAKrF,EACwBkF,GAAS1E,IAEpC6E,IAAOrF,EACT,KAAOqF,IAAOrF,GACZoF,EAAG1K,KAAK2K,GACJ9E,EAAOwF,KAAKlG,EAAMmG,OAAOjC,MAC3BsB,EAAKxF,EAAMmG,OAAOjC,IAClBA,OAEAsB,EAAKrF,EACwBkF,GAAS1E,SAI1C4E,EAAKpF,EAUP,OARIoF,IAAOpF,IAEToF,EAAYA,EAhYoB5F,KAAK,KAkYvC2F,EAAKC,EAELI,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAGT,SAASc,KACP,IAAId,EAAIC,EAAIC,EAER1S,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,IACLqB,EAAKM,QACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBsB,EAxZO,IAyZPtB,OAEAsB,EAAKrF,EACwBkF,GAASxE,IAEpC2E,IAAOrF,GACJ0F,OACM1F,EAGTmF,EADAC,EAhayB,SAua3BrB,GAAcoB,EACdA,EAAKnF,KAGP+D,GAAcoB,EACdA,EAAKnF,GAEHmF,IAAOnF,IACTmF,EAAKpB,IACLqB,EAAKM,QACM1F,GAC6B,MAAlCH,EAAMZ,WAAW8E,KACnBsB,EAlbM,IAmbNtB,OAEAsB,EAAKrF,EACwBkF,GAASvE,IAEpC0E,IAAOrF,GACJ0F,OACM1F,EAGTmF,EADAC,EA1bwB,WAic1BrB,GAAcoB,EACdA,EAAKnF,KAGP+D,GAAcoB,EACdA,EAAKnF,GAEHmF,IAAOnF,IACTmF,EAAKpB,IACLqB,EAAKM,QACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBsB,EA5cI,IA6cJtB,OAEAsB,EAAKrF,EACwBkF,GAAStE,IAEpCyE,IAAOrF,GACJ0F,OACM1F,EAGTmF,EADAC,EApdsB,YA2dxBrB,GAAcoB,EACdA,EAAKnF,KAGP+D,GAAcoB,EACdA,EAAKnF,GAEHmF,IAAOnF,IACTmF,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EAlfG,IAmfHrB,OAEAqB,EAAKpF,EACwBkF,GAAS7E,IAEpC+E,IAAOpF,IACTqF,EAAKK,QACM1F,EAGTmF,EADAC,EA9esB,cAqfxBrB,GAAcoB,EACdA,EAAKnF,MAMbwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GAGT,SAASQ,KACP,IAAIR,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAAIC,EAAIC,EAE5B3T,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAKhB,GAFA2K,EAAKpB,IACLqB,EAAKmB,QACMvG,EAAY,CAmCrB,IAlCAqF,EAAK,GACLa,EAAKnC,IACLoC,EAAKT,QACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBqC,EAphBM,IAqhBNrC,OAEAqC,EAAKpG,EACwBkF,GAASrE,IAEpCuF,IAAOpG,IACTqG,EAAKX,QACM1F,IACTsG,EAAKC,QACMvG,EAETkG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBvC,GAAcmC,EACdA,EAAKlG,KAGP+D,GAAcmC,EACdA,EAAKlG,GAEAkG,IAAOlG,GACZqF,EAAG3K,KAAKwL,GACRA,EAAKnC,IACLoC,EAAKT,QACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBqC,EAvjBI,IAwjBJrC,OAEAqC,EAAKpG,EACwBkF,GAASrE,IAEpCuF,IAAOpG,IACTqG,EAAKX,QACM1F,IACTsG,EAAKC,QACMvG,EAETkG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBvC,GAAcmC,EACdA,EAAKlG,KAGP+D,GAAcmC,EACdA,EAAKlG,GAGLqF,IAAOrF,EAGTmF,EADAC,EAplBO,CAolBMA,GAplBFoB,OAolBMnB,EAplBIoB,KAAI,SAAUrH,GAAK,OAAOA,EAAE,QAulBjD2E,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAGT,SAASoB,KACP,IAAIpB,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAnmBH7E,EAqmBjB5O,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAKhB,GAFA2K,EAAKpB,IACLqB,EAAKsB,QACM1G,EAAY,CAiBrB,IAhBAqF,EAAK,GACLa,EAAKnC,IACLoC,EAAKF,QACMjG,IACToG,EAAKM,QACM1G,EAETkG,EADAC,EAAK,CAACA,EAAIC,IAOZrC,GAAcmC,EACdA,EAAKlG,GAEAkG,IAAOlG,GACZqF,EAAG3K,KAAKwL,GACRA,EAAKnC,IACLoC,EAAKF,QACMjG,IACToG,EAAKM,QACM1G,EAETkG,EADAC,EAAK,CAACA,EAAIC,IAOZrC,GAAcmC,EACdA,EAAKlG,GAGLqF,IAAOrF,GAnpBQuB,EAqpBJ6D,EACbD,EADAC,EAAiBC,EAppBJsB,QAAO,SAAUC,EAAMC,GAChC,MAAO,CAAEpT,KAAMoT,EAAI,GAAIC,KAAMF,EAAMG,MAAOF,EAAI,MAC7CtF,KAqpBLwC,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAGT,SAASuB,KACP,IAAIvB,EAAIC,EAAIC,EAAIa,EA/pBKc,EAASC,EAClB5E,EAgqBR1P,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAchB,GAXA2K,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EA9qBU,IA+qBVrB,OAEAqB,EAAKpF,EACwBkF,GAASpE,IAEpCsE,IAAOpF,IACToF,EAAK,MAEHA,IAAOpF,EAAY,CAGrB,GAFAqF,EAAK,IACLa,EAAKgB,QACMlH,EACT,KAAOkG,IAAOlG,GACZqF,EAAG3K,KAAKwL,GACRA,EAAKgB,UAGP7B,EAAKrF,EAEHqF,IAAOrF,GAhsBQgH,EAksBJ5B,EAjsBL/C,EAAkB,KADA4E,EAksBT5B,GAjsBFnR,OAAe+S,EAAG,GAAK,CAAExT,KAAM,WAAYmS,UAAWqB,GAChED,IAAS3E,EAAE2E,SAAU,GAisB1B7B,EADAC,EA/rBS/C,IAksBT0B,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAGT,SAAS+B,KACP,IAAI/B,EAEAxS,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,UAGhB2K,EAwCF,WACE,IAAIA,EAAIC,EAEJzS,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAIsB,KAAlCqF,EAAMZ,WAAW8E,KACnBqB,EA/wBU,IAgxBVrB,OAEAqB,EAAKpF,EACwBkF,GAASnE,IAEpCqE,IAAOpF,IAEToF,EArxB+B,CAAE3R,KAAM,WAAYoO,MAqxBtCuD,IAEfD,EAAKC,EAELI,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GApEFgC,MACMnH,IACTmF,EAqEJ,WACE,IAAIA,EAAIC,EAAIC,EAER1S,EAAuB,GAAdoR,GAAmB,EAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EA3yBU,IA4yBVrB,OAEAqB,EAAKpF,EACwBkF,GAASlE,IAEpCoE,IAAOpF,IACToF,EAAK,MAEHA,IAAOpF,IACTqF,EAAKS,QACM9F,EAGTmF,EADAC,EAtzB6B,CAAE3R,KAAM,aAAcoO,MAszBtCwD,IAOftB,GAAcoB,EACdA,EAAKnF,GAGPwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GA7GAiC,MACMpH,IACTmF,EA8GN,WACE,IAAIA,EAAIC,EAAQc,EAAQE,EAEpBzT,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EAn1BU,IAo1BVrB,OAEAqB,EAAKpF,EACwBkF,GAASjE,IAEpCmE,IAAOpF,GACJ0F,OACM1F,IACTkG,EAmON,WACE,IAAIf,EAAIC,EAAQc,EAAQE,EAEpBzT,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,IACLqB,EAAKiC,QACMrH,GACJ0F,OACM1F,IACTkG,EAjJN,WACE,IAAIf,EAAIC,EAAIC,EAER1S,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EA19BU,IA29BVrB,OAEAqB,EAAKpF,EACwBkF,GAASpE,IAEpCsE,IAAOpF,IACToF,EAAK,MAEHA,IAAOpF,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBsB,EAj9BQ,IAk9BRtB,OAEAsB,EAAKrF,EACwBkF,GAAS7D,IAEpCgE,IAAOrF,GAEToF,EAAK9D,EAAQ8D,GACbD,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAGP+D,GAAcoB,EACdA,EAAKnF,GAGPwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GAmGEmC,MACMtH,GACJ0F,OACM1F,IACToG,EA+bV,WACE,IAAIjB,EAAIC,EAAQc,EAAIC,EAAIC,EAEpBzT,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAWhB,GARA2K,EAAKpB,GAn/CO,UAo/CRlE,EAAM0H,OAAOxD,GAAa,IAC5BqB,EAr/CU,QAs/CVrB,IAAe,IAEfqB,EAAKpF,EACwBkF,GAASpC,IAEpCsC,IAAOpF,EAET,GADK0F,OACM1F,EAAY,CASrB,GARAkG,EAAK,GACDnD,EAAQgD,KAAKlG,EAAMmG,OAAOjC,MAC5BoC,EAAKtG,EAAMmG,OAAOjC,IAClBA,OAEAoC,EAAKnG,EACwBkF,GAASlC,IAEpCmD,IAAOnG,EACT,KAAOmG,IAAOnG,GACZkG,EAAGxL,KAAKyL,GACJpD,EAAQgD,KAAKlG,EAAMmG,OAAOjC,MAC5BoC,EAAKtG,EAAMmG,OAAOjC,IAClBA,OAEAoC,EAAKnG,EACwBkF,GAASlC,SAI1CkD,EAAKlG,EAEHkG,IAAOlG,IACTmG,EAAKT,QACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBqC,EAphDE,IAqhDFrC,OAEAqC,EAAKpG,EACwBkF,GAASjC,IAEpCmD,IAAOpG,GAEToF,EA1hDuB,CAAE3R,KAAM,OAAQoO,MA0hD1BqE,EA1hDmC1G,KAAK,KA2hDrD2F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAOT+D,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,OAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAjhBMqC,MACMxH,IACToG,EA0jBZ,WACE,IAAIjB,EAAIC,EAAIC,EAAIa,EAAIC,EAxlDIsB,EA0lDpB9U,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAWhB,GARA2K,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EAzmDU,IA0mDVrB,OAEAqB,EAAKpF,EACwBkF,GAAS9B,IAEpCgC,IAAOpF,EAAY,CASrB,GARAqF,EAAK,GACDhC,EAAQ0C,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAAS5B,IAEpC4C,IAAOlG,EACT,KAAOkG,IAAOlG,GACZqF,EAAG3K,KAAKwL,GACJ7C,EAAQ0C,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAAS5B,SAI1C+B,EAAKrF,EAEHqF,IAAOrF,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBmC,EAxoDM,IAyoDNnC,OAEAmC,EAAKlG,EACwBkF,GAAS9B,IAEpC8C,IAAOlG,IACTmG,EA5FR,WACE,IAAIhB,EAAIC,EAEJzS,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAWhB,GARA2K,EAAK,GACDjC,EAAQ6C,KAAKlG,EAAMmG,OAAOjC,MAC5BqB,EAAKvF,EAAMmG,OAAOjC,IAClBA,OAEAqB,EAAKpF,EACwBkF,GAAS/B,IAEpCiC,IAAOpF,EACT,KAAOoF,IAAOpF,GACZmF,EAAGzK,KAAK0K,GACJlC,EAAQ6C,KAAKlG,EAAMmG,OAAOjC,MAC5BqB,EAAKvF,EAAMmG,OAAOjC,IAClBA,OAEAqB,EAAKpF,EACwBkF,GAAS/B,SAI1CgC,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAuDIuC,MACM1H,IACTmG,EAAK,MAEHA,IAAOnG,GA/oDOyH,EAipDCtB,EAAjBf,EAjpD+B,CAC/B3R,KAAM,SAAUoO,MAAO,IAAI8F,OAgpDdtC,EAhpDuB7F,KAAK,IAAKiI,EAAOA,EAAKjI,KAAK,IAAM,KAipDrE2F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAGP+D,GAAcoB,EACdA,EAAKnF,KAGP+D,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAzoBQyC,IAEHxB,IAAOpG,GAEToF,EAAKzD,EAAQyD,EAAIc,EAAIE,GACrBjB,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAeb+D,GAAcoB,EACdA,EAAKnF,GAEHmF,IAAOnF,IACTmF,EAAKpB,IACLqB,EAAKiC,QACMrH,GACJ0F,OACM1F,IACTkG,EAjPR,WACE,IAAIf,EAAIC,EAAIC,EAER1S,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,GACD5C,EAAQ4E,KAAKlG,EAAMmG,OAAOjC,MAC5BqB,EAAKvF,EAAMmG,OAAOjC,IAClBA,OAEAqB,EAAKpF,EACwBkF,GAAS9D,IAEpCgE,IAAOpF,IACToF,EAAK,MAEHA,IAAOpF,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBsB,EAv5BQ,IAw5BRtB,OAEAsB,EAAKrF,EACwBkF,GAAS7D,IAEpCgE,IAAOrF,GAEToF,EAAK9D,EAAQ8D,GACbD,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAGP+D,GAAcoB,EACdA,EAAKnF,GAEHmF,IAAOnF,IACLwB,EAAQuE,KAAKlG,EAAMmG,OAAOjC,MAC5BoB,EAAKtF,EAAMmG,OAAOjC,IAClBA,OAEAoB,EAAKnF,EACwBkF,GAASzD,KAI1C+D,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GA0LI0C,MACM7H,GACJ0F,OACM1F,IACToG,EA+CZ,WACE,IAAIjB,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAEpBzT,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAWhB,GARA2K,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EAlqCU,IAmqCVrB,OAEAqB,EAAKpF,EACwBkF,GAASnD,IAEpCqD,IAAOpF,EAAY,CAuCrB,IAtCAqF,EAAK,GACDrD,EAAQ+D,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAASjD,IAEpCiE,IAAOlG,IACTkG,EAAKnC,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBoC,EAhrCM,KAirCNpC,OAEAoC,EAAKnG,EACwBkF,GAAShD,IAEpCiE,IAAOnG,GACLH,EAAM3L,OAAS6P,IACjBqC,EAAKvG,EAAMmG,OAAOjC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAAS/C,IAEpCiE,IAAOpG,GAETmG,EAAK/D,EAAQ+D,EAAIC,GACjBF,EAAKC,IAELpC,GAAcmC,EACdA,EAAKlG,KAGP+D,GAAcmC,EACdA,EAAKlG,IAGFkG,IAAOlG,GACZqF,EAAG3K,KAAKwL,GACJlE,EAAQ+D,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAASjD,IAEpCiE,IAAOlG,IACTkG,EAAKnC,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBoC,EAvtCI,KAwtCJpC,OAEAoC,EAAKnG,EACwBkF,GAAShD,IAEpCiE,IAAOnG,GACLH,EAAM3L,OAAS6P,IACjBqC,EAAKvG,EAAMmG,OAAOjC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAAS/C,IAEpCiE,IAAOpG,GAETmG,EAAK/D,EAAQ+D,EAAIC,GACjBF,EAAKC,IAELpC,GAAcmC,EACdA,EAAKlG,KAGP+D,GAAcmC,EACdA,EAAKlG,IAIPqF,IAAOrF,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBmC,EAzvCM,IA0vCNnC,OAEAmC,EAAKlG,EACwBkF,GAASnD,IAEpCmE,IAAOlG,GAEToF,EAAK9C,EAAQ+C,GACbF,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAGP+D,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,EAEP,GAAImF,IAAOnF,EAST,GARAmF,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EAvwCQ,IAwwCRrB,OAEAqB,EAAKpF,EACwBkF,GAASzC,IAEpC2C,IAAOpF,EAAY,CAuCrB,IAtCAqF,EAAK,GACD3C,EAAQqD,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAASvC,IAEpCuD,IAAOlG,IACTkG,EAAKnC,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBoC,EAhyCI,KAiyCJpC,OAEAoC,EAAKnG,EACwBkF,GAAShD,IAEpCiE,IAAOnG,GACLH,EAAM3L,OAAS6P,IACjBqC,EAAKvG,EAAMmG,OAAOjC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAAS/C,IAEpCiE,IAAOpG,GAETmG,EAAK/D,EAAQ+D,EAAIC,GACjBF,EAAKC,IAELpC,GAAcmC,EACdA,EAAKlG,KAGP+D,GAAcmC,EACdA,EAAKlG,IAGFkG,IAAOlG,GACZqF,EAAG3K,KAAKwL,GACJxD,EAAQqD,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAASvC,IAEpCuD,IAAOlG,IACTkG,EAAKnC,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBoC,EAv0CE,KAw0CFpC,OAEAoC,EAAKnG,EACwBkF,GAAShD,IAEpCiE,IAAOnG,GACLH,EAAM3L,OAAS6P,IACjBqC,EAAKvG,EAAMmG,OAAOjC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAAS/C,IAEpCiE,IAAOpG,GAETmG,EAAK/D,EAAQ+D,EAAIC,GACjBF,EAAKC,IAELpC,GAAcmC,EACdA,EAAKlG,KAGP+D,GAAcmC,EACdA,EAAKlG,IAIPqF,IAAOrF,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBmC,EA91CI,IA+1CJnC,OAEAmC,EAAKlG,EACwBkF,GAASzC,IAEpCyD,IAAOlG,GAEToF,EAAK9C,EAAQ+C,GACbF,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAGP+D,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,EAMT,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EA9RQ2C,MACM9H,IACToG,EA+Rd,WACE,IAAIjB,EAAIC,EAAIC,EAAIa,EAt3CK3E,EAAGc,EAER0F,EAs3CZpV,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAahB,IAVA2K,EAAKpB,GACLqB,EAAKrB,GACLsB,EAAK,GACDzC,EAAQmD,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAASrC,IAEjCqD,IAAOlG,GACZqF,EAAG3K,KAAKwL,GACJtD,EAAQmD,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAASrC,IAyB1C,GAtBIwC,IAAOrF,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBmC,EAj7CQ,IAk7CRnC,OAEAmC,EAAKlG,EACwBkF,GAASxD,IAEpCwE,IAAOlG,EAEToF,EADAC,EAAK,CAACA,EAAIa,IAGVnC,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,GAEHoF,IAAOpF,IACToF,EAAK,MAEHA,IAAOpF,EAAY,CASrB,GARAqF,EAAK,GACDzC,EAAQmD,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAASrC,IAEpCqD,IAAOlG,EACT,KAAOkG,IAAOlG,GACZqF,EAAG3K,KAAKwL,GACJtD,EAAQmD,KAAKlG,EAAMmG,OAAOjC,MAC5BmC,EAAKrG,EAAMmG,OAAOjC,IAClBA,OAEAmC,EAAKlG,EACwBkF,GAASrC,SAI1CwC,EAAKrF,EAEHqF,IAAOrF,GAl8CWqC,EAo8CHgD,EAl8CL0C,GAFKxG,EAo8CJ6D,GAl8CqB,GAAGoB,OAAOwB,MAAM,GAAIzG,GAAG/B,KAAK,IAAM,GAk8CpE4F,EAj8Ca,CAAE3R,KAAM,UAAWoO,MAAOoG,WAAWF,EAAkB1F,EAAE7C,KAAK,MAk8C3E2F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EA3XU+C,MACMlI,IACToG,EA4XhB,WACE,IAAIjB,EAAIC,EAEJzS,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,UAIhB4K,EAAKU,QACM9F,IAEToF,EA/9C+B,CAAE3R,KAAM,UAAWoO,MA+9CrCuD,IAEfD,EAAKC,EAELI,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GAlZYgD,IAGL/B,IAAOpG,GAEToF,EAAKzD,EAAQyD,EAAIc,EAAIE,GACrBjB,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAeb+D,GAAcoB,EACdA,EAAKnF,GAEHmF,IAAOnF,IACTmF,EAAKpB,IACLqB,EAAKiC,QACMrH,IAEToF,EA1oC8B,CAAE3R,KAAM,YAAaiK,KA0oCtC0H,IAEfD,EAAKC,IAITI,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GA1UEiD,MACMpI,GACJ0F,OACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBqC,EA/1BE,IAg2BFrC,OAEAqC,EAAKpG,EACwBkF,GAAShE,IAEpCkF,IAAOpG,EAGTmF,EADAC,EAAac,GAGbnC,GAAcoB,EACdA,EAAKnF,KAeb+D,GAAcoB,EACdA,EAAKnF,GAGPwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GA3KEkD,MACMrI,IACTmF,EAygCR,WACE,IAAIA,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAAIC,EAvqDPpS,EAyqDjBtB,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAWhB,GARA2K,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EApuDU,IAquDVrB,OAEAqB,EAAKpF,EACwBkF,GAASxD,IAEpC0D,IAAOpF,EAET,IADAqF,EAAKS,QACM9F,EAAY,CAuBrB,IAtBAkG,EAAK,GACLC,EAAKpC,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqC,EAhvDM,IAivDNrC,OAEAqC,EAAKpG,EACwBkF,GAASxD,IAEpC0E,IAAOpG,IACTqG,EAAKP,QACM9F,EAETmG,EADAC,EAAK,CAACA,EAAIC,IAOZtC,GAAcoC,EACdA,EAAKnG,GAEAmG,IAAOnG,GACZkG,EAAGxL,KAAKyL,GACRA,EAAKpC,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqC,EAvwDI,IAwwDJrC,OAEAqC,EAAKpG,EACwBkF,GAASxD,IAEpC0E,IAAOpG,IACTqG,EAAKP,QACM9F,EAETmG,EADAC,EAAK,CAACA,EAAIC,IAOZtC,GAAcoC,EACdA,EAAKnG,GAGLkG,IAAOlG,GA3uDM/L,EA6uDFoR,EAAbD,EA5uDK,CAAE3R,KAAM,QAASiK,KA4uDLwI,EA5uDcS,QAAO,SAASC,EAAMlC,GAAI,OAAOkC,EAAOlC,EAAE,GAAKA,EAAE,KAAOzQ,IA6uDvFkR,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,OAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAjmCImD,MACMtI,IACTmF,EAkmCV,WACE,IAAIA,EAAIC,EAAQc,EAAQE,EAEpBzT,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,GA1wDO,UA2wDRlE,EAAM0H,OAAOxD,GAAa,IAC5BqB,EA5wDU,QA6wDVrB,IAAe,IAEfqB,EAAKpF,EACwBkF,GAAS3B,IAEpC6B,IAAOpF,GACJ0F,OACM1F,IACTkG,EAAKP,QACM3F,GACJ0F,OACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBqC,EAzyDE,IA0yDFrC,OAEAqC,EAAKpG,EACwBkF,GAASjC,IAEpCmD,IAAOpG,EAGTmF,EADAC,EAhyDwB,CAAE3R,KAAM,MAAOmS,UAgyD1BM,IAGbnC,GAAcoB,EACdA,EAAKnF,KAeb+D,GAAcoB,EACdA,EAAKnF,GAGPwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GA/pCMoD,MACMvI,IACTmF,EAgqCZ,WACE,IAAIA,EAAIC,EAAQc,EAAQE,EAEpBzT,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,GAv0DO,cAw0DRlE,EAAM0H,OAAOxD,GAAa,IAC5BqB,EAz0DU,YA00DVrB,IAAe,IAEfqB,EAAKpF,EACwBkF,GAAS1B,IAEpC4B,IAAOpF,GACJ0F,OACM1F,IACTkG,EAAKP,QACM3F,GACJ0F,OACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBqC,EAz2DE,IA02DFrC,OAEAqC,EAAKpG,EACwBkF,GAASjC,IAEpCmD,IAAOpG,EAGTmF,EADAC,EA71DwB,CAAE3R,KAAM,UAAWmS,UA61D9BM,IAGbnC,GAAcoB,EACdA,EAAKnF,KAeb+D,GAAcoB,EACdA,EAAKnF,GAGPwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GA7tCQqD,MACMxI,IACTmF,EA8tCd,WACE,IAAIA,EAAIC,EAAQc,EAAQE,EAEpBzT,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,GAp4DO,UAq4DRlE,EAAM0H,OAAOxD,GAAa,IAC5BqB,EAt4DU,QAu4DVrB,IAAe,IAEfqB,EAAKpF,EACwBkF,GAASzB,IAEpC2B,IAAOpF,GACJ0F,OACM1F,IACTkG,EAAKP,QACM3F,GACJ0F,OACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBqC,EAz6DE,IA06DFrC,OAEAqC,EAAKpG,EACwBkF,GAASjC,IAEpCmD,IAAOpG,EAGTmF,EADAC,EA15DwB,CAAE3R,KAAM,MAAOmS,UA05D1BM,IAGbnC,GAAcoB,EACdA,EAAKnF,KAeb+D,GAAcoB,EACdA,EAAKnF,GAGPwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GA3xCUsD,MACMzI,IACTmF,EA4xChB,WACE,IAAIA,EAAIC,EAEJzS,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SA97DJ,iBAk8DRqF,EAAM0H,OAAOxD,GAAa,KAC5BqB,EAn8DU,eAo8DVrB,IAAe,KAEfqB,EAAKpF,EACwBkF,GAASxB,KAEpC0B,IAAOpF,IAEToF,EAz8D8BsD,GAAI,IA28DpCvD,EAAKC,EAELI,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GAxzCYwD,MACM3I,IACTmF,EAyzClB,WACE,IAAIA,EAAIC,EAEJzS,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SA19DJ,gBA89DRqF,EAAM0H,OAAOxD,GAAa,KAC5BqB,EA/9DU,cAg+DVrB,IAAe,KAEfqB,EAAKpF,EACwBkF,GAASvB,KAEpCyB,IAAOpF,IAEToF,EAr+D8BwD,GAAQ,IAu+DxCzD,EAAKC,EAELI,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GAr1Cc0D,MACM7I,IACTmF,EAs1CpB,WACE,IAAIA,EAAIC,EAAQc,EAAIC,EAAIC,EAEpBzT,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAWhB,GARA2K,EAAKpB,GAz/DO,gBA0/DRlE,EAAM0H,OAAOxD,GAAa,KAC5BqB,EA3/DU,cA4/DVrB,IAAe,KAEfqB,EAAKpF,EACwBkF,GAAStB,KAEpCwB,IAAOpF,EAET,GADK0F,OACM1F,EAAY,CASrB,GARAkG,EAAK,GACDtD,EAAQmD,KAAKlG,EAAMmG,OAAOjC,MAC5BoC,EAAKtG,EAAMmG,OAAOjC,IAClBA,OAEAoC,EAAKnG,EACwBkF,GAASrC,IAEpCsD,IAAOnG,EACT,KAAOmG,IAAOnG,GACZkG,EAAGxL,KAAKyL,GACJvD,EAAQmD,KAAKlG,EAAMmG,OAAOjC,MAC5BoC,EAAKtG,EAAMmG,OAAOjC,IAClBA,OAEAoC,EAAKnG,EACwBkF,GAASrC,SAI1CqD,EAAKlG,EAEHkG,IAAOlG,IACTmG,EAAKT,QACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBqC,EA5jEE,IA6jEFrC,OAEAqC,EAAKpG,EACwBkF,GAASjC,IAEpCmD,IAAOpG,GAEToF,EApiEuBsD,GAAII,SAoiEd5C,EApiEyB1G,KAAK,IAAK,KAqiEhD2F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAOT+D,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,OAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAx6CgB4D,MACM/I,IACTmF,EAy6CtB,WACE,IAAIA,EAAIC,EAAQc,EAAIC,EAAIC,EAEpBzT,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAWhB,GARA2K,EAAKpB,GA3kEO,qBA4kERlE,EAAM0H,OAAOxD,GAAa,KAC5BqB,EA7kEU,mBA8kEVrB,IAAe,KAEfqB,EAAKpF,EACwBkF,GAASrB,KAEpCuB,IAAOpF,EAET,GADK0F,OACM1F,EAAY,CASrB,GARAkG,EAAK,GACDtD,EAAQmD,KAAKlG,EAAMmG,OAAOjC,MAC5BoC,EAAKtG,EAAMmG,OAAOjC,IAClBA,OAEAoC,EAAKnG,EACwBkF,GAASrC,IAEpCsD,IAAOnG,EACT,KAAOmG,IAAOnG,GACZkG,EAAGxL,KAAKyL,GACJvD,EAAQmD,KAAKlG,EAAMmG,OAAOjC,MAC5BoC,EAAKtG,EAAMmG,OAAOjC,IAClBA,OAEAoC,EAAKnG,EACwBkF,GAASrC,SAI1CqD,EAAKlG,EAEHkG,IAAOlG,IACTmG,EAAKT,QACM1F,GAC6B,KAAlCH,EAAMZ,WAAW8E,KACnBqC,EAjpEE,IAkpEFrC,OAEAqC,EAAKpG,EACwBkF,GAASjC,IAEpCmD,IAAOpG,GAEToF,EAtnEuBwD,GAAQE,SAsnElB5C,EAtnE6B1G,KAAK,IAAK,KAunEpD2F,EAAKC,IAELrB,GAAcoB,EACdA,EAAKnF,KAOT+D,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,OAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EA3/CkB6D,MACMhJ,IACTmF,EA4/CxB,WACE,IAAIA,EAAIC,EAAIC,EAER1S,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,OAAI4S,GACFxB,GAAcwB,EAAOE,QAEdF,EAAO/K,SAGhB2K,EAAKpB,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBqB,EA/pEW,IAgqEXrB,OAEAqB,EAAKpF,EACwBkF,GAASpB,KAEpCsB,IAAOpF,IACTqF,EAAKS,QACM9F,EAGTmF,EADAC,EAtqEO,CAAE3R,KAAM,QAASiK,KAsqEV2H,IAOhBtB,GAAcoB,EACdA,EAAKnF,GAGPwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GAjiDoB8D,IAa3BzD,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,GAwPT,SAASkC,KACP,IAAIlC,EAAIC,EAAIC,EAAIa,EAAIC,EAAIC,EAn+BH7E,EAAG0F,EAq+BpBtU,EAAuB,GAAdoR,GAAmB,GAC5BwB,EAASC,GAAiB7S,GAE9B,GAAI4S,EAGF,OAFAxB,GAAcwB,EAAOE,QAEdF,EAAO/K,OAKhB,GAFA2K,EAAKpB,IACLqB,EAAKU,QACM9F,EAAY,CAuBrB,IAtBAqF,EAAK,GACLa,EAAKnC,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBoC,EAt/BQ,IAu/BRpC,OAEAoC,EAAKnG,EACwBkF,GAASxD,IAEpCyE,IAAOnG,IACToG,EAAKN,QACM9F,EAETkG,EADAC,EAAK,CAACA,EAAIC,IAOZrC,GAAcmC,EACdA,EAAKlG,GAEAkG,IAAOlG,GACZqF,EAAG3K,KAAKwL,GACRA,EAAKnC,GACiC,KAAlClE,EAAMZ,WAAW8E,KACnBoC,EA7gCM,IA8gCNpC,OAEAoC,EAAKnG,EACwBkF,GAASxD,IAEpCyE,IAAOnG,IACToG,EAAKN,QACM9F,EAETkG,EADAC,EAAK,CAACA,EAAIC,IAOZrC,GAAcmC,EACdA,EAAKlG,GAGLqF,IAAOrF,GA/hCQuB,EAiiCJ6D,EAjiCO6B,EAiiCH5B,EACjBF,EADAC,EAhiCS,GAAGoB,OAAOwB,MAAM,CAACzG,GAAI0F,GAAIzH,KAAK,MAmiCvCuE,GAAcoB,EACdA,EAAKnF,QAGP+D,GAAcoB,EACdA,EAAKnF,EAKP,OAFAwF,GAAiB7S,GAAO,CAAE8S,QAAS1B,GAAavJ,OAAQ2K,GAEjDA,EAktCP,SAASuD,GAAIQ,GAAK,MAAO,CAAEzV,KAAM,YAAa0V,MAAO,CAAE1V,KAAM,UAAWoO,MAAOqH,IAC/E,SAASN,GAAQM,GAAK,MAAO,CAAEzV,KAAM,iBAAkB0V,MAAO,CAAE1V,KAAM,UAAWoO,MAAOqH,IAkB1F,IAFAnJ,EAAaK,OAEMJ,GAAc+D,KAAgBlE,EAAM3L,OACrD,OAAO6L,EAMP,MAJIA,IAAeC,GAAc+D,GAAclE,EAAM3L,QACnDgR,GAnpEK,CAAEzR,KAAM,QAyEiB8J,EA8kE9B6G,GA9kEwC5G,EA+kExC2G,GAAiBtE,EAAM3L,OAAS2L,EAAMmG,OAAO7B,IAAkB,KA/kEhB1G,EAglE/C0G,GAAiBtE,EAAM3L,OACnB0Q,GAAoBT,GAAgBA,GAAiB,GACrDS,GAAoBT,GAAgBA,IAjlEnC,IAAI9G,EACTA,EAAgBW,aAAaT,EAAUC,GACvCD,EACAC,EACAC,KAtZa2L,OCyBrB,SAASC,EAAQ3W,EAAKmJ,GAClB,IAAK,IAAI5H,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,GAAW,MAAPvB,EAAe,OAAOA,EAC1BA,EAAMA,EAAImJ,EAAK5H,IAEnB,OAAOvB,EAyCX,IAAM4W,EAAmC,mBAAZC,QAAyB,IAAIA,QAAU,KASpE,SAASC,EAAWC,GAChB,GAAgB,MAAZA,EACA,OAAO,WAAA,OAAM,GAGjB,GAAqB,MAAjBH,EAAuB,CACvB,IAAII,EAAUJ,EAAcK,IAAIF,GAChC,OAAe,MAAXC,IAGJA,EAAUE,EAAgBH,GAC1BH,EAAcO,IAAIJ,EAAUC,IAHjBA,EAOf,OAAOE,EAAgBH,GAQ3B,SAASG,EAAgBH,GACrB,OAAOA,EAAShW,MACZ,IAAK,WACD,OAAO,WAAA,OAAM,GAEjB,IAAK,aACD,IAAMoO,EAAQ4H,EAAS5H,MAAMiI,cAC7B,OAAO,SAAC3W,EAAM4W,EAAUjK,GACpB,IAAMkK,EAAelK,GAAWA,EAAQkK,aAAgB,OACxD,OAAOnI,IAAU1O,EAAK6W,GAAaF,eAI3C,IAAK,QACD,IAAM1W,EAAOqW,EAAS/L,KAAKuM,MAAM,KACjC,OAAO,SAAC9W,EAAM4W,GAEV,OA9EhB,SAASG,EAAO/W,EAAMgX,EAAU/W,EAAMgX,GAElC,IADA,IAAItV,EAAUqV,EACLlW,EAAImW,EAAenW,EAAIb,EAAKc,SAAUD,EAAG,CAC9C,GAAe,MAAXa,EACA,OAAO,EAEX,IAAMuV,EAAQvV,EAAQ1B,EAAKa,IAC3B,GAAIiG,MAAMC,QAAQkQ,GAAQ,CACtB,IAAK,IAAIC,EAAI,EAAGA,EAAID,EAAMnW,SAAUoW,EAChC,GAAIJ,EAAO/W,EAAMkX,EAAMC,GAAIlX,EAAMa,EAAI,GACjC,OAAO,EAGf,OAAO,EAEXa,EAAUuV,EAEd,OAAOlX,IAAS2B,EA6DGoV,CAAO/W,EADG4W,EAAS3W,EAAKc,OAAS,GACVd,EAAM,IAI5C,IAAK,UACD,IAAMmX,EAAWd,EAAS7D,UAAUa,IAAI+C,GACxC,OAAO,SAACrW,EAAM4W,EAAUjK,GACpB,IAAK,IAAI7L,EAAI,EAAGA,EAAIsW,EAASrW,SAAUD,EACnC,GAAIsW,EAAStW,GAAGd,EAAM4W,EAAUjK,GAAY,OAAO,EAEvD,OAAO,GAIf,IAAK,WACD,IAAMyK,EAAWd,EAAS7D,UAAUa,IAAI+C,GACxC,OAAO,SAACrW,EAAM4W,EAAUjK,GACpB,IAAK,IAAI7L,EAAI,EAAGA,EAAIsW,EAASrW,SAAUD,EACnC,IAAKsW,EAAStW,GAAGd,EAAM4W,EAAUjK,GAAY,OAAO,EAExD,OAAO,GAIf,IAAK,MACD,IAAMyK,EAAWd,EAAS7D,UAAUa,IAAI+C,GACxC,OAAO,SAACrW,EAAM4W,EAAUjK,GACpB,IAAK,IAAI7L,EAAI,EAAGA,EAAIsW,EAASrW,SAAUD,EACnC,GAAIsW,EAAStW,GAAGd,EAAM4W,EAAUjK,GAAY,OAAO,EAEvD,OAAO,GAIf,IAAK,MACD,IAAMyK,EAAWd,EAAS7D,UAAUa,IAAI+C,GACxC,OAAO,SAACrW,EAAM4W,EAAUjK,GACpB,IAAItF,GAAS,EAEP+G,EAAI,GAkBV,OAjBAiJ,EAAWrW,SAAShB,EAAM,CACtBmJ,eAAOnJ,EAAMH,GACK,MAAVA,GAAkBuO,EAAEkJ,QAAQzX,GAEhC,IAAK,IAAIiB,EAAI,EAAGA,EAAIsW,EAASrW,SAAUD,EACnC,GAAIsW,EAAStW,GAAGd,EAAMoO,EAAGzB,GAGrB,OAFAtF,GAAS,OACTvH,cAKZuJ,iBAAW+E,EAAEmJ,SACb7O,KAAMiE,GAAWA,EAAQ6K,YACzBhP,SAAUmE,GAAWA,EAAQnE,UAAY,cAGtCnB,GAIf,IAAK,QACD,IAAMsM,EAAO0C,EAAWC,EAAS3C,MAC3BC,EAAQyC,EAAWC,EAAS1C,OAClC,OAAO,SAAC5T,EAAM4W,EAAUjK,GACpB,SAAIiK,EAAS7V,OAAS,GAAK6S,EAAM5T,EAAM4W,EAAUjK,KACtCgH,EAAKiD,EAAS,GAAIA,EAASxK,MAAM,GAAIO,IAMxD,IAAK,aACD,IAAMgH,EAAO0C,EAAWC,EAAS3C,MAC3BC,EAAQyC,EAAWC,EAAS1C,OAClC,OAAO,SAAC5T,EAAM4W,EAAUjK,GACpB,GAAIiH,EAAM5T,EAAM4W,EAAUjK,GACtB,IAAK,IAAI7L,EAAI,EAAG2W,EAAIb,EAAS7V,OAAQD,EAAI2W,IAAK3W,EAC1C,GAAI6S,EAAKiD,EAAS9V,GAAI8V,EAASxK,MAAMtL,EAAI,GAAI6L,GACzC,OAAO,EAInB,OAAO,GAIf,IAAK,YACD,IAAM1M,EAAOqW,EAAS/L,KAAKuM,MAAM,KACjC,OAAQR,EAAS3H,UACb,UAAK,EACD,OAAO,SAAC3O,GAAI,OAA4B,MAAvBkW,EAAQlW,EAAMC,IACnC,IAAK,IACD,OAAQqW,EAAS5H,MAAMpO,MACnB,IAAK,SACD,OAAO,SAACN,GACJ,IAAMuR,EAAI2E,EAAQlW,EAAMC,GACxB,MAAoB,iBAANsR,GAAkB+E,EAAS5H,MAAMA,MAAMkE,KAAKrB,IAElE,IAAK,UACD,IAAMxG,YAAauL,EAAS5H,MAAMA,OAClC,OAAO,SAAC1O,GAAI,OAAK+K,cAAemL,EAAQlW,EAAMC,KAElD,IAAK,OACD,OAAO,SAACD,GAAI,OAAKsW,EAAS5H,MAAMA,UAAiBwH,EAAQlW,EAAMC,KAEvE,MAAM,IAAImJ,6CAAsCkN,EAAS5H,MAAMpO,OACnE,IAAK,KACD,OAAQgW,EAAS5H,MAAMpO,MACnB,IAAK,SACD,OAAO,SAACN,GAAI,OAAMsW,EAAS5H,MAAMA,MAAMkE,KAAKsD,EAAQlW,EAAMC,KAC9D,IAAK,UACD,IAAM8K,YAAauL,EAAS5H,MAAMA,OAClC,OAAO,SAAC1O,GAAI,OAAK+K,cAAemL,EAAQlW,EAAMC,KAElD,IAAK,OACD,OAAO,SAACD,GAAI,OAAKsW,EAAS5H,MAAMA,UAAiBwH,EAAQlW,EAAMC,KAEvE,MAAM,IAAImJ,6CAAsCkN,EAAS5H,MAAMpO,OACnE,IAAK,KACD,OAAO,SAACN,GAAI,OAAKkW,EAAQlW,EAAMC,IAASqW,EAAS5H,MAAMA,OAC3D,IAAK,IACD,OAAO,SAAC1O,GAAI,OAAKkW,EAAQlW,EAAMC,GAAQqW,EAAS5H,MAAMA,OAC1D,IAAK,IACD,OAAO,SAAC1O,GAAI,OAAKkW,EAAQlW,EAAMC,GAAQqW,EAAS5H,MAAMA,OAC1D,IAAK,KACD,OAAO,SAAC1O,GAAI,OAAKkW,EAAQlW,EAAMC,IAASqW,EAAS5H,MAAMA,OAE/D,MAAM,IAAItF,kCAA2BkN,EAAS3H,WAGlD,IAAK,UACD,IAAMgF,EAAO0C,EAAWC,EAAS3C,MAC3BC,EAAQyC,EAAWC,EAAS1C,OAClC,OAAO,SAAC5T,EAAM4W,EAAUjK,GAAO,OAC3BiH,EAAM5T,EAAM4W,EAAUjK,IAClB+K,EAAQ1X,EAAM2T,EAAMiD,EAjQtB,YAiQ2CjK,IACzC2J,EAAS3C,KAAKE,SACdF,EAAK3T,EAAM4W,EAAUjK,IACrB+K,EAAQ1X,EAAM4T,EAAOgD,EAnQtB,aAmQ4CjK,IAGvD,IAAK,WACD,IAAMgH,EAAO0C,EAAWC,EAAS3C,MAC3BC,EAAQyC,EAAWC,EAAS1C,OAClC,OAAO,SAAC5T,EAAM4W,EAAUjK,GAAO,OAC3BiH,EAAM5T,EAAM4W,EAAUjK,IAClBgL,EAAS3X,EAAM2T,EAAMiD,EA5QvB,YA4Q4CjK,IAC1C2J,EAAS1C,MAAMC,SACfF,EAAK3T,EAAM4W,EAAUjK,IACrBgL,EAAS3X,EAAM4T,EAAOgD,EA9QvB,aA8Q6CjK,IAGxD,IAAK,YACD,IAAM4I,EAAMe,EAASN,MAAMtH,MACrBkF,EAAQyC,EAAWC,EAAS1C,OAClC,OAAO,SAAC5T,EAAM4W,EAAUjK,GAAO,OAC3BiH,EAAM5T,EAAM4W,EAAUjK,IAClBiL,EAAS5X,EAAM4W,EAAUrB,EAAK5I,IAG1C,IAAK,iBACD,IAAM4I,GAAOe,EAASN,MAAMtH,MACtBkF,EAAQyC,EAAWC,EAAS1C,OAClC,OAAO,SAAC5T,EAAM4W,EAAUjK,GAAO,OAC3BiH,EAAM5T,EAAM4W,EAAUjK,IAClBiL,EAAS5X,EAAM4W,EAAUrB,EAAK5I,IAG1C,IAAK,QAED,OAAO,SAAC3M,EAAM4W,EAAUjK,GAEpB,GAAIA,GAAWA,EAAQkL,WACnB,OAAOlL,EAAQkL,WAAWvB,EAAS/L,KAAMvK,EAAM4W,GAGnD,GAAIjK,GAAWA,EAAQkK,YAAa,OAAO,EAI3C,OAFaP,EAAS/L,KAAKoM,eAGvB,IAAK,YACD,GAA2B,cAAxB3W,EAAKM,KAAK8L,OAAO,GAAoB,OAAO,EAEnD,IAAK,cACD,MAAgC,gBAAzBpM,EAAKM,KAAK8L,OAAO,IAC5B,IAAK,UACD,GAA2B,YAAxBpM,EAAKM,KAAK8L,OAAO,GAAkB,OAAO,EAEjD,IAAK,aACD,MAAgC,eAAzBpM,EAAKM,KAAK8L,OAAO,KACI,YAAxBpM,EAAKM,KAAK8L,OAAO,IAEC,eAAdpM,EAAKM,OACgB,IAApBsW,EAAS7V,QAAqC,iBAArB6V,EAAS,GAAGtW,OAE5B,iBAAdN,EAAKM,KACb,IAAK,WACD,MAAqB,wBAAdN,EAAKM,MACM,uBAAdN,EAAKM,MACS,4BAAdN,EAAKM,KAEjB,MAAM,IAAI8I,oCAA6BkN,EAAS/L,QAK5D,MAAM,IAAInB,uCAAgCkN,EAAShW,OAkDvD,SAASwX,EAAe9X,EAAM2M,GAC1B,IAAMkK,EAAelK,GAAWA,EAAQkK,aAAgB,OAElDrW,EAAWR,EAAK6W,GACtB,OAAIlK,GAAWA,EAAQ6K,aAAe7K,EAAQ6K,YAAYhX,GAC/CmM,EAAQ6K,YAAYhX,GAE3B6W,EAAWnY,YAAYsB,GAChB6W,EAAWnY,YAAYsB,GAE9BmM,GAAuC,mBAArBA,EAAQnE,SACnBmE,EAAQnE,SAASxI,GAGrByI,OAAOC,KAAK1I,GAAM+X,QAAO,SAAUvY,GACtC,OAAOA,IAAQqX,KAWvB,SAASxW,EAAOL,EAAM2M,GAClB,IAAMkK,EAAelK,GAAWA,EAAQkK,aAAgB,OACxD,OAAgB,OAAT7W,GAAiC,WAAhBgY,EAAOhY,IAAkD,iBAAtBA,EAAK6W,GAapE,SAASa,EAAQ1X,EAAMuW,EAASK,EAAUqB,EAAMtL,GAC5C,IAAO9M,IAAU+W,QACjB,IAAK/W,EAAU,OAAO,EAEtB,IADA,IAAM6I,EAAOoP,EAAejY,EAAQ8M,GAC3B7L,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,IAAMoX,EAAWrY,EAAO6I,EAAK5H,IAC7B,GAAIiG,MAAMC,QAAQkR,GAAW,CACzB,IAAMC,EAAaD,EAASE,QAAQpY,GACpC,GAAImY,EAAa,EAAK,SACtB,IAAIE,SAAYzW,SA7aV,cA8aFqW,GACAI,EAAa,EACbzW,EAAauW,IAEbE,EAAaF,EAAa,EAC1BvW,EAAasW,EAASnX,QAE1B,IAAK,IAAIoW,EAAIkB,EAAYlB,EAAIvV,IAAcuV,EACvC,GAAI9W,EAAO6X,EAASf,GAAIxK,IAAY4J,EAAQ2B,EAASf,GAAIP,EAAUjK,GAC/D,OAAO,GAKvB,OAAO,EAaX,SAASgL,EAAS3X,EAAMuW,EAASK,EAAUqB,EAAMtL,GAC7C,IAAO9M,IAAU+W,QACjB,IAAK/W,EAAU,OAAO,EAEtB,IADA,IAAM6I,EAAOoP,EAAejY,EAAQ8M,GAC3B7L,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,IAAMoX,EAAWrY,EAAO6I,EAAK5H,IAC7B,GAAIiG,MAAMC,QAAQkR,GAAW,CACzB,IAAMI,EAAMJ,EAASE,QAAQpY,GAC7B,GAAIsY,EAAM,EAAK,SACf,GAldM,cAkdFL,GAAsBK,EAAM,GAAKjY,EAAO6X,EAASI,EAAM,GAAI3L,IAAY4J,EAAQ2B,EAASI,EAAM,GAAI1B,EAAUjK,GAC5G,OAAO,EAEX,GApdO,eAodHsL,GAAuBK,EAAMJ,EAASnX,OAAS,GAAKV,EAAO6X,EAASI,EAAM,GAAI3L,IAAa4J,EAAQ2B,EAASI,EAAM,GAAI1B,EAAUjK,GAChI,OAAO,GAInB,OAAO,EAaX,SAASiL,EAAS5X,EAAM4W,EAAUrB,EAAK5I,GACnC,GAAY,IAAR4I,EAAa,OAAO,EACxB,IAAO1V,IAAU+W,QACjB,IAAK/W,EAAU,OAAO,EAEtB,IADA,IAAM6I,EAAOoP,EAAejY,EAAQ8M,GAC3B7L,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,IAAMoX,EAAWrY,EAAO6I,EAAK5H,IAC7B,GAAIiG,MAAMC,QAAQkR,GAAU,CACxB,IAAMI,EAAM/C,EAAM,EAAI2C,EAASnX,OAASwU,EAAMA,EAAM,EACpD,GAAI+C,GAAO,GAAKA,EAAMJ,EAASnX,QAAUmX,EAASI,KAAStY,EACvD,OAAO,GAInB,OAAO,EAuCX,SAASgB,EAASuX,EAAKjC,EAAUpV,EAASyL,GACtC,GAAK2J,EAAL,CACA,IAAMM,EAAW,GACXL,EAAUF,EAAWC,GACrBkC,EAjCV,SAASC,EAASnC,EAAUU,GACxB,GAAgB,MAAZV,GAAuC,UAAnB0B,EAAO1B,GAAwB,MAAO,GAC9C,MAAZU,IAAoBA,EAAWV,GAGnC,IAFA,IAAMoC,EAAUpC,EAASzC,QAAU,CAACmD,GAAY,GAC1CtO,EAAOD,OAAOC,KAAK4N,GAChBxV,EAAI,EAAGA,EAAI4H,EAAK3H,SAAUD,EAAG,CAClC,IAAMyQ,EAAI7I,EAAK5H,GACT6X,EAAMrC,EAAS/E,GACrBmH,EAAQnR,WAARmR,IAAgBD,EAASE,EAAW,SAANpH,EAAeoH,EAAM3B,KAEvD,OAAO0B,EAuBaD,CAASnC,GAAUhD,IAAI+C,GAC3CgB,EAAWrW,SAASuX,EAAK,CACrBpP,eAAOnJ,EAAMH,GAET,GADc,MAAVA,GAAkB+W,EAASU,QAAQzX,GACnC0W,EAAQvW,EAAM4W,EAAUjK,GACxB,GAAI6L,EAAYzX,OACZ,IAAK,IAAID,EAAI,EAAG2W,EAAIe,EAAYzX,OAAQD,EAAI2W,IAAK3W,EAAG,CAC5C0X,EAAY1X,GAAGd,EAAM4W,EAAUjK,IAC/BzL,EAAQlB,EAAMH,EAAQ+W,GAE1B,IAAK,IAAIO,EAAI,EAAGyB,EAAIhC,EAAS7V,OAAQoW,EAAIyB,IAAKzB,EAAG,CAC7C,IAAM0B,EAAqBjC,EAASxK,MAAM+K,EAAI,GAC1CqB,EAAY1X,GAAG8V,EAASO,GAAI0B,EAAoBlM,IAChDzL,EAAQ0V,EAASO,GAAItX,EAAQgZ,SAKzC3X,EAAQlB,EAAMH,EAAQ+W,IAIlCvN,iBAAWuN,EAASW,SACpB7O,KAAMiE,GAAWA,EAAQ6K,YACzBhP,SAAUmE,GAAWA,EAAQnE,UAAY,eAajD,SAAS6G,EAAMkJ,EAAKjC,EAAU3J,GAC1B,IAAM+L,EAAU,GAIhB,OAHA1X,EAASuX,EAAKjC,GAAU,SAAUtW,GAC9B0Y,EAAQnR,KAAKvH,KACd2M,GACI+L,EAQX,SAASjM,EAAM6J,GACX,OAAOwC,EAAOrM,MAAM6J,GAUxB,SAASyC,EAAMR,EAAKjC,EAAU3J,GAC1B,OAAO0C,EAAMkJ,EAAK9L,EAAM6J,GAAW3J,UAGvCoM,EAAMtM,MAAQA,EACdsM,EAAM1J,MAAQA,EACd0J,EAAM/X,SAAWA,EACjB+X,EAAMC,QAvPN,SAAiBhZ,EAAMsW,EAAUM,EAAUjK,GACvC,OAAK2J,KACAtW,IACA4W,IAAYA,EAAW,IAErBP,EAAWC,EAAXD,CAAqBrW,EAAM4W,EAAUjK,KAmPhDoM,EAAMA,MAAQA"}