{"_args": [["vue-it-bigger@0.2.2", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue-it-bigger@0.2.2", "_id": "vue-it-bigger@0.2.2", "_inBundle": false, "_integrity": "sha512-u7SIvNOEsgEZEIFZlBpEB0nVJrB71dBUxU3bL+T63KF8gHIalwmC6LttS6Q0ALDs4WrSQCO84h1Q6wx33HC0jg==", "_location": "/vue-it-bigger", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-it-bigger@0.2.2", "name": "vue-it-bigger", "escapedName": "vue-it-bigger", "rawSpec": "0.2.2", "saveSpec": null, "fetchSpec": "0.2.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue-it-bigger/-/vue-it-bigger-0.2.2.tgz", "_spec": "0.2.2", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>", "email": "janos.rus<PERSON><EMAIL>"}, "bugs": {"url": "https://github.com/haiafara/vue-it-bigger/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"@babel/runtime": "^7.13.10", "babel-loader": "^8.2.2", "hammerjs": "^2.0.8", "terser-webpack-plugin": "^5.1.1"}, "description": "A simple image / (YouTube) video lightbox component for Vue.js. Based on vue-image-lightbox.", "devDependencies": {"@babel/core": "^7.13.10", "@babel/plugin-transform-runtime": "^7.13.10", "@babel/preset-env": "^7.13.12", "@vue/test-utils": "^1.0.0-beta.33", "babel-core": "^7.0.0-bridge.0", "copy-webpack-plugin": "8.1.0", "css-loader": "5.2.0", "eslint": "^7.22.0", "eslint-plugin-vue": "^7.8.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.3.1", "jest": "^26.6.3", "mini-css-extract-plugin": "^1.3.9", "optimize-css-assets-webpack-plugin": "5.0.4", "rimraf": "^3.0.2", "style-loader": "^2.0.0", "url-loader": "^4.1.1", "vue": "^2.6.12", "vue-jest": "^3.0.7", "vue-loader": "^15.9.6", "vue-template-compiler": "^2.6.12", "webpack": "5.28.0", "webpack-cli": "4.5.0", "webpack-dev-server": "3.11.2"}, "files": ["dist", "src"], "homepage": "https://github.com/haiafara/vue-it-bigger", "jest": {"moduleFileExtensions": ["js", "json", "vue"], "transform": {"^.+\\.js$": "babel-jest", "^.+\\.vue$": "vue-jest"}, "moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/components/$1"}, "collectCoverage": true, "collectCoverageFrom": ["**/*.{js,vue}", "!**/build/**", "!**/coverage/**", "!**/dist/**", "!**/gh-pages/**", "!**/node_modules/**", "!**/specs/**", "!**/src/*"], "coverageDirectory": "./coverage/"}, "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "lightbox", "gallery", "image gallery", "image lightbox", "video gallery", "video lightbox"], "license": "MIT", "main": "dist/vue-it-bigger.min.js", "name": "vue-it-bigger", "repository": {"type": "git", "url": "git+https://github.com/haiafara/vue-it-bigger.git"}, "scripts": {"build": "rimraf dist && webpack --progress --config build/webpack.prod.conf.js --mode production", "dev": "webpack-dev-server --inline --hot --open --config build/webpack.dev.conf.js --mode development", "gh-pages": "webpack --config build/webpack.gh-pages.conf.js --mode production", "lint": "eslint --ext .js,.vue src/", "test": "jest"}, "version": "0.2.2"}