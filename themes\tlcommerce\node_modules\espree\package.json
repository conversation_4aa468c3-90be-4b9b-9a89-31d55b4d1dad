{"_args": [["espree@9.5.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "espree@9.5.0", "_id": "espree@9.5.0", "_inBundle": false, "_integrity": "sha512-JPbJGhKc47++oo4JkEoTe2wjy4fmMwvFpgJT9cQzmfXKp22Dr6Hf1tdCteLz1h0P3t+mGvWZ+4Uankvh8+c6zw==", "_location": "/espree", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "espree@9.5.0", "name": "espree", "escapedName": "espree", "rawSpec": "9.5.0", "saveSpec": null, "fetchSpec": "9.5.0"}, "_requiredBy": ["/@eslint/eslintrc", "/eslint", "/vue-eslint-parser"], "_resolved": "https://registry.npmjs.org/espree/-/espree-9.5.0.tgz", "_spec": "9.5.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/eslint/espree/issues"}, "dependencies": {"acorn": "^8.8.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.3.0"}, "description": "An Esprima-compatible JavaScript parser built on Acorn", "devDependencies": {"@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.2.0", "c8": "^7.11.0", "chai": "^4.3.6", "eslint": "^8.13.0", "eslint-config-eslint": "^7.0.0", "eslint-plugin-jsdoc": "^39.2.4", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "mocha": "^9.2.2", "npm-run-all": "^4.1.5", "rollup": "^2.41.2", "shelljs": "^0.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./espree.js", "require": "./dist/espree.cjs", "default": "./dist/espree.cjs"}, "./dist/espree.cjs"], "./package.json": "./package.json"}, "files": ["lib", "dist/espree.cjs", "espree.js"], "funding": "https://opencollective.com/eslint", "homepage": "https://github.com/eslint/espree", "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax", "acorn"], "license": "BSD-2-<PERSON><PERSON>", "main": "dist/espree.cjs", "name": "espree", "repository": {"type": "git", "url": "git+https://github.com/eslint/espree.git"}, "scripts": {"build": "rollup -c rollup.config.js", "fixlint": "npm run lint -- --fix", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-release": "eslint-generate-release", "lint": "eslint .", "prepublishOnly": "npm run update-version && npm run build", "pretest": "npm run build", "publish-release": "eslint-publish-release", "sync-docs": "node sync-docs.js", "test": "npm-run-all -p unit lint", "unit": "npm-run-all -s unit:*", "unit:cjs": "mocha --color --reporter progress --timeout 30000 tests/lib/commonjs.cjs", "unit:esm": "c8 mocha --color --reporter progress --timeout 30000 'tests/lib/**/*.js'", "update-version": "node tools/update-version.js"}, "type": "module", "version": "9.5.0"}