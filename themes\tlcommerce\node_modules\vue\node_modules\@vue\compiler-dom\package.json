{"_args": [["@vue/compiler-dom@3.2.36", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "@vue/compiler-dom@3.2.36", "_id": "@vue/compiler-dom@3.2.36", "_inBundle": false, "_integrity": "sha512-tcOTAOiW4s24QLnq+ON6J+GRONXJ+A/mqKCORi0LSlIh8XQlNnlm24y8xIL8la+ZDgkdbjarQ9ZqYSvEja6gVA==", "_location": "/vue/@vue/compiler-dom", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-dom@3.2.36", "name": "@vue/compiler-dom", "escapedName": "@vue%2fcompiler-dom", "scope": "@vue", "rawSpec": "3.2.36", "saveSpec": null, "fetchSpec": "3.2.36"}, "_requiredBy": ["/vue", "/vue/@vue/compiler-ssr"], "_resolved": "https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.2.36.tgz", "_spec": "3.2.36", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerDOM", "compat": true, "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "dependencies": {"@vue/compiler-core": "3.2.36", "@vue/shared": "3.2.36"}, "description": "@vue/compiler-dom", "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-dom#readme", "jsdelivr": "dist/compiler-dom.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/compiler-dom.esm-bundler.js", "name": "@vue/compiler-dom", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-dom"}, "sideEffects": false, "types": "dist/compiler-dom.d.ts", "unpkg": "dist/compiler-dom.global.js", "version": "3.2.36"}