{"_args": [["vue-scrollto@2.20.0", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue-scrollto@2.20.0", "_id": "vue-scrollto@2.20.0", "_inBundle": false, "_integrity": "sha512-7i+AGKJTThZnMEkhIPgrZjyAX+fXV7/rGdg+CV283uZZwCxwiMXaBLTmIc5RTA4uwGnT+E6eJle3mXQfM2OD3A==", "_location": "/vue-scrollto", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-scrollto@2.20.0", "name": "vue-scrollto", "escapedName": "vue-scrollto", "rawSpec": "2.20.0", "saveSpec": null, "fetchSpec": "2.20.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue-scrollto/-/vue-scrollto-2.20.0.tgz", "_spec": "2.20.0", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://igor-rand<PERSON>.com"}, "bugs": {"url": "https://github.com/rigor789/vue-scrollto/issues"}, "dependencies": {"bezier-easing": "2.1.0"}, "description": "Adds a directive that listens for click events and scrolls to elements.", "devDependencies": {"@babel/core": "^7.9.0", "@babel/plugin-transform-object-assign": "^7.8.3", "@babel/preset-env": "^7.9.5", "@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "^9.0.0", "@vuepress/plugin-google-analytics": "^1.4.0", "husky": "^4.2.5", "lint-staged": "^10.1.2", "prettier": "^1.19.1", "rollup": "^2.4.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "semantic-release": "^17.0.4", "vuepress": "^1.4.0"}, "homepage": "https://github.com/rigor789/vue-scrollto#readme", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "directive", "scroll", "scrollto", "scroll to"], "license": "MIT", "lint-staged": {"src/**.js": ["prettier --write", "git add"]}, "main": "vue-scrollto.js", "name": "vue-scrollto", "prettier": {"semi": false, "singleQuote": true, "trailingComma": "es5", "tabWidth": 2}, "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "docs/changelog/README.md"}], ["@semantic-release/git", {"assets": ["docs/changelog/README.md"], "message": "chore(changelog): ${nextRelease.version} [ci skip]"}], "@semantic-release/npm", "@semantic-release/github"]}, "repository": {"type": "git", "url": "git+https://github.com/rigor789/vue-scrollto.git"}, "scripts": {"build": "rollup -c", "docs:build": "vuepress build docs", "docs:dev": "vuepress dev docs", "lint-staged": "lint-staged", "prepublishOnly": "npm run build", "semantic-release": "semantic-release"}, "types": "vue-scrollto.d.ts", "version": "2.20.0"}