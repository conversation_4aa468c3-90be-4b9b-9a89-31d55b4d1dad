{"_args": [["vue-select@4.0.0-beta.6", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue-select@4.0.0-beta.6", "_id": "vue-select@4.0.0-beta.6", "_inBundle": false, "_integrity": "sha512-K+zrNBSpwMPhAxYLTCl56gaMrWZGgayoWCLqe5rWwkB8aUbAUh7u6sXjIR7v4ckp2WKC7zEEUY27g6h1MRsIHw==", "_location": "/vue-select", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-select@4.0.0-beta.6", "name": "vue-select", "escapedName": "vue-select", "rawSpec": "4.0.0-beta.6", "saveSpec": null, "fetchSpec": "4.0.0-beta.6"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue-select/-/vue-select-4.0.0-beta.6.tgz", "_spec": "4.0.0-beta.6", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/sagalbot/vue-select/issues"}, "bundlewatch": {"files": [{"path": "./dist/vue-select.es.js", "compression": "gzip", "maxSize": "8 KB"}, {"path": "./dist/vue-select.umd.js", "compression": "gzip", "maxSize": "7 KB"}, {"path": "./dist/vue-select.css", "compression": "gzip", "maxSize": "2 KB"}]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "description": "Everything you wish the HTML <select> element could do, wrapped up into a lightweight, extensible Vue component.", "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^8.0.5", "@types/jsdom": "^16.2.14", "@types/node": "^18.0.5", "@vitejs/plugin-vue": "^3.0.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.0.2", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.7", "bundlewatch": "^0.3.3", "c8": "^7.11.3", "commitizen": "^4.2.5", "coveralls": "^3.1.1", "cross-env": "^7.0.3", "cz-conventional-changelog": "3.3.0", "eslint": "^8.20.0", "eslint-plugin-vue": "^9.2.0", "jsdom": "^20.0.0", "postcss-nested": "^5.0.6", "prettier": "^2.7.1", "semantic-release": "^19.0.3", "typescript": "^4.7.4", "vite": "^3.0.0", "vitest": "^0.18.1", "vue": "^3.2.37", "vue-tsc": "^0.38.8"}, "directories": {"doc": "docs", "test": "tests"}, "exports": {".": {"import": "./dist/vue-select.es.js", "require": "./dist/vue-select.umd.js"}, "./dist/vue-select.css": {"import": "./dist/vue-select.css", "require": "./dist/vue-select.css", "style": "./dist/vue-select.css"}}, "files": ["dist"], "homepage": "https://vue-select.org", "license": "MIT", "main": "./dist/vue-select.umd.js", "module": "./dist/vue-select.es.js", "name": "vue-select", "peerDependencies": {"vue": "3.x"}, "prepare": "npm run build", "private": false, "repository": {"type": "git", "url": "git+https://github.com/sagalbot/vue-select.git"}, "scripts": {"build": "vue-tsc --noEmit && vite build", "build:docs": "cd docs && yarn build", "commit": "git-cz", "coverage": "vitest --run --coverage --environment jsdom --silent", "dev": "vite", "dev:docs": "cd docs && yarn serve", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "preview": "vite preview --port 5050", "semantic-release": "semantic-release", "test": "vitest --environment jsdom", "typecheck": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false"}, "version": "4.0.0-beta.6"}