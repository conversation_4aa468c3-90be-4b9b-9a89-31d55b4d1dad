!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.VueSocialSharing=t():e.VueSocialSharing=t()}("undefined"!=typeof self?self:this,(function(){return function(e){var t={};function n(r){if(t[r])return t[r].exports;var o=t[r]={i:r,l:!1,exports:{}};return e[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)n.d(r,o,function(t){return e[t]}.bind(null,o));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=1)}([function(e,t,n){"use strict";(function(e){function r(e,t){const n=Object.create(null),r=e.split(",");for(let e=0;e<r.length;e++)n[r[e]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}n.d(t,"a",(function(){return m})),n.d(t,"b",(function(){return g})),n.d(t,"c",(function(){return y})),n.d(t,"d",(function(){return v})),n.d(t,"e",(function(){return V})),n.d(t,"f",(function(){return H})),n.d(t,"g",(function(){return Y})),n.d(t,"h",(function(){return j})),n.d(t,"i",(function(){return Q})),n.d(t,"j",(function(){return K})),n.d(t,"k",(function(){return C})),n.d(t,"l",(function(){return z})),n.d(t,"m",(function(){return X})),n.d(t,"n",(function(){return x})),n.d(t,"o",(function(){return T})),n.d(t,"p",(function(){return o})),n.d(t,"q",(function(){return f})),n.d(t,"r",(function(){return U})),n.d(t,"s",(function(){return A})),n.d(t,"t",(function(){return O})),n.d(t,"u",(function(){return F})),n.d(t,"v",(function(){return w})),n.d(t,"w",(function(){return N})),n.d(t,"x",(function(){return D})),n.d(t,"y",(function(){return d})),n.d(t,"z",(function(){return E})),n.d(t,"A",(function(){return s})),n.d(t,"B",(function(){return I})),n.d(t,"C",(function(){return P})),n.d(t,"D",(function(){return h})),n.d(t,"E",(function(){return b})),n.d(t,"F",(function(){return r})),n.d(t,"G",(function(){return p})),n.d(t,"H",(function(){return c})),n.d(t,"I",(function(){return k})),n.d(t,"J",(function(){return G})),n.d(t,"K",(function(){return J})),n.d(t,"L",(function(){return B}));const o=r("Infinity,undefined,NaN,isFinite,isNaN,parseFloat,parseInt,decodeURI,decodeURIComponent,encodeURI,encodeURIComponent,Math,Number,Date,Array,Object,Boolean,String,RegExp,Map,Set,JSON,Intl,BigInt");const i="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",s=r(i);function c(e){if(x(e)){const t={};for(let n=0;n<e.length;n++){const r=e[n],o=c(I(r)?u(r):r);if(o)for(const e in o)t[e]=o[e]}return t}if(F(e))return e}const l=/;(?![^(]*\))/g,a=/:(.+)/;function u(e){const t={};return e.split(l).forEach(e=>{if(e){const n=e.split(a);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function p(e){let t="";if(I(e))t=e;else if(x(e))for(let n=0;n<e.length;n++){const r=p(e[n]);r&&(t+=r+" ")}else if(F(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const f=r("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot"),d=r("svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistanceLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view");function h(e,t){if(e===t)return!0;let n=L(e),r=L(t);if(n||r)return!(!n||!r)&&e.getTime()===t.getTime();if(n=x(e),r=x(t),n||r)return!(!n||!r)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let r=0;n&&r<e.length;r++)n=h(e[r],t[r]);return n}(e,t);if(n=F(e),r=F(t),n||r){if(!n||!r)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const r=e.hasOwnProperty(n),o=t.hasOwnProperty(n);if(r&&!o||!r&&o||!h(e[n],t[n]))return!1}}return String(e)===String(t)}function b(e,t){return e.findIndex(e=>h(e,t))}const g={},m=[],v=()=>{},y=()=>!1,_=/^on[^a-z]/,w=e=>_.test(e),O=e=>e.startsWith("onUpdate:"),j=Object.assign,k=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},S=Object.prototype.hasOwnProperty,C=(e,t)=>S.call(e,t),x=Array.isArray,A=e=>"[object Map]"===M(e),E=e=>"[object Set]"===M(e),L=e=>e instanceof Date,T=e=>"function"==typeof e,I=e=>"string"==typeof e,P=e=>"symbol"==typeof e,F=e=>null!==e&&"object"==typeof e,N=e=>F(e)&&T(e.then)&&T(e.catch),R=Object.prototype.toString,M=e=>R.call(e),B=e=>M(e).slice(8,-1),U=e=>I(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,D=r(",key,ref,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),W=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},$=/-(\w)/g,V=W(e=>e.replace($,(e,t)=>t?t.toUpperCase():"")),q=/\B([A-Z])/g,z=W(e=>e.replace(q,"-$1").toLowerCase()),H=W(e=>e.charAt(0).toUpperCase()+e.slice(1)),G=W(e=>e?"on"+H(e):""),K=(e,t)=>e!==t&&(e==e||t==t),X=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},Y=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},J=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Z;const Q=()=>Z||(Z="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==e?e:{})}).call(this,n(2))},function(e,t,n){e.exports=n(3)},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(e){"object"==typeof window&&(n=window)}e.exports=n},function(e,t,n){"use strict";n.r(t),n.d(t,"ShareNetwork",(function(){return xn}));var r=n(0);const o=new WeakMap,i=[];let s;const c=Symbol(""),l=Symbol("");function a(e,t=r.b){(function(e){return e&&!0===e._isEffect})(e)&&(e=e.raw);const n=function(e,t){const n=function(){if(!n.active)return t.scheduler?void 0:e();if(!i.includes(n)){f(n);try{return h.push(d),d=!0,i.push(n),s=n,e()}finally{i.pop(),g(),s=i[i.length-1]}}};return n.id=p++,n.allowRecurse=!!t.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=e,n.deps=[],n.options=t,n}(e,t);return t.lazy||n(),n}function u(e){e.active&&(f(e),e.options.onStop&&e.options.onStop(),e.active=!1)}let p=0;function f(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let d=!0;const h=[];function b(){h.push(d),d=!1}function g(){const e=h.pop();d=void 0===e||e}function m(e,t,n){if(!d||void 0===s)return;let r=o.get(e);r||o.set(e,r=new Map);let i=r.get(n);i||r.set(n,i=new Set),i.has(s)||(i.add(s),s.deps.push(i))}function v(e,t,n,i,a,u){const p=o.get(e);if(!p)return;const f=new Set,d=e=>{e&&e.forEach(e=>{(e!==s||e.allowRecurse)&&f.add(e)})};if("clear"===t)p.forEach(d);else if("length"===n&&Object(r.n)(e))p.forEach((e,t)=>{("length"===t||t>=i)&&d(e)});else switch(void 0!==n&&d(p.get(n)),t){case"add":Object(r.n)(e)?Object(r.r)(n)&&d(p.get("length")):(d(p.get(c)),Object(r.s)(e)&&d(p.get(l)));break;case"delete":Object(r.n)(e)||(d(p.get(c)),Object(r.s)(e)&&d(p.get(l)));break;case"set":Object(r.s)(e)&&d(p.get(c))}f.forEach(e=>{e.options.scheduler?e.options.scheduler(e):e()})}const y=Object(r.F)("__proto__,__v_isRef,__isVue"),_=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(r.C)),w=C(),O=C(!1,!0),j=C(!0),k=C(!0,!0),S={};function C(e=!1,t=!1){return function(n,o,i){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_raw"===o&&i===(e?t?ne:te:t?ee:Q).get(n))return n;const s=Object(r.n)(n);if(!e&&s&&Object(r.k)(S,o))return Reflect.get(S,o,i);const c=Reflect.get(n,o,i);if(Object(r.C)(o)?_.has(o):y(o))return c;if(e||m(n,0,o),t)return c;if(ue(c)){return!s||!Object(r.r)(o)?c.value:c}return Object(r.u)(c)?e?oe(c):re(c):c}}["includes","indexOf","lastIndexOf"].forEach(e=>{const t=Array.prototype[e];S[e]=function(...e){const n=ae(this);for(let e=0,t=this.length;e<t;e++)m(n,0,e+"");const r=t.apply(n,e);return-1===r||!1===r?t.apply(n,e.map(ae)):r}}),["push","pop","shift","unshift","splice"].forEach(e=>{const t=Array.prototype[e];S[e]=function(...e){b();const n=t.apply(this,e);return g(),n}});const x=E(),A=E(!0);function E(e=!1){return function(t,n,o,i){let s=t[n];if(!e&&(o=ae(o),s=ae(s),!Object(r.n)(t)&&ue(s)&&!ue(o)))return s.value=o,!0;const c=Object(r.n)(t)&&Object(r.r)(n)?Number(n)<t.length:Object(r.k)(t,n),l=Reflect.set(t,n,o,i);return t===ae(i)&&(c?Object(r.j)(o,s)&&v(t,"set",n,o):v(t,"add",n,o)),l}}const L={get:w,set:x,deleteProperty:function(e,t){const n=Object(r.k)(e,t),o=(e[t],Reflect.deleteProperty(e,t));return o&&n&&v(e,"delete",t,void 0),o},has:function(e,t){const n=Reflect.has(e,t);return Object(r.C)(t)&&_.has(t)||m(e,0,t),n},ownKeys:function(e){return m(e,0,Object(r.n)(e)?"length":c),Reflect.ownKeys(e)}},T={get:j,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},I=(Object(r.h)({},L,{get:O,set:A}),Object(r.h)({},T,{get:k}),e=>Object(r.u)(e)?re(e):e),P=e=>Object(r.u)(e)?oe(e):e,F=e=>e,N=e=>Reflect.getPrototypeOf(e);function R(e,t,n=!1,r=!1){const o=ae(e=e.__v_raw),i=ae(t);t!==i&&!n&&m(o,0,t),!n&&m(o,0,i);const{has:s}=N(o),c=r?F:n?P:I;return s.call(o,t)?c(e.get(t)):s.call(o,i)?c(e.get(i)):void 0}function M(e,t=!1){const n=this.__v_raw,r=ae(n),o=ae(e);return e!==o&&!t&&m(r,0,e),!t&&m(r,0,o),e===o?n.has(e):n.has(e)||n.has(o)}function B(e,t=!1){return e=e.__v_raw,!t&&m(ae(e),0,c),Reflect.get(e,"size",e)}function U(e){e=ae(e);const t=ae(this);return N(t).has.call(t,e)||(t.add(e),v(t,"add",e,e)),this}function D(e,t){t=ae(t);const n=ae(this),{has:o,get:i}=N(n);let s=o.call(n,e);s||(e=ae(e),s=o.call(n,e));const c=i.call(n,e);return n.set(e,t),s?Object(r.j)(t,c)&&v(n,"set",e,t):v(n,"add",e,t),this}function W(e){const t=ae(this),{has:n,get:r}=N(t);let o=n.call(t,e);o||(e=ae(e),o=n.call(t,e));r&&r.call(t,e);const i=t.delete(e);return o&&v(t,"delete",e,void 0),i}function $(){const e=ae(this),t=0!==e.size,n=e.clear();return t&&v(e,"clear",void 0,void 0),n}function V(e,t){return function(n,r){const o=this,i=o.__v_raw,s=ae(i),l=t?F:e?P:I;return!e&&m(s,0,c),i.forEach((e,t)=>n.call(r,l(e),l(t),o))}}function q(e,t,n){return function(...o){const i=this.__v_raw,s=ae(i),a=Object(r.s)(s),u="entries"===e||e===Symbol.iterator&&a,p="keys"===e&&a,f=i[e](...o),d=n?F:t?P:I;return!t&&m(s,0,p?l:c),{next(){const{value:e,done:t}=f.next();return t?{value:e,done:t}:{value:u?[d(e[0]),d(e[1])]:d(e),done:t}},[Symbol.iterator](){return this}}}}function z(e){return function(...t){return"delete"!==e&&this}}const H={get(e){return R(this,e)},get size(){return B(this)},has:M,add:U,set:D,delete:W,clear:$,forEach:V(!1,!1)},G={get(e){return R(this,e,!1,!0)},get size(){return B(this)},has:M,add:U,set:D,delete:W,clear:$,forEach:V(!1,!0)},K={get(e){return R(this,e,!0)},get size(){return B(this,!0)},has(e){return M.call(this,e,!0)},add:z("add"),set:z("set"),delete:z("delete"),clear:z("clear"),forEach:V(!0,!1)},X={get(e){return R(this,e,!0,!0)},get size(){return B(this,!0)},has(e){return M.call(this,e,!0)},add:z("add"),set:z("set"),delete:z("delete"),clear:z("clear"),forEach:V(!0,!0)};function Y(e,t){const n=t?e?X:G:e?K:H;return(t,o,i)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(Object(r.k)(n,o)&&o in t?n:t,o,i)}["keys","values","entries",Symbol.iterator].forEach(e=>{H[e]=q(e,!1,!1),K[e]=q(e,!0,!1),G[e]=q(e,!1,!0),X[e]=q(e,!0,!0)});const J={get:Y(!1,!1)},Z=(Y(!1,!0),{get:Y(!0,!1)});Y(!0,!0);const Q=new WeakMap,ee=new WeakMap,te=new WeakMap,ne=new WeakMap;function re(e){return e&&e.__v_isReadonly?e:ie(e,!1,L,J,Q)}function oe(e){return ie(e,!0,T,Z,te)}function ie(e,t,n,o,i){if(!Object(r.u)(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const s=i.get(e);if(s)return s;const c=(l=e).__v_skip||!Object.isExtensible(l)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(Object(r.L)(l));var l;if(0===c)return e;const a=new Proxy(e,2===c?o:n);return i.set(e,a),a}function se(e){return ce(e)?se(e.__v_raw):!(!e||!e.__v_isReactive)}function ce(e){return!(!e||!e.__v_isReadonly)}function le(e){return se(e)||ce(e)}function ae(e){return e&&ae(e.__v_raw)||e}function ue(e){return Boolean(e&&!0===e.__v_isRef)}function pe(e,t,n,r){let o;try{o=r?e(...r):e()}catch(e){de(e,t,n)}return o}function fe(e,t,n,o){if(Object(r.o)(e)){const i=pe(e,t,n,o);return i&&Object(r.w)(i)&&i.catch(e=>{de(e,t,n)}),i}const i=[];for(let r=0;r<e.length;r++)i.push(fe(e[r],t,n,o));return i}function de(e,t,n,r=!0){t&&t.vnode;if(t){let r=t.parent;const o=t.proxy,i=n;for(;r;){const t=r.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,o,i))return;r=r.parent}const s=t.appContext.config.errorHandler;if(s)return void pe(s,null,10,[e,o,i])}!function(e,t,n,r=!0){console.error(e)}(e,0,0,r)}let he=!1,be=!1;const ge=[];let me=0;const ve=[];let ye=null,_e=0;const we=[];let Oe=null,je=0;const ke=Promise.resolve();let Se=null,Ce=null;function xe(e){const t=Se||ke;return e?t.then(this?e.bind(this):e):t}function Ae(e){if(!(ge.length&&ge.includes(e,he&&e.allowRecurse?me+1:me)||e===Ce)){const t=function(e){let t=me+1,n=ge.length;const r=Fe(e);for(;t<n;){const e=t+n>>>1;Fe(ge[e])<r?t=e+1:n=e}return t}(e);t>-1?ge.splice(t,0,e):ge.push(e),Ee()}}function Ee(){he||be||(be=!0,Se=ke.then(Ne))}function Le(e,t,n,o){Object(r.n)(e)?n.push(...e):t&&t.includes(e,e.allowRecurse?o+1:o)||n.push(e),Ee()}function Te(e){Le(e,Oe,we,je)}function Ie(e,t=null){if(ve.length){for(Ce=t,ye=[...new Set(ve)],ve.length=0,_e=0;_e<ye.length;_e++)ye[_e]();ye=null,_e=0,Ce=null,Ie(e,t)}}function Pe(e){if(we.length){const e=[...new Set(we)];if(we.length=0,Oe)return void Oe.push(...e);for(Oe=e,Oe.sort((e,t)=>Fe(e)-Fe(t)),je=0;je<Oe.length;je++)Oe[je]();Oe=null,je=0}}const Fe=e=>null==e.id?1/0:e.id;function Ne(e){be=!1,he=!0,Ie(e),ge.sort((e,t)=>Fe(e)-Fe(t));try{for(me=0;me<ge.length;me++){const e=ge[me];e&&pe(e,null,14)}}finally{me=0,ge.length=0,Pe(),he=!1,Se=null,(ge.length||we.length)&&Ne(e)}}new Set;new Map;let Re=0;const Me=e=>Re+=e;let Be=null,Ue=null;function De(e){let t;for(let n=0;n<e.length;n++){const r=e[n];if(!wt(r))return;if(r.type!==vt||"v-if"===r.children){if(t)return;t=r}}return t}function We(e){if(Object(r.o)(e)&&(e=e()),Object(r.n)(e)){0,e=De(e)}return Lt(e)}function $e(e,t){t&&t.pendingBranch?Object(r.n)(e)?t.effects.push(...e):t.effects.push(e):Te(e)}function Ve(e,t,n=Mt,r=!1){if(n){const o=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...r)=>{if(n.isUnmounted)return;b(),Ut(n);const o=fe(t,n,e,r);return Ut(null),g(),o});return r?o.unshift(i):o.push(i),i}}const qe=e=>(t,n=Mt)=>!Wt&&Ve(e,t,n),ze=(qe("bm"),qe("m")),He=(qe("bu"),qe("u")),Ge=qe("bum");qe("um"),qe("rtg"),qe("rtc");const Ke={};function Xe(e,t,n){return Ye(e,t,n)}function Ye(e,t,{immediate:n,deep:o,flush:i,onTrack:s,onTrigger:c}=r.b,l=Mt){let p,f,d=!1;if(ue(e)?(p=()=>e.value,d=!!e._shallow):se(e)?(p=()=>e,o=!0):p=Object(r.n)(e)?()=>e.map(e=>ue(e)?e.value:se(e)?Ze(e):Object(r.o)(e)?pe(e,l,2,[l&&l.proxy]):void 0):Object(r.o)(e)?t?()=>pe(e,l,2,[l&&l.proxy]):()=>{if(!l||!l.isUnmounted)return f&&f(),fe(e,l,3,[h])}:r.d,t&&o){const e=p;p=()=>Ze(e())}let h=e=>{f=v.options.onStop=()=>{pe(e,l,4)}},b=Object(r.n)(e)?[]:Ke;const g=()=>{if(v.active)if(t){const e=v();(o||d||Object(r.j)(e,b))&&(f&&f(),fe(t,l,3,[e,b===Ke?void 0:b,h]),b=e)}else v()};let m;g.allowRecurse=!!t,m="sync"===i?g:"post"===i?()=>dt(g,l&&l.suspense):()=>{!l||l.isMounted?function(e){Le(e,ye,ve,_e)}(g):g()};const v=a(p,{lazy:!0,onTrack:s,onTrigger:c,scheduler:m});return $t(v,l),t?n?g():b=v():"post"===i?dt(v,l&&l.suspense):v(),()=>{u(v),l&&Object(r.I)(l.effects,v)}}function Je(e,t,n){const o=this.proxy;return Ye(Object(r.B)(e)?()=>o[e]:e.bind(o),t.bind(o),n,this)}function Ze(e,t=new Set){if(!Object(r.u)(e)||t.has(e))return e;if(t.add(e),ue(e))Ze(e.value,t);else if(Object(r.n)(e))for(let n=0;n<e.length;n++)Ze(e[n],t);else if(Object(r.z)(e)||Object(r.s)(e))e.forEach(e=>{Ze(e,t)});else for(const n in e)Ze(e[n],t);return e}function Qe(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return ze(()=>{e.isMounted=!0}),Ge(()=>{e.isUnmounting=!0}),e}const et=[Function,Array],tt={name:"BaseTransition",props:{mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:et,onEnter:et,onAfterEnter:et,onEnterCancelled:et,onBeforeLeave:et,onLeave:et,onAfterLeave:et,onLeaveCancelled:et,onBeforeAppear:et,onAppear:et,onAfterAppear:et,onAppearCancelled:et},setup(e,{slots:t}){const n=Bt(),r=Qe();let o;return()=>{const i=t.default&&ct(t.default(),!0);if(!i||!i.length)return;const s=ae(e),{mode:c}=s;const l=i[0];if(r.isLeaving)return ot(l);const a=it(l);if(!a)return ot(l);const u=rt(a,s,r,n);st(a,u);const p=n.subTree,f=p&&it(p);let d=!1;const{getTransitionKey:h}=a.type;if(h){const e=h();void 0===o?o=e:e!==o&&(o=e,d=!0)}if(f&&f.type!==vt&&(!Ot(a,f)||d)){const e=rt(f,s,r,n);if(st(f,e),"out-in"===c)return r.isLeaving=!0,e.afterLeave=()=>{r.isLeaving=!1,n.update()},ot(l);"in-out"===c&&a.type!==vt&&(e.delayLeave=(e,t,n)=>{nt(r,f)[String(f.key)]=f,e._leaveCb=()=>{t(),e._leaveCb=void 0,delete u.delayedLeave},u.delayedLeave=n})}return l}}};function nt(e,t){const{leavingVNodes:n}=e;let r=n.get(t.type);return r||(r=Object.create(null),n.set(t.type,r)),r}function rt(e,t,n,r){const{appear:o,mode:i,persisted:s=!1,onBeforeEnter:c,onEnter:l,onAfterEnter:a,onEnterCancelled:u,onBeforeLeave:p,onLeave:f,onAfterLeave:d,onLeaveCancelled:h,onBeforeAppear:b,onAppear:g,onAfterAppear:m,onAppearCancelled:v}=t,y=String(e.key),_=nt(n,e),w=(e,t)=>{e&&fe(e,r,9,t)},O={mode:i,persisted:s,beforeEnter(t){let r=c;if(!n.isMounted){if(!o)return;r=b||c}t._leaveCb&&t._leaveCb(!0);const i=_[y];i&&Ot(e,i)&&i.el._leaveCb&&i.el._leaveCb(),w(r,[t])},enter(e){let t=l,r=a,i=u;if(!n.isMounted){if(!o)return;t=g||l,r=m||a,i=v||u}let s=!1;const c=e._enterCb=t=>{s||(s=!0,w(t?i:r,[e]),O.delayedLeave&&O.delayedLeave(),e._enterCb=void 0)};t?(t(e,c),t.length<=1&&c()):c()},leave(t,r){const o=String(e.key);if(t._enterCb&&t._enterCb(!0),n.isUnmounting)return r();w(p,[t]);let i=!1;const s=t._leaveCb=n=>{i||(i=!0,r(),w(n?h:d,[t]),t._leaveCb=void 0,_[o]===e&&delete _[o])};_[o]=e,f?(f(t,s),f.length<=1&&s()):s()},clone:e=>rt(e,t,n,r)};return O}function ot(e){if(lt(e))return(e=At(e)).children=null,e}function it(e){return lt(e)?e.children?e.children[0]:void 0:e}function st(e,t){6&e.shapeFlag&&e.component?st(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function ct(e,t=!1){let n=[],r=0;for(let o=0;o<e.length;o++){const i=e[o];i.type===gt?(128&i.patchFlag&&r++,n=n.concat(ct(i.children,t))):(t||i.type!==vt)&&n.push(i)}if(r>1)for(let e=0;e<n.length;e++)n[e].patchFlag=-2;return n}const lt=e=>e.type.__isKeepAlive;RegExp,RegExp;function at(e,t){return Object(r.n)(e)?e.some(e=>at(e,t)):Object(r.B)(e)?e.split(",").indexOf(t)>-1:!!e.test&&e.test(t)}function ut(e){let t=e.shapeFlag;256&t&&256,512&t&&512,e.shapeFlag=t}function pt(e){return 128&e.shapeFlag?e.ssContent:e}function ft(){return{app:null,config:{isNativeTag:r.c,performance:!1,globalProperties:{},optionMergeStrategies:{},isCustomElement:r.c,errorHandler:void 0,warnHandler:void 0},mixins:[],components:{},directives:{},provides:Object.create(null)}}const dt=$e;function ht(e,t,n,r=null){fe(e,t,7,[n,r])}const bt=Symbol();const gt=Symbol(void 0),mt=Symbol(void 0),vt=Symbol(void 0);Symbol(void 0);let yt=null;let _t=1;function wt(e){return!!e&&!0===e.__v_isVNode}function Ot(e,t){return e.type===t.type&&e.key===t.key}const jt="__vInternal",kt=({key:e})=>null!=e?e:null,St=({ref:e})=>null!=e?Object(r.B)(e)||ue(e)||Object(r.o)(e)?{i:Be,r:e}:e:null,Ct=xt;function xt(e,t=null,n=null,o=0,i=null,s=!1){if(e&&e!==bt||(e=vt),wt(e)){const r=At(e,t,!0);return n&&Tt(r,n),r}if(qt(e)&&(e=e.__vccOpts),t){(le(t)||jt in t)&&(t=Object(r.h)({},t));let{class:e,style:n}=t;e&&!Object(r.B)(e)&&(t.class=Object(r.G)(e)),Object(r.u)(n)&&(le(n)&&!Object(r.n)(n)&&(n=Object(r.h)({},n)),t.style=Object(r.H)(n))}const c=Object(r.B)(e)?1:(e=>e.__isSuspense)(e)?128:(e=>e.__isTeleport)(e)?64:Object(r.u)(e)?4:Object(r.o)(e)?2:0;const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&kt(t),ref:t&&St(t),scopeId:Ue,slotScopeIds:null,children:null,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:c,patchFlag:o,dynamicProps:i,dynamicChildren:null,appContext:null};if(Tt(l,n),128&c){const{content:e,fallback:t}=function(e){const{shapeFlag:t,children:n}=e;let r,o;return 32&t?(r=We(n.default),o=We(n.fallback)):(r=We(n),o=Lt(null)),{content:r,fallback:o}}(l);l.ssContent=e,l.ssFallback=t}return _t>0&&!s&&yt&&(o>0||6&c)&&32!==o&&yt.push(l),l}function At(e,t,n=!1){const{props:o,ref:i,patchFlag:s,children:c}=e,l=t?function(...e){const t=Object(r.h)({},e[0]);for(let n=1;n<e.length;n++){const o=e[n];for(const e in o)if("class"===e)t.class!==o.class&&(t.class=Object(r.G)([t.class,o.class]));else if("style"===e)t.style=Object(r.H)([t.style,o.style]);else if(Object(r.v)(e)){const n=t[e],r=o[e];n!==r&&(t[e]=n?[].concat(n,o[e]):r)}else""!==e&&(t[e]=o[e])}return t}(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&kt(l),ref:t&&t.ref?n&&i?Object(r.n)(i)?i.concat(St(t)):[i,St(t)]:St(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==gt?-1===s?16:16|s:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&At(e.ssContent),ssFallback:e.ssFallback&&At(e.ssFallback),el:e.el,anchor:e.anchor}}function Et(e=" ",t=0){return Ct(mt,null,e,t)}function Lt(e){return null==e||"boolean"==typeof e?Ct(vt):Object(r.n)(e)?Ct(gt,null,e):"object"==typeof e?null===e.el?e:At(e):Ct(mt,null,String(e))}function Tt(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(Object(r.n)(t))n=16;else if("object"==typeof t){if(1&o||64&o){const n=t.default;return void(n&&(n._c&&Me(1),Tt(e,n()),n._c&&Me(-1)))}{n=32;const r=t._;r||jt in t?3===r&&Be&&(1024&Be.vnode.patchFlag?(t._=2,e.patchFlag|=1024):t._=1):t._ctx=Be}}else Object(r.o)(t)?(t={default:t,_ctx:Be},n=32):(t=String(t),64&o?(n=16,t=[Et(t)]):n=8);e.children=t,e.shapeFlag|=n}let It=!0;function Pt(e,t,n){const o=n.appContext.config.optionMergeStrategies,{mixins:i,extends:s}=t;s&&Pt(e,s,n),i&&i.forEach(t=>Pt(e,t,n));for(const i in t)o&&Object(r.k)(o,i)?e[i]=o[i](e[i],t[i],n.proxy,i):e[i]=t[i]}const Ft=e=>e?Dt(e)?e.exposed?e.exposed:e.proxy:Ft(e.parent):null,Nt=Object(r.h)(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ft(e.parent),$root:e=>Ft(e.root),$emit:e=>e.emit,$options:e=>__VUE_OPTIONS_API__?function(e){const t=e.type,{__merged:n,mixins:r,extends:o}=t;if(n)return n;const i=e.appContext.mixins;if(!i.length&&!r&&!o)return t;const s={};return i.forEach(t=>Pt(s,t,e)),Pt(s,t,e),t.__merged=s}(e):e.type,$forceUpdate:e=>()=>Ae(e.update),$nextTick:e=>xe.bind(e.proxy),$watch:e=>__VUE_OPTIONS_API__?Je.bind(e):r.d}),Rt={get({_:e},t){const{ctx:n,setupState:o,data:i,props:s,accessCache:c,type:l,appContext:a}=e;if("__v_skip"===t)return!0;let u;if("$"!==t[0]){const l=c[t];if(void 0!==l)switch(l){case 0:return o[t];case 1:return i[t];case 3:return n[t];case 2:return s[t]}else{if(o!==r.b&&Object(r.k)(o,t))return c[t]=0,o[t];if(i!==r.b&&Object(r.k)(i,t))return c[t]=1,i[t];if((u=e.propsOptions[0])&&Object(r.k)(u,t))return c[t]=2,s[t];if(n!==r.b&&Object(r.k)(n,t))return c[t]=3,n[t];__VUE_OPTIONS_API__&&!It||(c[t]=4)}}const p=Nt[t];let f,d;return p?("$attrs"===t&&m(e,0,t),p(e)):(f=l.__cssModules)&&(f=f[t])?f:n!==r.b&&Object(r.k)(n,t)?(c[t]=3,n[t]):(d=a.config.globalProperties,Object(r.k)(d,t)?d[t]:void 0)},set({_:e},t,n){const{data:o,setupState:i,ctx:s}=e;if(i!==r.b&&Object(r.k)(i,t))i[t]=n;else if(o!==r.b&&Object(r.k)(o,t))o[t]=n;else if(Object(r.k)(e.props,t))return!1;return("$"!==t[0]||!(t.slice(1)in e))&&(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:i,propsOptions:s}},c){let l;return void 0!==n[c]||e!==r.b&&Object(r.k)(e,c)||t!==r.b&&Object(r.k)(t,c)||(l=s[0])&&Object(r.k)(l,c)||Object(r.k)(o,c)||Object(r.k)(Nt,c)||Object(r.k)(i.config.globalProperties,c)}};Object(r.h)({},Rt,{get(e,t){if(t!==Symbol.unscopables)return Rt.get(e,t,e)},has:(e,t)=>"_"!==t[0]&&!Object(r.p)(t)});ft();let Mt=null;const Bt=()=>Mt||Be,Ut=e=>{Mt=e};function Dt(e){return 4&e.vnode.shapeFlag}let Wt=!1;function $t(e,t=Mt){t&&(t.effects||(t.effects=[])).push(e)}function Vt(e){return Object(r.o)(e)&&e.displayName||e.name}function qt(e){return Object(r.o)(e)&&"__vccOpts"in e}function zt(e,t,n){const o=arguments.length;return 2===o?Object(r.u)(t)&&!Object(r.n)(t)?wt(t)?Ct(e,null,[t]):Ct(e,t):Ct(e,null,t):(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&wt(n)&&(n=[n]),Ct(e,t,n))}Symbol("");const Ht="http://www.w3.org/2000/svg",Gt="undefined"!=typeof document?document:null;let Kt,Xt;const Yt={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{const o=t?Gt.createElementNS(Ht,e):Gt.createElement(e,n?{is:n}:void 0);return"select"===e&&r&&null!=r.multiple&&o.setAttribute("multiple",r.multiple),o},createText:e=>Gt.createTextNode(e),createComment:e=>Gt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Gt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},cloneNode(e){const t=e.cloneNode(!0);return"_value"in e&&(t._value=e._value),t},insertStaticContent(e,t,n,r){const o=r?Xt||(Xt=Gt.createElementNS(Ht,"svg")):Kt||(Kt=Gt.createElement("div"));o.innerHTML=e;const i=o.firstChild;let s=i,c=s;for(;s;)c=s,Yt.insert(s,t,n),s=o.firstChild;return[i,c]}};const Jt=/\s*!important$/;function Zt(e,t,n){if(Object(r.n)(n))n.forEach(n=>Zt(e,t,n));else if(t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=en[t];if(n)return n;let o=Object(r.e)(t);if("filter"!==o&&o in e)return en[t]=o;o=Object(r.f)(o);for(let n=0;n<Qt.length;n++){const r=Qt[n]+o;if(r in e)return en[t]=r}return t}(e,t);Jt.test(n)?e.setProperty(Object(r.l)(o),n.replace(Jt,""),"important"):e[o]=n}}const Qt=["Webkit","Moz","ms"],en={};const tn="http://www.w3.org/1999/xlink";let nn=Date.now,rn=!1;if("undefined"!=typeof window){nn()>document.createEvent("Event").timeStamp&&(nn=()=>performance.now());const e=navigator.userAgent.match(/firefox\/(\d+)/i);rn=!!(e&&Number(e[1])<=53)}let on=0;const sn=Promise.resolve(),cn=()=>{on=0};function ln(e,t,n,r){e.addEventListener(t,n,r)}function an(e,t,n,o,i=null){const s=e._vei||(e._vei={}),c=s[t];if(o&&c)c.value=o;else{const[n,l]=function(e){let t;if(un.test(e)){let n;for(t={};n=e.match(un);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[Object(r.l)(e.slice(2)),t]}(t);if(o){ln(e,n,s[t]=function(e,t){const n=e=>{const o=e.timeStamp||nn();(rn||o>=n.attached-1)&&fe(function(e,t){if(Object(r.n)(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e(t))}return t}(e,n.value),t,5,[e])};return n.value=e,n.attached=(()=>on||(sn.then(cn),on=nn()))(),n}(o,i),l)}else c&&(!function(e,t,n,r){e.removeEventListener(t,n,r)}(e,n,c,l),s[t]=void 0)}}const un=/(?:Once|Passive|Capture)$/;const pn=/^on[a-z]/;const fn=(e,{slots:t})=>zt(tt,hn(e),t);fn.displayName="Transition";const dn={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};fn.props=Object(r.h)({},tt.props,dn);function hn(e){let{name:t="v",type:n,css:o=!0,duration:i,enterFromClass:s=t+"-enter-from",enterActiveClass:c=t+"-enter-active",enterToClass:l=t+"-enter-to",appearFromClass:a=s,appearActiveClass:u=c,appearToClass:p=l,leaveFromClass:f=t+"-leave-from",leaveActiveClass:d=t+"-leave-active",leaveToClass:h=t+"-leave-to"}=e;const b={};for(const t in e)t in dn||(b[t]=e[t]);if(!o)return b;const g=function(e){if(null==e)return null;if(Object(r.u)(e))return[bn(e.enter),bn(e.leave)];{const t=bn(e);return[t,t]}}(i),m=g&&g[0],v=g&&g[1],{onBeforeEnter:y,onEnter:_,onEnterCancelled:w,onLeave:O,onLeaveCancelled:j,onBeforeAppear:k=y,onAppear:S=_,onAppearCancelled:C=w}=b,x=(e,t,n)=>{mn(e,t?p:l),mn(e,t?u:c),n&&n()},A=(e,t)=>{mn(e,h),mn(e,d),t&&t()},E=e=>(t,r)=>{const o=e?S:_,i=()=>x(t,e,r);o&&o(t,i),vn(()=>{mn(t,e?a:s),gn(t,e?p:l),o&&o.length>1||_n(t,n,m,i)})};return Object(r.h)(b,{onBeforeEnter(e){y&&y(e),gn(e,s),gn(e,c)},onBeforeAppear(e){k&&k(e),gn(e,a),gn(e,u)},onEnter:E(!1),onAppear:E(!0),onLeave(e,t){const r=()=>A(e,t);gn(e,f),kn(),gn(e,d),vn(()=>{mn(e,f),gn(e,h),O&&O.length>1||_n(e,n,v,r)}),O&&O(e,r)},onEnterCancelled(e){x(e,!1),w&&w(e)},onAppearCancelled(e){x(e,!0),C&&C(e)},onLeaveCancelled(e){A(e),j&&j(e)}})}function bn(e){return Object(r.K)(e)}function gn(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.add(t)),(e._vtc||(e._vtc=new Set)).add(t)}function mn(e,t){t.split(/\s+/).forEach(t=>t&&e.classList.remove(t));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function vn(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let yn=0;function _n(e,t,n,r){const o=e._endId=++yn,i=()=>{o===e._endId&&r()};if(n)return setTimeout(i,n);const{type:s,timeout:c,propCount:l}=wn(e,t);if(!s)return r();const a=s+"end";let u=0;const p=()=>{e.removeEventListener(a,f),i()},f=t=>{t.target===e&&++u>=l&&p()};setTimeout(()=>{u<l&&p()},c+1),e.addEventListener(a,f)}function wn(e,t){const n=window.getComputedStyle(e),r=e=>(n[e]||"").split(", "),o=r("transitionDelay"),i=r("transitionDuration"),s=On(o,i),c=r("animationDelay"),l=r("animationDuration"),a=On(c,l);let u=null,p=0,f=0;"transition"===t?s>0&&(u="transition",p=s,f=i.length):"animation"===t?a>0&&(u="animation",p=a,f=l.length):(p=Math.max(s,a),u=p>0?s>a?"transition":"animation":null,f=u?"transition"===u?i.length:l.length:0);return{type:u,timeout:p,propCount:f,hasTransform:"transition"===u&&/\b(transform|all)(,|$)/.test(n.transitionProperty)}}function On(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((t,n)=>jn(t)+jn(e[n])))}function jn(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function kn(){return document.body.offsetHeight}new WeakMap,new WeakMap;Object(r.h)({patchProp:(e,t,n,o,i=!1,s,c,l,a)=>{switch(t){case"class":!function(e,t,n){if(null==t&&(t=""),n)e.setAttribute("class",t);else{const n=e._vtc;n&&(t=(t?[t,...n]:[...n]).join(" ")),e.className=t}}(e,o,i);break;case"style":!function(e,t,n){const o=e.style;if(n)if(Object(r.B)(n)){if(t!==n){const t=o.display;o.cssText=n,"_vod"in e&&(o.display=t)}}else{for(const e in n)Zt(o,e,n[e]);if(t&&!Object(r.B)(t))for(const e in t)null==n[e]&&Zt(o,e,"")}else e.removeAttribute("style")}(e,n,o);break;default:Object(r.v)(t)?Object(r.t)(t)||an(e,t,0,o,c):function(e,t,n,o){if(o)return"innerHTML"===t||!!(t in e&&pn.test(t)&&Object(r.o)(n));if("spellcheck"===t||"draggable"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(pn.test(t)&&Object(r.B)(n))return!1;return t in e}(e,t,o,i)?function(e,t,n,r,o,i,s){if("innerHTML"===t||"textContent"===t)return r&&s(r,o,i),void(e[t]=null==n?"":n);if("value"!==t||"PROGRESS"===e.tagName){if(""===n||null==n){const r=typeof e[t];if(""===n&&"boolean"===r)return void(e[t]=!0);if(null==n&&"string"===r)return e[t]="",void e.removeAttribute(t);if("number"===r)return e[t]=0,void e.removeAttribute(t)}try{e[t]=n}catch(e){0}}else{e._value=n;const t=null==n?"":n;e.value!==t&&(e.value=t)}}(e,t,o,s,c,l,a):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(tn,t.slice(6,t.length)):e.setAttributeNS(tn,t,n);else{const o=Object(r.A)(t);null==n||o&&!1===n?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,i))}},forcePatchProp:(e,t)=>"value"===t},Yt);var Sn={baidu:"http://cang.baidu.com/do/add?iu=@u&it=@t",buffer:"https://bufferapp.com/add?text=@t&url=@u",email:"mailto:?subject=@t&body=@u%0D%0A@d",evernote:"https://www.evernote.com/clip.action?url=@u&title=@t",facebook:"https://www.facebook.com/sharer/sharer.php?u=@u&title=@t&description=@d&quote=@q&hashtag=@h",flipboard:"https://share.flipboard.com/bookmarklet/popout?v=2&url=@u&title=@t",hackernews:"https://news.ycombinator.com/submitlink?u=@u&t=@t",instapaper:"http://www.instapaper.com/edit?url=@u&title=@t&description=@d",line:"http://line.me/R/msg/text/?@t%0D%0A@u%0D%0A@d",linkedin:"https://www.linkedin.com/shareArticle?url=@u",messenger:"fb-messenger://share/?link=@u",odnoklassniki:"https://connect.ok.ru/dk?st.cmd=WidgetSharePreview&st.shareUrl=@u&st.comments=@t",pinterest:"https://pinterest.com/pin/create/button/?url=@u&media=@m&description=@t",pocket:"https://getpocket.com/save?url=@u&title=@t",quora:"https://www.quora.com/share?url=@u&title=@t",reddit:"https://www.reddit.com/submit?url=@u&title=@t",skype:"https://web.skype.com/share?url=@t%0D%0A@u%0D%0A@d",sms:"sms:?body=@t%0D%0A@u%0D%0A@d",stumbleupon:"https://www.stumbleupon.com/submit?url=@u&title=@t",telegram:"https://t.me/share/url?url=@u&text=@t%0D%0A@d",tumblr:"https://www.tumblr.com/share/link?url=@u&name=@t&description=@d",twitter:"https://twitter.com/intent/tweet?text=@t&url=@u&hashtags=@h@tu",viber:"viber://forward?text=@t%0D%0A@u%0D%0A@d",vk:"https://vk.com/share.php?url=@u&title=@t&description=@d&image=@m&noparse=true",weibo:"http://service.weibo.com/share/share.php?url=@u&title=@t&pic=@m",whatsapp:"https://api.whatsapp.com/send?text=@t%0D%0A@u%0D%0A@d",wordpress:"https://wordpress.com/press-this.php?u=@u&t=@t&s=@d&i=@m",xing:"https://www.xing.com/social/share/spi?op=share&url=@u&title=@t",yammer:"https://www.yammer.com/messages/new?login=true&status=@t%0D%0A@u%0D%0A@d"},Cn="undefined"!=typeof window?window:null;var xn={name:"ShareNetwork",props:{network:{type:String,required:!0},url:{type:String,required:!0},title:{type:String,required:!0},description:{type:String,default:""},quote:{type:String,default:""},hashtags:{type:String,default:""},twitterUser:{type:String,default:""},media:{type:String,default:""},tag:{type:String,default:"a"},popup:{type:Object,default:function(){return{width:626,height:436}}},options:{type:Object,default:function(){return{networks:null}}}},data:function(){return{popupTop:0,popupLeft:0,popupWindow:void 0,popupInterval:null}},computed:{networks:function(){return Object.assign(Sn,this.options.networks||{})},key:function(){return this.network.toLowerCase()},rawLink:function(){var e=navigator.userAgent.toLowerCase();return"sms"===this.key&&(e.indexOf("iphone")>-1||e.indexOf("ipad")>-1)?this.networks[this.key].replace(":?",":&"):this.networks[this.key]},shareLink:function(){var e=this.rawLink;return"twitter"===this.key&&(this.hashtags.length||(e=e.replace("&hashtags=@h","")),this.twitterUser.length||(e=e.replace("@tu",""))),e.replace(/@tu/g,"&via="+encodeURIComponent(this.twitterUser)).replace(/@u/g,encodeURIComponent(this.url)).replace(/@t/g,encodeURIComponent(this.title)).replace(/@d/g,encodeURIComponent(this.description)).replace(/@q/g,encodeURIComponent(this.quote)).replace(/@h/g,this.encodedHashtags).replace(/@m/g,encodeURIComponent(this.media))},encodedHashtags:function(){return"facebook"===this.key&&this.hashtags.length?"%23"+this.hashtags.split(",")[0]:this.hashtags}},render:function(){var e=this;if(!this.networks.hasOwnProperty(this.key))throw new Error("Network "+this.key+" does not exist");var t={class:"share-network-"+this.key,onclick:function(){return e["http"===e.rawLink.substring(0,4)?"share":"touch"]()}};return"a"===this.tag&&(t.href="javascript:void(0)"),zt(this.tag,t,"function"==typeof this.$slots.default?this.$slots.default():null)},methods:{resizePopup:function(){var e=Cn.innerWidth||document.documentElement.clientWidth||Cn.screenX,t=Cn.innerHeight||document.documentElement.clientHeight||Cn.screenY,n=e/Cn.screen.availWidth;this.popupLeft=(e-this.popup.width)/2/n+(void 0!==Cn.screenLeft?Cn.screenLeft:Cn.screenX),this.popupTop=(t-this.popup.height)/2/n+(void 0!==Cn.screenTop?Cn.screenTop:Cn.screenY)},share:function(){var e=this;this.resizePopup(),this.popupWindow&&this.popupInterval&&(clearInterval(this.popupInterval),this.popupWindow.close(),this.emit("change")),this.popupWindow=Cn.open(this.shareLink,"sharer-"+this.key,",height="+this.popup.height+",width="+this.popup.width+",left="+this.popupLeft+",top="+this.popupTop+",screenX="+this.popupLeft+",screenY="+this.popupTop),this.popupWindow&&(this.popupWindow.focus(),this.popupInterval=setInterval((function(){e.popupWindow&&!e.popupWindow.closed||(clearInterval(e.popupInterval),e.popupWindow=null,e.emit("close"))}),500),this.emit("open"))},touch:function(){window.open(this.shareLink,"_blank"),this.emit("open")},emit:function(e){this.$root.$emit("share_network_"+e,this.key,this.url),this.$emit(e,this.key,this.url)}}};t.default={install:function(e,t){e.component(xn.name,xn)}}}])}));