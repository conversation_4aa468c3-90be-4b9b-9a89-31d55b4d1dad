{"_args": [["vue-cool-lightbox@2.7.5", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue-cool-lightbox@2.7.5", "_id": "vue-cool-lightbox@2.7.5", "_inBundle": false, "_integrity": "sha512-WQoCYvJQF7y9DP8s7Pt6bZhWYIO/rx8020h3kkHJBa1+RpbfiwcFlMOPonfzaWsUH2mSYKXP0FWpmxr2obm2+Q==", "_location": "/vue-cool-lightbox", "_phantomChildren": {"@babel/parser": "7.21.2", "postcss": "8.4.21", "source-map": "0.6.1"}, "_requested": {"type": "version", "registry": true, "raw": "vue-cool-lightbox@2.7.5", "name": "vue-cool-lightbox", "escapedName": "vue-cool-lightbox", "rawSpec": "2.7.5", "saveSpec": null, "fetchSpec": "2.7.5"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue-cool-lightbox/-/vue-cool-lightbox-2.7.5.tgz", "_spec": "2.7.5", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"./sfc": "src/components/vue-cool-lightbox.vue"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/lucaspulliese/vue-cool-lightbox/issues"}, "dependencies": {"body-scroll-lock": "^3.1.5", "plyr": "^3.6.7", "vue": "^2.6.10"}, "description": "A pretty Vue.js component to display an image gallery lightbox inspired by fancybox", "devDependencies": {"@vue/cli-plugin-babel": "^4.2.2", "@vue/cli-plugin-eslint": "^4.2.2", "@vue/cli-service": "^4.5.6", "babel-eslint": "^10.0.3", "core-js": "^3.3.2", "cross-env": "^5.2.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "minimist": "^1.2.0", "node-sass": "^4.14.1", "rollup": "^1.14.4", "rollup-plugin-buble": "^0.19.6", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-css-only": "^2.0.0", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-uglify-es": "0.0.1", "rollup-plugin-vue": "^4.7.2", "sass-loader": "^8.0.0", "serialize-javascript": ">=2.1.1", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": false, "env": {"node": false}, "extends": ["plugin:vue/essential"], "rules": {"no-console": "off"}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "files": ["dist/*", "src/*", "public/*", "*.json", "*.js"], "homepage": "https://vue-cool-lightbox.lucaspulliese.com", "keywords": ["vue", "v<PERSON><PERSON><PERSON>", "lightbox", "gallery", "image gallery", "image lightbox"], "license": "MIT", "main": "dist/vue-cool-lightbox.umd.js", "module": "dist/vue-cool-lightbox.esm.js", "name": "vue-cool-lightbox", "postcss": {"plugins": {"autoprefixer": {}}}, "private": false, "repository": {"type": "git", "url": "git+https://github.com/lucaspulliese/vue-cool-lightbox.git"}, "scripts": {"build": "npm run build:unpkg & npm run build:es & npm run build:umd", "build:demo": "vue-cli-service build --dest build", "build:es": "cross-env NODE_ENV=production rollup --config build-scripts/rollup.config.js --format es --file dist/vue-cool-lightbox.esm.js", "build:umd": "cross-env NODE_ENV=production rollup --config build-scripts/rollup.config.js --format umd --file dist/vue-cool-lightbox.umd.js", "build:unpkg": "cross-env NODE_ENV=production rollup --config build-scripts/rollup.config.js --format iife --file dist/vue-cool-lightbox.min.js", "lint": "vue-cli-service lint", "serve": "vue-cli-service serve"}, "unpkg": "dist/vue-cool-lightbox.min.js", "version": "2.7.5"}