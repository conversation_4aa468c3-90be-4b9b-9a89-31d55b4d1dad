{"_args": [["vue3-runtime-template@1.0.2", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue3-runtime-template@1.0.2", "_id": "vue3-runtime-template@1.0.2", "_inBundle": false, "_integrity": "sha512-oquh7JFsbL9MuPqCehOufzgONVvBCmcLWCvVgBA5VreLpNGcQSmb9Bz0g9xqQ+ufdwZGQs+T111GbRyJfPYFQA==", "_location": "/vue3-runtime-template", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue3-runtime-template@1.0.2", "name": "vue3-runtime-template", "escapedName": "vue3-runtime-template", "rawSpec": "1.0.2", "saveSpec": null, "fetchSpec": "1.0.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue3-runtime-template/-/vue3-runtime-template-1.0.2.tgz", "_spec": "1.0.2", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "dist/vue3-runtime-template.es.js", "bugs": {"url": "https://github.com/mattelen/vue3-runtime-template/issues"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/mattelen"}], "dependencies": {"vue": "^3.0.0"}, "description": "Create Vue 3 components by compiling templates on the fly", "devDependencies": {"acorn": "^8.0.4", "microbundle": "^0.11.0"}, "homepage": "https://github.com/mattelen/vue3-runtime-template#readme", "keywords": ["v<PERSON><PERSON><PERSON>", "vue3", "dynamic", "runtime", "template"], "license": "MIT", "main": "dist/vue3-runtime-template.umd.js", "module": "dist/vue3-runtime-template.es.js", "name": "vue3-runtime-template", "repository": {"type": "git", "url": "git+https://github.com/mattelen/vue3-runtime-template.git"}, "scripts": {"build": "microbundle index.js", "prepublishOnly": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "types": "vue3-runtime-template.d.ts", "unpkg": "dist/vue3-runtime-template.umd.js", "version": "1.0.2"}