{"_args": [["vue-template-compiler@2.7.14", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "vue-template-compiler@2.7.14", "_id": "vue-template-compiler@2.7.14", "_inBundle": false, "_integrity": "sha512-zyA5Y3ArvVG0NacJDkkzJuPQDF8RFeRlzV2vLeSnhSpieO6LK2OVbdLPi5MPPs09Ii+gMO8nY4S3iKQxBxDmWQ==", "_location": "/vue-template-compiler", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-template-compiler@2.7.14", "name": "vue-template-compiler", "escapedName": "vue-template-compiler", "rawSpec": "2.7.14", "saveSpec": null, "fetchSpec": "2.7.14"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/vue-template-compiler/-/vue-template-compiler-2.7.14.tgz", "_spec": "2.7.14", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "browser": "browser.js", "bugs": {"url": "https://github.com/vuejs/vue/issues"}, "dependencies": {"de-indent": "^1.0.2", "he": "^1.2.0"}, "description": "template compiler for Vue 2.0", "devDependencies": {"vue": "file:../.."}, "files": ["types/*.d.ts", "*.js"], "homepage": "https://github.com/vuejs/vue/tree/dev/packages/vue-template-compiler#readme", "jsdelivr": "browser.js", "keywords": ["vue", "compiler"], "license": "MIT", "main": "index.js", "name": "vue-template-compiler", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue.git"}, "types": "types/index.d.ts", "unpkg": "browser.js", "version": "2.7.14"}