<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <title>Vue It Bigger!</title>

    <style>
      body {
        padding-top: 20px;
        padding-bottom: 20px;
      }

      .header,
      .content,
      .footer {
        padding-right: 15px;
        padding-left: 15px;
      }

      .header {
        padding-bottom: 20px;
        border-bottom: 1px solid #e5e5e5;
      }

      .header h3 {
        margin-top: 0;
        margin-bottom: 0;
        line-height: 40px;
      }

      .content {
        margin-top: 20px;
      }

      #app {
        margin-bottom: 20px;
      }

      .footer {
        padding-top: 19px;
        color: #777;
        border-top: 1px solid #e5e5e5;
      }

      @media (min-width: 768px) {
        .container {
          max-width: 730px;
        }
      }

      @media screen and (min-width: 768px) {
        .header,
        .content,
        .footer {
          padding-right: 0;
          padding-left: 0;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="row header clearfix">
        <nav>
          <ul class="nav nav-pills pull-right">
            <li role="presentation" class="active"><a href="/vue-it-bigger">Demo</a></li>
            <li role="presentation"><a href="https://github.com/haiafara/vue-it-bigger">GitHub</a></li>
          </ul>
        </nav>
        <h3>Vue It Bigger!</h3>
      </div>
      <div class="row content">
        <p>Click on any thumbnail below to open the lightbox. The code for this example is in <a href="https://github.com/haiafara/vue-it-bigger/blob/master/src/App.vue">App.vue</a>.</p>
        <div id="app"></div>
      </div>
      <div class="row footer">
        All images by <a href="https://www.flickr.com/photos/janos">János Rusiczki</a>.
      </div>
    </div>
    <script src="build.js"></script>
  </body>
</html>
