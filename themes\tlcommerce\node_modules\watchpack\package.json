{"_args": [["watchpack@2.4.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "watchpack@2.4.0", "_id": "watchpack@2.4.0", "_inBundle": false, "_integrity": "sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==", "_location": "/watchpack", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "watchpack@2.4.0", "name": "watchpack", "escapedName": "watchpack", "rawSpec": "2.4.0", "saveSpec": null, "fetchSpec": "2.4.0"}, "_requiredBy": ["/webpack"], "_resolved": "https://registry.npmjs.org/watchpack/-/watchpack-2.4.0.tgz", "_spec": "2.4.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/watchpack/issues"}, "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "description": "Wrapper library for directory and file watching.", "devDependencies": {"coveralls": "^3.0.0", "eslint": "^5.11.1", "eslint-config-prettier": "^4.3.0", "eslint-plugin-prettier": "^3.1.0", "istanbul": "^0.4.3", "mocha": "^5.0.1", "prettier": "^1.11.0", "rimraf": "^2.6.2", "should": "^8.3.1", "write-file-atomic": "^3.0.1"}, "directories": {"test": "test"}, "engines": {"node": ">=10.13.0"}, "files": ["lib/"], "homepage": "https://github.com/webpack/watchpack", "license": "MIT", "main": "./lib/watchpack.js", "name": "watchpack", "repository": {"type": "git", "url": "git+https://github.com/webpack/watchpack.git"}, "scripts": {"cover": "istanbul cover node_modules/mocha/bin/_mocha", "lint": "eslint lib", "precover": "yarn lint", "pretest": "yarn lint", "pretty-files": "prettier \"lib/**.*\" \"test/**/*.js\" --write", "test": "mocha"}, "version": "2.4.0"}