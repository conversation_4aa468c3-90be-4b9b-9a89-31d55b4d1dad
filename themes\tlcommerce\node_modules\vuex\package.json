{"_args": [["vuex@4.1.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vuex@4.1.0", "_id": "vuex@4.1.0", "_inBundle": false, "_integrity": "sha512-hmV6UerDrPcgbSy9ORAtNXDr9M4wlNP4pEFKye4ujJF8oqgFFuxDCdOLS3eNoRTtq5O3hoBDh9Doj1bQMYHRbQ==", "_location": "/vuex", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vuex@4.1.0", "name": "vuex", "escapedName": "vuex", "rawSpec": "4.1.0", "saveSpec": null, "fetchSpec": "4.1.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vuex/-/vuex-4.1.0.tgz", "_spec": "4.1.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "browser": "dist/vuex.esm-browser.js", "bugs": {"url": "https://github.com/vuejs/vuex/issues"}, "dependencies": {"@vue/devtools-api": "^6.0.0-beta.11"}, "description": "state management for Vue.js", "devDependencies": {"@babel/core": "^7.14.3", "@babel/preset-env": "^7.14.2", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "^19.0.0", "@rollup/plugin-node-resolve": "^13.0.0", "@rollup/plugin-replace": "^2.4.2", "@types/node": "^15.6.0", "@vue/compiler-sfc": "^3.2.4", "babel-jest": "^26.6.3", "babel-loader": "^8.2.2", "brotli": "^1.3.2", "chalk": "^4.1.1", "conventional-changelog-cli": "^2.1.1", "cross-env": "^7.0.3", "css-loader": "^2.1.0", "enquirer": "^2.3.5", "eslint": "^7.27.0", "eslint-plugin-vue-libs": "^4.0.0", "execa": "^5.0.0", "express": "^4.17.1", "fs-extra": "^10.0.0", "jest": "^26.6.3", "puppeteer": "^9.1.1", "regenerator-runtime": "^0.13.5", "rollup": "^2.49.0", "rollup-plugin-terser": "^7.0.2", "semver": "^7.3.5", "start-server-and-test": "^1.12.3", "todomvc-app-css": "^2.4.1", "typescript": "^4.2.4", "vitepress": "^0.20.0", "vue": "^3.2.4", "vue-loader": "^16.5.0", "vue-style-loader": "^4.1.3", "webpack": "^4.43.0", "webpack-dev-middleware": "^3.7.2", "webpack-hot-middleware": "^2.25.0"}, "exports": {".": {"module": "./dist/vuex.esm-bundler.js", "require": "./dist/vuex.cjs.js", "import": "./dist/vuex.mjs"}, "./*": "./*", "./": "./"}, "files": ["dist", "types/index.d.ts", "types/helpers.d.ts", "types/logger.d.ts", "types/vue.d.ts"], "homepage": "https://github.com/vuejs/vuex#readme", "jsdelivr": "dist/vuex.global.js", "license": "MIT", "main": "dist/vuex.cjs.js", "module": "dist/vuex.esm-bundler.js", "name": "vuex", "peerDependencies": {"vue": "^3.2.0"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vuex.git"}, "scripts": {"build": "node scripts/build.js", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "coverage": "jest --testPathIgnorePatterns test/e2e --coverage", "dev": "node examples/server.js", "docs": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:serve": "yarn docs:build && vitepress serve docs", "lint": "eslint src test", "release": "node scripts/release.js", "test": "npm run lint && npm run build && npm run test:types && npm run test:unit && npm run test:ssr && npm run test:e2e && npm run test:esm", "test:e2e": "start-server-and-test dev http://localhost:8080 \"jest --testPathIgnorePatterns test/unit\"", "test:esm": "node test/esm/esm-test.js", "test:ssr": "cross-env VUE_ENV=server jest --testPathIgnorePatterns test/e2e", "test:types": "tsc -p types/test", "test:unit": "jest --testPathIgnorePatterns test/e2e"}, "sideEffects": false, "typings": "types/index.d.ts", "unpkg": "dist/vuex.global.js", "version": "4.1.0"}