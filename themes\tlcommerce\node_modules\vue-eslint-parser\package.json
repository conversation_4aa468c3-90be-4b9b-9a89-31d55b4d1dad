{"_args": [["vue-eslint-parser@8.3.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "vue-eslint-parser@8.3.0", "_id": "vue-eslint-parser@8.3.0", "_inBundle": false, "_integrity": "sha512-dzHGG3+sYwSf6zFBa0Gi9ZDshD7+ad14DGOdTLjruRVgZXe2J+DcZ9iUhyR48z5g1PqRa20yt3Njna/veLJL/g==", "_location": "/vue-eslint-parser", "_phantomChildren": {"esrecurse": "4.3.0"}, "_requested": {"type": "version", "registry": true, "raw": "vue-eslint-parser@8.3.0", "name": "vue-eslint-parser", "escapedName": "vue-eslint-parser", "rawSpec": "8.3.0", "saveSpec": null, "fetchSpec": "8.3.0"}, "_requiredBy": ["/eslint-plugin-vue"], "_resolved": "https://registry.npmjs.org/vue-eslint-parser/-/vue-eslint-parser-8.3.0.tgz", "_spec": "8.3.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/vuejs/vue-eslint-parser/issues"}, "dependencies": {"debug": "^4.3.2", "eslint-scope": "^7.0.0", "eslint-visitor-keys": "^3.1.0", "espree": "^9.0.0", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.5"}, "description": "The ESLint custom parser for `.vue` files.", "devDependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.16.3", "@babel/plugin-syntax-decorators": "^7.16.0", "@babel/plugin-syntax-pipeline-operator": "^7.16.0", "@babel/plugin-syntax-typescript": "^7.16.0", "@types/debug": "^4.1.7", "@types/eslint": "^7.29.0", "@types/estree": "^0.0.50", "@types/lodash": "^4.14.177", "@types/mocha": "^9.0.0", "@types/node": "^16.11.7", "@types/semver": "^7.3.9", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "chokidar": "^3.5.2", "codecov": "^3.8.3", "cross-spawn": "^7.0.3", "dts-bundle": "^0.7.3", "eslint": "^8.2.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-jsonc": "^2.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-node-dependencies": "^0.6.0", "eslint-plugin-prettier": "^4.0.0", "fs-extra": "^10.0.0", "jsonc-eslint-parser": "^2.0.3", "mocha": "^9.1.3", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "opener": "^1.5.2", "prettier": "^2.4.1", "rimraf": "^3.0.2", "rollup": "^2.60.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "ts-node": "^10.4.0", "typescript": "~4.4.4", "wait-on": "^6.0.0", "warun": "^1.0.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "files": ["index.*"], "funding": "https://github.com/sponsors/mysticatea", "homepage": "https://github.com/vuejs/vue-eslint-parser#readme", "keywords": [], "license": "MIT", "main": "index.js", "name": "vue-eslint-parser", "peerDependencies": {"eslint": ">=6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue-eslint-parser.git"}, "scripts": {"build": "tsc --module es2015 && rollup -c -o index.js && dts-bundle --name vue-eslint-parser --main .temp/index.d.ts --out ../index.d.ts", "clean": "rimraf .nyc_output .temp coverage index.*", "codecov": "codecov", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint src test package.json --ext .js,.ts", "postversion": "git push && git push --tags", "prebuild": "npm run -s clean", "pretest": "run-s build lint", "preupdate-fixtures": "npm run -s build", "preversion": "npm test", "prewatch": "npm run -s clean", "setup": "git submodule update --init && cd test/fixtures/eslint && npm install", "test": "npm run -s test:mocha", "test:debug": "mocha --require ts-node/register/transpile-only \"test/*.js\" --reporter dot --timeout 60000", "test:mocha": "nyc mocha \"test/*.js\" --reporter dot --timeout 60000", "update-fixtures": "node scripts/update-fixtures-ast.js && node scripts/update-fixtures-document-fragment.js", "version": "npm run -s build", "watch": "run-p watch:*", "watch:coverage-report": "wait-on coverage/lcov-report/index.html && opener coverage/lcov-report/index.html", "watch:rollup": "wait-on .temp/index.js && rollup -c -o index.js --watch", "watch:test": "wait-on index.js && warun index.js \"test/*.js\" \"test/fixtures/ast/*/*.json\" \"test/fixtures/*\" --debounce 1000 --no-initial -- nyc mocha \"test/*.js\" --reporter dot --timeout 10000", "watch:tsc": "tsc --module es2015 --watch", "watch:update-ast": "wait-on index.js && warun index.js \"test/fixtures/ast/*/*.vue\" -- node scripts/update-fixtures-ast.js"}, "version": "8.3.0"}