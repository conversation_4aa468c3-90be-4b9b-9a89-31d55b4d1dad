{"_args": [["eslint-plugin-vue@8.7.1", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "eslint-plugin-vue@8.7.1", "_id": "eslint-plugin-vue@8.7.1", "_inBundle": false, "_integrity": "sha512-28sbtm4l4cOzoO1LtzQPxfxhQABararUb1JtqusQqObJpWX2e/gmVyeYVfepizPFne0Q5cILkYGiBoV36L12Wg==", "_location": "/eslint-plugin-vue", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "eslint-plugin-vue@8.7.1", "name": "eslint-plugin-vue", "escapedName": "eslint-plugin-vue", "rawSpec": "8.7.1", "saveSpec": null, "fetchSpec": "8.7.1"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/eslint-plugin-vue/-/eslint-plugin-vue-8.7.1.tgz", "_spec": "8.7.1", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/mysticatea"}, "bugs": {"url": "https://github.com/vuejs/eslint-plugin-vue/issues"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/michalsnik"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ota-meshi"}], "dependencies": {"eslint-utils": "^3.0.0", "natural-compare": "^1.4.0", "nth-check": "^2.0.1", "postcss-selector-parser": "^6.0.9", "semver": "^7.3.5", "vue-eslint-parser": "^8.0.1"}, "description": "Official ESLint plugin for Vue.js", "devDependencies": {"@types/eslint": "^7.28.1", "@types/eslint-visitor-keys": "^1.0.0", "@types/natural-compare": "^1.4.0", "@types/node": "^13.13.5", "@types/semver": "^7.2.0", "@typescript-eslint/parser": "^5.5.0", "@vuepress/plugin-pwa": "^1.4.1", "acorn": "^8.5.0", "env-cmd": "^10.1.0", "eslint": "^8.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-eslint-plugin": "^3.5.3", "eslint-plugin-import": "^2.20.2", "eslint-plugin-jsonc": "^1.4.0", "eslint-plugin-node-dependencies": ">=0.5.0 <1.0.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "file:.", "espree": "^9.0.0", "lodash": "^4.17.21", "markdownlint-cli": "^0.31.1", "mocha": "^7.1.2", "nyc": "^15.1.0", "prettier": "^2.4.1", "typescript": "^4.5.0", "vue-eslint-editor": "^1.1.0", "vuepress": "^1.8.2"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "files": ["lib"], "homepage": "https://eslint.vuejs.org", "keywords": ["eslint", "eslint-plugin", "eslint-config", "vue", "v<PERSON><PERSON><PERSON>", "rules"], "license": "MIT", "main": "lib/index.js", "name": "eslint-plugin-vue", "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/eslint-plugin-vue.git"}, "scripts": {"cover": "npm run cover:test && npm run cover:report", "cover:report": "nyc report --reporter=html", "cover:test": "nyc npm run test:base -- --timeout 60000", "debug": "mocha --inspect \"tests/lib/**/*.js\" --reporter dot --timeout 60000", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "lint": "eslint . --rulesdir eslint-internal-rules && markdownlint \"**/*.md\"", "lint:fix": "eslint . --rulesdir eslint-internal-rules --fix && markdownlint \"**/*.md\" --fix", "new": "node tools/new-rule.js", "predocs:build": "npm run update", "preversion": "npm test && git add .", "start": "npm run test:base -- --watch --growl", "test": "nyc npm run test:base -- \"tests/integrations/*.js\" --timeout 60000", "test:base": "mocha \"tests/lib/**/*.js\" --reporter dot", "tsc": "tsc", "update": "node ./tools/update.js", "version": "env-cmd -e version npm run update && npm run lint -- --fix && git add ."}, "version": "8.7.1"}