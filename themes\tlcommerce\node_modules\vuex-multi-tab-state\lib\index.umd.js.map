{"version": 3, "file": "index.umd.js", "sources": ["../src/tab.ts", "../src/index.ts"], "sourcesContent": ["export default class Tab {\n  private tabId!: string;\n\n  private window!: Window;\n\n  constructor(window: Window) {\n    // Thanks to: https://gist.github.com/6174/6062387\n    this.tabId =\n      Math.random()\n        .toString(36)\n        .substring(2, 15) +\n      Math.random()\n        .toString(36)\n        .substring(2, 15);\n    this.window = window;\n  }\n\n  storageAvailable(): Bo<PERSON>an {\n    const test = 'vuex-multi-tab-state-test';\n    try {\n      this.window.localStorage.setItem(test, test);\n      this.window.localStorage.removeItem(test);\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  saveState(key: string, state: object) {\n    const toSave = JSON.stringify({\n      id: this.tabId,\n      state,\n    });\n\n    // Save the state in local storage\n    this.window.localStorage.setItem(key, toSave);\n  }\n\n  fetchState(key: string, cb: Function) {\n    const value = this.window.localStorage.getItem(key);\n\n    if (value) {\n      try {\n        const parsed = JSON.parse(value);\n        cb(parsed.state);\n      } catch (e) {\n        console.warn(`State saved in localStorage with key ${key} is invalid!`);\n      }\n    }\n  }\n\n  addEventListener(key: string, cb: Function) {\n    return this.window.addEventListener('storage', (event: StorageEvent) => {\n      if (!event.newValue || event.key !== key) {\n        return;\n      }\n\n      try {\n        const newState = JSON.parse(event.newValue);\n\n        // Check if the new state is from another tab\n        if (newState.id !== this.tabId) {\n          cb(newState.state);\n        }\n      } catch (e) {\n        console.warn(\n          `New state saved in localStorage with key ${key} is invalid`\n        );\n      }\n    });\n  }\n}\n", "import { pick, set, remove } from 'dot-object';\nimport Tab from './tab';\n\nexport interface Options {\n  statesPaths?: string[];\n  key?: string;\n  onBeforeReplace?(state: any): any;\n  onBeforeSave?(state: any): any;\n}\n\nexport default function(options?: Options) {\n  const tab = new Tab(window);\n  let key: string = 'vuex-multi-tab';\n  let statesPaths: string[] = [];\n  let onBeforeReplace = (state: any) => state;\n  let onBeforeSave = (state: any) => state;\n\n  if (options) {\n    key = options.key ? options.key : key;\n    statesPaths = options.statesPaths ? options.statesPaths : statesPaths;\n    onBeforeReplace = options.onBeforeReplace || onBeforeReplace;\n    onBeforeSave = options.onBeforeSave || onBeforeSave;\n  }\n\n  function filterStates(state: { [key: string]: any }): { [key: string]: any } {\n    const result = {};\n    statesPaths.forEach(statePath => {\n      set(statePath, pick(statePath, state), result);\n    });\n    return result;\n  }\n\n  /**\n   * simple object deep clone method\n   * @param obj\n   */\n  function cloneObj(obj: any): any {\n    if (Array.isArray(obj)) {\n      return obj.map(val => cloneObj(val));\n    }\n    if (typeof obj === 'object' && obj !== null) {\n      return Object.keys(obj).reduce((r: any, objKey) => {\n        r[objKey] = cloneObj(obj[objKey]);\n        return r;\n      }, {});\n    }\n    return obj;\n  }\n\n  function mergeState(oldState: any, newState: object) {\n    // if whole state is to be replaced then do just that\n    if (statesPaths.length === 0) return { ...newState };\n\n    // else clone old state\n    const merged: any = cloneObj(oldState);\n\n    // and replace only specified paths\n    statesPaths.forEach(statePath => {\n      const newValue = pick(statePath, newState);\n      // remove value if it doesn't exist, overwrite otherwise\n      if (typeof newValue === 'undefined') remove(statePath, merged);\n      else set(statePath, newValue, merged);\n    });\n    return merged;\n  }\n\n  if (!tab.storageAvailable()) {\n    throw new Error('Local storage is not available!');\n  }\n\n  function replaceState(store: any, state: object) {\n    const adjustedState = onBeforeReplace(state);\n\n    if (adjustedState) {\n      store.replaceState(mergeState(store.state, adjustedState));\n    }\n  }\n\n  return (store: any) => {\n    // First time, fetch state from local storage\n    tab.fetchState(key, (state: object) => {\n      replaceState(store, state);\n    });\n\n    // Add event listener to the state saved in local storage\n    tab.addEventListener(key, (state: object) => {\n      replaceState(store, state);\n    });\n\n    store.subscribe((mutation: MutationEvent, state: object) => {\n      let toSave = state;\n\n      // Filter state\n      if (statesPaths.length > 0) {\n        toSave = filterStates(state);\n      }\n\n      toSave = onBeforeSave(toSave);\n\n      // Save state in local storage\n      if (toSave) {\n        tab.saveState(key, toSave);\n      }\n    });\n  };\n}\n"], "names": ["Tab", "window", "this", "tabId", "Math", "random", "toString", "substring", "storageAvailable", "test", "localStorage", "setItem", "removeItem", "e", "saveState", "key", "state", "toSave", "JSON", "stringify", "id", "fetchState", "cb", "value", "getItem", "parse", "console", "warn", "addEventListener", "event", "newValue", "newState", "_this", "options", "tab", "statesPaths", "onBeforeReplace", "onBeforeSave", "Error", "replaceState", "store", "adjustedState", "oldState", "length", "merged", "cloneObj", "obj", "Array", "isArray", "map", "val", "Object", "keys", "reduce", "r", "obj<PERSON><PERSON>", "for<PERSON>ach", "statePath", "pick", "remove", "set", "mergeState", "subscribe", "mutation", "result", "filterStates"], "mappings": "6bAAqBA,aAKnB,WAAYC,GAEVC,KAAKC,MACHC,KAAKC,SACFC,SAAS,IACTC,UAAU,EAAG,IAChBH,KAAKC,SACFC,SAAS,IACTC,UAAU,EAAG,IAClBL,KAAKD,OAASA,6BAGhBO,iBAAA,WACE,IAAMC,EAAO,4BACb,IAGE,OAFAP,KAAKD,OAAOS,aAAaC,QAAQF,EAAMA,GACvCP,KAAKD,OAAOS,aAAaE,WAAWH,MAEpC,MAAOI,GACP,aAIJC,UAAA,SAAUC,EAAaC,GACrB,IAAMC,EAASC,KAAKC,UAAU,CAC5BC,GAAIlB,KAAKC,MACTa,MAAAA,IAIFd,KAAKD,OAAOS,aAAaC,QAAQI,EAAKE,MAGxCI,WAAA,SAAWN,EAAaO,GACtB,IAAMC,EAAQrB,KAAKD,OAAOS,aAAac,QAAQT,GAE/C,GAAIQ,EACF,IAEED,EADeJ,KAAKO,MAAMF,GAChBP,OACV,MAAOH,GACPa,QAAQC,6CAA6CZ,sBAK3Da,iBAAA,SAAiBb,EAAaO,cAC5B,YAAYrB,OAAO2B,iBAAiB,UAAW,SAACC,GAC9C,GAAKA,EAAMC,UAAYD,EAAMd,MAAQA,EAIrC,IACE,IAAMgB,EAAWb,KAAKO,MAAMI,EAAMC,UAG9BC,EAASX,KAAOY,EAAK7B,OACvBmB,EAAGS,EAASf,OAEd,MAAOH,GACPa,QAAQC,iDACsCZ,0CCxD9BkB,GACtB,IAAMC,EAAM,IAAIlC,EAAIC,QAChBc,EAAc,iBACdoB,EAAwB,GACxBC,EAAkB,SAACpB,UAAeA,GAClCqB,EAAe,SAACrB,UAAeA,GAmDnC,GAjDIiB,IACFlB,EAAMkB,EAAQlB,IAAMkB,EAAQlB,IAAMA,EAClCoB,EAAcF,EAAQE,YAAcF,EAAQE,YAAcA,EAC1DC,EAAkBH,EAAQG,iBAAmBA,EAC7CC,EAAeJ,EAAQI,cAAgBA,IA6CpCH,EAAI1B,mBACP,UAAU8B,MAAM,mCAGlB,SAASC,EAAaC,EAAYxB,GAChC,IAAMyB,EAAgBL,EAAgBpB,GAElCyB,GACFD,EAAMD,aAzBV,SAAoBG,EAAeX,GAEjC,GAA2B,IAAvBI,EAAYQ,OAAc,YAAYZ,GAG1C,IAAMa,EAlBR,SAASC,EAASC,GAChB,OAAIC,MAAMC,QAAQF,GACTA,EAAIG,IAAI,SAAAC,UAAOL,EAASK,KAEd,iBAARJ,GAA4B,OAARA,EACtBK,OAAOC,KAAKN,GAAKO,OAAO,SAACC,EAAQC,GAEtC,OADAD,EAAEC,GAAUV,EAASC,EAAIS,IAClBD,GACN,IAEER,EAQaD,CAASH,GAS7B,OANAP,EAAYqB,QAAQ,SAAAC,GAClB,IAAM3B,EAAW4B,OAAKD,EAAW1B,QAET,IAAbD,EAA0B6B,SAAOF,EAAWb,GAClDgB,MAAIH,EAAW3B,EAAUc,KAEzBA,EAWciB,CAAWrB,EAAMxB,MAAOyB,IAI/C,gBAAQD,GAENN,EAAIb,WAAWN,EAAK,SAACC,GACnBuB,EAAaC,EAAOxB,KAItBkB,EAAIN,iBAAiBb,EAAK,SAACC,GACzBuB,EAAaC,EAAOxB,KAGtBwB,EAAMsB,UAAU,SAACC,EAAyB/C,GACxC,IAAIC,EAASD,EAGTmB,EAAYQ,OAAS,IACvB1B,EAtEN,SAAsBD,GACpB,IAAMgD,EAAS,GAIf,OAHA7B,EAAYqB,QAAQ,SAAAC,GAClBG,MAAIH,EAAWC,OAAKD,EAAWzC,GAAQgD,KAElCA,EAiEMC,CAAajD,KAGxBC,EAASoB,EAAapB,KAIpBiB,EAAIpB,UAAUC,EAAKE"}