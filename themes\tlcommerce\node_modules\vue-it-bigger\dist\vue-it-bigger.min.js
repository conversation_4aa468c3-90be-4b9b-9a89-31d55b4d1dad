/*! For license information please see vue-it-bigger.min.js.LICENSE.txt */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.Lightbox=e():t.Lightbox=e()}(self,(function(){return(()=>{var t={840:(t,e,i)=>{var n;!function(s,o,r,a){"use strict";var c,h=["","webkit","Moz","MS","ms","o"],u=o.createElement("div"),l=Math.round,p=Math.abs,d=Date.now;function f(t,e,i){return setTimeout(x(t,i),e)}function v(t,e,i){return!!Array.isArray(t)&&(m(t,i[e],i),!0)}function m(t,e,i){var n;if(t)if(t.forEach)t.forEach(e,i);else if(t.length!==a)for(n=0;n<t.length;)e.call(i,t[n],n,t),n++;else for(n in t)t.hasOwnProperty(n)&&e.call(i,t[n],n,t)}function g(t,e,i){var n="DEPRECATED METHOD: "+e+"\n"+i+" AT \n";return function(){var e=new Error("get-stack-trace"),i=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",o=s.console&&(s.console.warn||s.console.log);return o&&o.call(s.console,n,i),t.apply(this,arguments)}}c="function"!=typeof Object.assign?function(t){if(t===a||null===t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),i=1;i<arguments.length;i++){var n=arguments[i];if(n!==a&&null!==n)for(var s in n)n.hasOwnProperty(s)&&(e[s]=n[s])}return e}:Object.assign;var y=g((function(t,e,i){for(var n=Object.keys(e),s=0;s<n.length;)(!i||i&&t[n[s]]===a)&&(t[n[s]]=e[n[s]]),s++;return t}),"extend","Use `assign`."),T=g((function(t,e){return y(t,e,!0)}),"merge","Use `assign`.");function b(t,e,i){var n,s=e.prototype;(n=t.prototype=Object.create(s)).constructor=t,n._super=s,i&&c(n,i)}function x(t,e){return function(){return t.apply(e,arguments)}}function _(t,e){return"function"==typeof t?t.apply(e&&e[0]||a,e):t}function w(t,e){return t===a?e:t}function E(t,e,i){m(L(e),(function(e){t.addEventListener(e,i,!1)}))}function I(t,e,i){m(L(e),(function(e){t.removeEventListener(e,i,!1)}))}function S(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function C(t,e){return t.indexOf(e)>-1}function L(t){return t.trim().split(/\s+/g)}function M(t,e,i){if(t.indexOf&&!i)return t.indexOf(e);for(var n=0;n<t.length;){if(i&&t[n][i]==e||!i&&t[n]===e)return n;n++}return-1}function A(t){return Array.prototype.slice.call(t,0)}function P(t,e,i){for(var n=[],s=[],o=0;o<t.length;){var r=e?t[o][e]:t[o];M(s,r)<0&&n.push(t[o]),s[o]=r,o++}return i&&(n=e?n.sort((function(t,i){return t[e]>i[e]})):n.sort()),n}function N(t,e){for(var i,n,s=e[0].toUpperCase()+e.slice(1),o=0;o<h.length;){if((n=(i=h[o])?i+s:e)in t)return n;o++}return a}var O=1;function $(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||s}var D="ontouchstart"in s,H=N(s,"PointerEvent")!==a,R=D&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),k="touch",z="mouse",B=24,X=["x","y"],Y=["clientX","clientY"];function F(t,e){var i=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){_(t.options.enable,[t])&&i.handler(e)},this.init()}function q(t,e,i){var n=i.pointers.length,s=i.changedPointers.length,o=1&e&&n-s==0,r=12&e&&n-s==0;i.isFirst=!!o,i.isFinal=!!r,o&&(t.session={}),i.eventType=e,function(t,e){var i=t.session,n=e.pointers,s=n.length;i.firstInput||(i.firstInput=W(e));s>1&&!i.firstMultiple?i.firstMultiple=W(e):1===s&&(i.firstMultiple=!1);var o=i.firstInput,r=i.firstMultiple,c=r?r.center:o.center,h=e.center=j(n);e.timeStamp=d(),e.deltaTime=e.timeStamp-o.timeStamp,e.angle=K(c,h),e.distance=G(c,h),function(t,e){var i=e.center,n=t.offsetDelta||{},s=t.prevDelta||{},o=t.prevInput||{};1!==e.eventType&&4!==o.eventType||(s=t.prevDelta={x:o.deltaX||0,y:o.deltaY||0},n=t.offsetDelta={x:i.x,y:i.y});e.deltaX=s.x+(i.x-n.x),e.deltaY=s.y+(i.y-n.y)}(i,e),e.offsetDirection=U(e.deltaX,e.deltaY);var u=V(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=u.x,e.overallVelocityY=u.y,e.overallVelocity=p(u.x)>p(u.y)?u.x:u.y,e.scale=r?(l=r.pointers,f=n,G(f[0],f[1],Y)/G(l[0],l[1],Y)):1,e.rotation=r?function(t,e){return K(e[1],e[0],Y)+K(t[1],t[0],Y)}(r.pointers,n):0,e.maxPointers=i.prevInput?e.pointers.length>i.prevInput.maxPointers?e.pointers.length:i.prevInput.maxPointers:e.pointers.length,function(t,e){var i,n,s,o,r=t.lastInterval||e,c=e.timeStamp-r.timeStamp;if(8!=e.eventType&&(c>25||r.velocity===a)){var h=e.deltaX-r.deltaX,u=e.deltaY-r.deltaY,l=V(c,h,u);n=l.x,s=l.y,i=p(l.x)>p(l.y)?l.x:l.y,o=U(h,u),t.lastInterval=e}else i=r.velocity,n=r.velocityX,s=r.velocityY,o=r.direction;e.velocity=i,e.velocityX=n,e.velocityY=s,e.direction=o}(i,e);var l,f;var v=t.element;S(e.srcEvent.target,v)&&(v=e.srcEvent.target);e.target=v}(t,i),t.emit("hammer.input",i),t.recognize(i),t.session.prevInput=i}function W(t){for(var e=[],i=0;i<t.pointers.length;)e[i]={clientX:l(t.pointers[i].clientX),clientY:l(t.pointers[i].clientY)},i++;return{timeStamp:d(),pointers:e,center:j(e),deltaX:t.deltaX,deltaY:t.deltaY}}function j(t){var e=t.length;if(1===e)return{x:l(t[0].clientX),y:l(t[0].clientY)};for(var i=0,n=0,s=0;s<e;)i+=t[s].clientX,n+=t[s].clientY,s++;return{x:l(i/e),y:l(n/e)}}function V(t,e,i){return{x:e/t||0,y:i/t||0}}function U(t,e){return t===e?1:p(t)>=p(e)?t<0?2:4:e<0?8:16}function G(t,e,i){i||(i=X);var n=e[i[0]]-t[i[0]],s=e[i[1]]-t[i[1]];return Math.sqrt(n*n+s*s)}function K(t,e,i){i||(i=X);var n=e[i[0]]-t[i[0]],s=e[i[1]]-t[i[1]];return 180*Math.atan2(s,n)/Math.PI}F.prototype={handler:function(){},init:function(){this.evEl&&E(this.element,this.evEl,this.domHandler),this.evTarget&&E(this.target,this.evTarget,this.domHandler),this.evWin&&E($(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&I(this.element,this.evEl,this.domHandler),this.evTarget&&I(this.target,this.evTarget,this.domHandler),this.evWin&&I($(this.element),this.evWin,this.domHandler)}};var Z={mousedown:1,mousemove:2,mouseup:4},J="mousedown",Q="mousemove mouseup";function tt(){this.evEl=J,this.evWin=Q,this.pressed=!1,F.apply(this,arguments)}b(tt,F,{handler:function(t){var e=Z[t.type];1&e&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=4),this.pressed&&(4&e&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:z,srcEvent:t}))}});var et={pointerdown:1,pointermove:2,pointerup:4,pointercancel:8,pointerout:8},it={2:k,3:"pen",4:z,5:"kinect"},nt="pointerdown",st="pointermove pointerup pointercancel";function ot(){this.evEl=nt,this.evWin=st,F.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}s.MSPointerEvent&&!s.PointerEvent&&(nt="MSPointerDown",st="MSPointerMove MSPointerUp MSPointerCancel"),b(ot,F,{handler:function(t){var e=this.store,i=!1,n=t.type.toLowerCase().replace("ms",""),s=et[n],o=it[t.pointerType]||t.pointerType,r=o==k,a=M(e,t.pointerId,"pointerId");1&s&&(0===t.button||r)?a<0&&(e.push(t),a=e.length-1):12&s&&(i=!0),a<0||(e[a]=t,this.callback(this.manager,s,{pointers:e,changedPointers:[t],pointerType:o,srcEvent:t}),i&&e.splice(a,1))}});var rt={touchstart:1,touchmove:2,touchend:4,touchcancel:8},at="touchstart",ct="touchstart touchmove touchend touchcancel";function ht(){this.evTarget=at,this.evWin=ct,this.started=!1,F.apply(this,arguments)}function ut(t,e){var i=A(t.touches),n=A(t.changedTouches);return 12&e&&(i=P(i.concat(n),"identifier",!0)),[i,n]}b(ht,F,{handler:function(t){var e=rt[t.type];if(1===e&&(this.started=!0),this.started){var i=ut.call(this,t,e);12&e&&i[0].length-i[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:k,srcEvent:t})}}});var lt={touchstart:1,touchmove:2,touchend:4,touchcancel:8},pt="touchstart touchmove touchend touchcancel";function dt(){this.evTarget=pt,this.targetIds={},F.apply(this,arguments)}function ft(t,e){var i=A(t.touches),n=this.targetIds;if(3&e&&1===i.length)return n[i[0].identifier]=!0,[i,i];var s,o,r=A(t.changedTouches),a=[],c=this.target;if(o=i.filter((function(t){return S(t.target,c)})),1===e)for(s=0;s<o.length;)n[o[s].identifier]=!0,s++;for(s=0;s<r.length;)n[r[s].identifier]&&a.push(r[s]),12&e&&delete n[r[s].identifier],s++;return a.length?[P(o.concat(a),"identifier",!0),a]:void 0}b(dt,F,{handler:function(t){var e=lt[t.type],i=ft.call(this,t,e);i&&this.callback(this.manager,e,{pointers:i[0],changedPointers:i[1],pointerType:k,srcEvent:t})}});function vt(){F.apply(this,arguments);var t=x(this.handler,this);this.touch=new dt(this.manager,t),this.mouse=new tt(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}function mt(t,e){1&t?(this.primaryTouch=e.changedPointers[0].identifier,gt.call(this,e)):12&t&&gt.call(this,e)}function gt(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var i={x:e.clientX,y:e.clientY};this.lastTouches.push(i);var n=this.lastTouches;setTimeout((function(){var t=n.indexOf(i);t>-1&&n.splice(t,1)}),2500)}}function yt(t){for(var e=t.srcEvent.clientX,i=t.srcEvent.clientY,n=0;n<this.lastTouches.length;n++){var s=this.lastTouches[n],o=Math.abs(e-s.x),r=Math.abs(i-s.y);if(o<=25&&r<=25)return!0}return!1}b(vt,F,{handler:function(t,e,i){var n=i.pointerType==k,s=i.pointerType==z;if(!(s&&i.sourceCapabilities&&i.sourceCapabilities.firesTouchEvents)){if(n)mt.call(this,e,i);else if(s&&yt.call(this,i))return;this.callback(t,e,i)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var Tt=N(u.style,"touchAction"),bt=Tt!==a,xt="compute",_t="auto",wt="manipulation",Et="none",It="pan-x",St="pan-y",Ct=function(){if(!bt)return!1;var t={},e=s.CSS&&s.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(i){t[i]=!e||s.CSS.supports("touch-action",i)})),t}();function Lt(t,e){this.manager=t,this.set(e)}Lt.prototype={set:function(t){t==xt&&(t=this.compute()),bt&&this.manager.element.style&&Ct[t]&&(this.manager.element.style[Tt]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return m(this.manager.recognizers,(function(e){_(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),function(t){if(C(t,Et))return Et;var e=C(t,It),i=C(t,St);if(e&&i)return Et;if(e||i)return e?It:St;if(C(t,wt))return wt;return _t}(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,i=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var n=this.actions,s=C(n,Et)&&!Ct.none,o=C(n,St)&&!Ct["pan-y"],r=C(n,It)&&!Ct["pan-x"];if(s){var a=1===t.pointers.length,c=t.distance<2,h=t.deltaTime<250;if(a&&c&&h)return}if(!r||!o)return s||o&&6&i||r&&i&B?this.preventSrc(e):void 0}},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};var Mt=32;function At(t){this.options=c({},this.defaults,t||{}),this.id=O++,this.manager=null,this.options.enable=w(this.options.enable,!0),this.state=1,this.simultaneous={},this.requireFail=[]}function Pt(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}function Nt(t){return 16==t?"down":8==t?"up":2==t?"left":4==t?"right":""}function Ot(t,e){var i=e.manager;return i?i.get(t):t}function $t(){At.apply(this,arguments)}function Dt(){$t.apply(this,arguments),this.pX=null,this.pY=null}function Ht(){$t.apply(this,arguments)}function Rt(){At.apply(this,arguments),this._timer=null,this._input=null}function kt(){$t.apply(this,arguments)}function zt(){$t.apply(this,arguments)}function Bt(){At.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function Xt(t,e){return(e=e||{}).recognizers=w(e.recognizers,Xt.defaults.preset),new Yt(t,e)}At.prototype={defaults:{},set:function(t){return c(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(v(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=Ot(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return v(t,"dropRecognizeWith",this)||(t=Ot(t,this),delete this.simultaneous[t.id]),this},requireFailure:function(t){if(v(t,"requireFailure",this))return this;var e=this.requireFail;return-1===M(e,t=Ot(t,this))&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(v(t,"dropRequireFailure",this))return this;t=Ot(t,this);var e=M(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){var e=this,i=this.state;function n(i){e.manager.emit(i,t)}i<8&&n(e.options.event+Pt(i)),n(e.options.event),t.additionalEvent&&n(t.additionalEvent),i>=8&&n(e.options.event+Pt(i))},tryEmit:function(t){if(this.canEmit())return this.emit(t);this.state=Mt},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},recognize:function(t){var e=c({},t);if(!_(this.options.enable,[this,e]))return this.reset(),void(this.state=Mt);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},process:function(t){},getTouchAction:function(){},reset:function(){}},b($t,At,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},process:function(t){var e=this.state,i=t.eventType,n=6&e,s=this.attrTest(t);return n&&(8&i||!s)?16|e:n||s?4&i?8|e:2&e?4|e:2:Mt}}),b(Dt,$t,{defaults:{event:"pan",threshold:10,pointers:1,direction:30},getTouchAction:function(){var t=this.options.direction,e=[];return 6&t&&e.push(St),t&B&&e.push(It),e},directionTest:function(t){var e=this.options,i=!0,n=t.distance,s=t.direction,o=t.deltaX,r=t.deltaY;return s&e.direction||(6&e.direction?(s=0===o?1:o<0?2:4,i=o!=this.pX,n=Math.abs(t.deltaX)):(s=0===r?1:r<0?8:16,i=r!=this.pY,n=Math.abs(t.deltaY))),t.direction=s,i&&n>e.threshold&&s&e.direction},attrTest:function(t){return $t.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=Nt(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)}}),b(Ht,$t,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[Et]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||2&this.state)},emit:function(t){if(1!==t.scale){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}}),b(Rt,At,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[_t]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,n=t.distance<e.threshold,s=t.deltaTime>e.time;if(this._input=t,!n||!i||12&t.eventType&&!s)this.reset();else if(1&t.eventType)this.reset(),this._timer=f((function(){this.state=8,this.tryEmit()}),e.time,this);else if(4&t.eventType)return 8;return Mt},reset:function(){clearTimeout(this._timer)},emit:function(t){8===this.state&&(t&&4&t.eventType?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=d(),this.manager.emit(this.options.event,this._input)))}}),b(kt,$t,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[Et]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||2&this.state)}}),b(zt,$t,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:30,pointers:1},getTouchAction:function(){return Dt.prototype.getTouchAction.call(this)},attrTest:function(t){var e,i=this.options.direction;return 30&i?e=t.overallVelocity:6&i?e=t.overallVelocityX:i&B&&(e=t.overallVelocityY),this._super.attrTest.call(this,t)&&i&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&p(e)>this.options.velocity&&4&t.eventType},emit:function(t){var e=Nt(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}}),b(Bt,At,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[wt]},process:function(t){var e=this.options,i=t.pointers.length===e.pointers,n=t.distance<e.threshold,s=t.deltaTime<e.time;if(this.reset(),1&t.eventType&&0===this.count)return this.failTimeout();if(n&&s&&i){if(4!=t.eventType)return this.failTimeout();var o=!this.pTime||t.timeStamp-this.pTime<e.interval,r=!this.pCenter||G(this.pCenter,t.center)<e.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,r&&o?this.count+=1:this.count=1,this._input=t,0===this.count%e.taps)return this.hasRequireFailures()?(this._timer=f((function(){this.state=8,this.tryEmit()}),e.interval,this),2):8}return Mt},failTimeout:function(){return this._timer=f((function(){this.state=Mt}),this.options.interval,this),Mt},reset:function(){clearTimeout(this._timer)},emit:function(){8==this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),Xt.VERSION="2.0.7",Xt.defaults={domEvents:!1,touchAction:xt,enable:!0,inputTarget:null,inputClass:null,preset:[[kt,{enable:!1}],[Ht,{enable:!1},["rotate"]],[zt,{direction:6}],[Dt,{direction:6},["swipe"]],[Bt],[Bt,{event:"doubletap",taps:2},["tap"]],[Rt]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};function Yt(t,e){var i;this.options=c({},Xt.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((i=this).options.inputClass||(H?ot:R?dt:D?vt:tt))(i,q),this.touchAction=new Lt(this,this.options.touchAction),Ft(this,!0),m(this.options.recognizers,(function(t){var e=this.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}function Ft(t,e){var i,n=t.element;n.style&&(m(t.options.cssProps,(function(s,o){i=N(n.style,o),e?(t.oldCssProps[i]=n.style[i],n.style[i]=s):n.style[i]=t.oldCssProps[i]||""})),e||(t.oldCssProps={}))}Yt.prototype={set:function(t){return c(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?2:1},recognize:function(t){var e=this.session;if(!e.stopped){var i;this.touchAction.preventDefaults(t);var n=this.recognizers,s=e.curRecognizer;(!s||s&&8&s.state)&&(s=e.curRecognizer=null);for(var o=0;o<n.length;)i=n[o],2===e.stopped||s&&i!=s&&!i.canRecognizeWith(s)?i.reset():i.recognize(t),!s&&14&i.state&&(s=e.curRecognizer=i),o++}},get:function(t){if(t instanceof At)return t;for(var e=this.recognizers,i=0;i<e.length;i++)if(e[i].options.event==t)return e[i];return null},add:function(t){if(v(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(v(t,"remove",this))return this;if(t=this.get(t)){var e=this.recognizers,i=M(e,t);-1!==i&&(e.splice(i,1),this.touchAction.update())}return this},on:function(t,e){if(t!==a&&e!==a){var i=this.handlers;return m(L(t),(function(t){i[t]=i[t]||[],i[t].push(e)})),this}},off:function(t,e){if(t!==a){var i=this.handlers;return m(L(t),(function(t){e?i[t]&&i[t].splice(M(i[t],e),1):delete i[t]})),this}},emit:function(t,e){this.options.domEvents&&function(t,e){var i=o.createEvent("Event");i.initEvent(t,!0,!0),i.gesture=e,e.target.dispatchEvent(i)}(t,e);var i=this.handlers[t]&&this.handlers[t].slice();if(i&&i.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var n=0;n<i.length;)i[n](e),n++}},destroy:function(){this.element&&Ft(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}},c(Xt,{INPUT_START:1,INPUT_MOVE:2,INPUT_END:4,INPUT_CANCEL:8,STATE_POSSIBLE:1,STATE_BEGAN:2,STATE_CHANGED:4,STATE_ENDED:8,STATE_RECOGNIZED:8,STATE_CANCELLED:16,STATE_FAILED:Mt,DIRECTION_NONE:1,DIRECTION_LEFT:2,DIRECTION_RIGHT:4,DIRECTION_UP:8,DIRECTION_DOWN:16,DIRECTION_HORIZONTAL:6,DIRECTION_VERTICAL:B,DIRECTION_ALL:30,Manager:Yt,Input:F,TouchAction:Lt,TouchInput:dt,MouseInput:tt,PointerEventInput:ot,TouchMouseInput:vt,SingleTouchInput:ht,Recognizer:At,AttrRecognizer:$t,Tap:Bt,Pan:Dt,Swipe:zt,Pinch:Ht,Rotate:kt,Press:Rt,on:E,off:I,each:m,merge:T,extend:y,assign:c,inherit:b,bindFn:x,prefixed:N}),(void 0!==s?s:"undefined"!=typeof self?self:{}).Hammer=Xt,(n=function(){return Xt}.call(e,i,e,t))===a||(t.exports=n)}(window,document)}},e={};function i(n){var s=e[n];if(void 0!==s)return s.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,i),o.exports}i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n={};return(()=>{"use strict";i.r(n),i.d(n,{default:()=>y});var t=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{on:{click:function(e){return e.stopPropagation(),t.closeLightBox(e)}}},[i("transition",{attrs:{mode:"out-in",name:"vib-container-transition"},on:{afterEnter:t.enableImageTransition,beforeLeave:t.disableImageTransition}},[t.media&&t.media.length>0?i("div",{directives:[{name:"show",rawName:"v-show",value:t.lightBoxShown,expression:"lightBoxShown"}],ref:"container",staticClass:"vib-container"},[i("div",{staticClass:"vib-content",on:{click:function(t){t.stopPropagation()}}},[i("transition",{attrs:{mode:"out-in",name:t.imageTransitionName}},[null==t.currentMedia.type||"image"==t.currentMedia.type?i("img",{key:t.currentMedia.src,staticClass:"vib-image",attrs:{src:t.currentMedia.src,srcset:t.currentMedia.srcset||"",alt:t.currentMedia.caption}}):"youtube"==t.media[t.select].type?i("div",{staticClass:"video-background"},[i("iframe",{attrs:{src:"https://www.youtube.com/embed/"+t.media[t.select].id+"?showinfo=0",width:"560",height:"315",frameborder:"0",allowfullscreen:""}})]):"video"==t.currentMedia.type?i("video",{key:t.currentMedia.sources[0].src,ref:"video",attrs:{controls:"",width:t.currentMedia.width,height:t.currentMedia.height,autoplay:t.currentMedia.autoplay}},t._l(t.currentMedia.sources,(function(t){return i("source",{key:t.src,attrs:{src:t.src,type:t.type}})})),0):t._e()])],1),t._v(" "),t.showThumbs?i("div",{staticClass:"vib-thumbnail-wrapper vib-hideable",class:{"vib-hidden":t.controlsHidden},on:{click:function(t){t.stopPropagation()},mouseover:function(e){t.interfaceHovered=!0},mouseleave:function(e){t.interfaceHovered=!1}}},t._l(t.imagesThumb,(function(e,n){return i("div",{directives:[{name:"show",rawName:"v-show",value:n>=t.thumbIndex.begin&&n<=t.thumbIndex.end,expression:"index >= thumbIndex.begin && index <= thumbIndex.end"}],key:"string"==typeof e.thumb?""+e.thumb+n:n,class:"vib-thumbnail"+(t.select===n?"-active":""),style:{backgroundImage:"url("+e.thumb+")"},on:{click:function(e){return e.stopPropagation(),t.showImage(n)}}},["video"==e.type||"youtube"==e.type?t._t("videoIcon",[i("VideoIcon")]):t._e()],2)})),0):t._e(),t._v(" "),i("div",{staticClass:"vib-footer vib-hideable",class:{"vib-hidden":t.controlsHidden},on:{mouseover:function(e){t.interfaceHovered=!0},mouseleave:function(e){t.interfaceHovered=!1}}},[t._t("customCaption",[i("div",{directives:[{name:"show",rawName:"v-show",value:t.showCaption,expression:"showCaption"}],domProps:{innerHTML:t._s(t.currentMedia.caption)}})],{currentMedia:t.currentMedia}),t._v(" "),i("div",{staticClass:"vib-footer-count"},[t._t("footer",[t._v("\n            "+t._s(t.select+1)+" / "+t._s(t.media.length)+"\n          ")],{current:t.select+1,total:t.media.length})],2)],2),t._v(" "),t.closable?i("button",{staticClass:"vib-close vib-hideable",class:{"vib-hidden":t.controlsHidden},attrs:{type:"button",title:t.closeText},on:{mouseover:function(e){t.interfaceHovered=!0},mouseleave:function(e){t.interfaceHovered=!1}}},[t._t("close",[i("CloseIcon")])],2):t._e(),t._v(" "),t.media.length>1?i("button",{staticClass:"vib-arrow vib-arrow-left vib-hideable",class:{"vib-hidden":t.controlsHidden},attrs:{type:"button",title:t.previousText},on:{click:function(e){return e.stopPropagation(),t.previousImage()},mouseover:function(e){t.interfaceHovered=!0},mouseleave:function(e){t.interfaceHovered=!1}}},[t._t("previous",[i("LeftArrowIcon")])],2):t._e(),t._v(" "),t.media.length>1?i("button",{staticClass:"vib-arrow vib-arrow-right vib-hideable",class:{"vib-hidden":t.controlsHidden},attrs:{type:"button",title:t.nextText},on:{click:function(e){return e.stopPropagation(),t.nextImage()},mouseover:function(e){t.interfaceHovered=!0},mouseleave:function(e){t.interfaceHovered=!1}}},[t._t("next",[i("RightArrowIcon")])],2):t._e()]):t._e()])],1)};t._withStripped=!0;var e=function(){var t=this.$createElement,e=this._self._c||t;return e("span",[e("svg",{attrs:{fill:"white",x:"0px",y:"0px",width:"100%",height:"100%",viewBox:"0 0 512 512"}},[e("path",{attrs:{d:"M213.7,256L213.7,256L213.7,256L380.9,81.9c4.2-4.3,4.1-11.4-0.2-15.8l-29.9-30.6c-4.3-4.4-11.3-4.5-15.5-0.2L131.1,247.9 c-2.2,2.2-3.2,5.2-3,8.1c-0.1,3,0.9,5.9,3,8.1l204.2,212.7c4.2,4.3,11.2,4.2,15.5-0.2l29.9-30.6c4.3-4.4,4.4-11.5,0.2-15.8 L213.7,256z"}})])])};function s(t,e,i,n,s,o,r,a){var c,h="function"==typeof t?t.options:t;if(e&&(h.render=e,h.staticRenderFns=i,h._compiled=!0),n&&(h.functional=!0),o&&(h._scopeId="data-v-"+o),r?(c=function(t){(t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),s&&s.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(r)},h._ssrRegister=c):s&&(c=a?function(){s.call(this,(h.functional?this.parent:this).$root.$options.shadowRoot)}:s),c)if(h.functional){h._injectStyles=c;var u=h.render;h.render=function(t,e){return c.call(e),u(t,e)}}else{var l=h.beforeCreate;h.beforeCreate=l?[].concat(l,c):[c]}return{exports:t,options:h}}e._withStripped=!0;var o=s({},e,[],!1,null,null,null);o.options.__file="src/components/LeftArrowIcon.vue";const r=o.exports;var a=function(){var t=this.$createElement,e=this._self._c||t;return e("span",[e("svg",{attrs:{fill:"white",x:"0px",y:"0px",width:"100%",height:"100%",viewBox:"0 0 512 512"}},[e("path",{attrs:{d:"M298.3,256L298.3,256L298.3,256L131.1,81.9c-4.2-4.3-4.1-11.4,0.2-15.8l29.9-30.6c4.3-4.4,11.3-4.5,15.5-0.2l204.2,212.7 c2.2,2.2,3.2,5.2,3,8.1c0.1,3-0.9,5.9-3,8.1L176.7,476.8c-4.2,4.3-11.2,4.2-15.5-0.2L131.3,446c-4.3-4.4-4.4-11.5-0.2-15.8 L298.3,256z"}})])])};a._withStripped=!0;var c=s({},a,[],!1,null,null,null);c.options.__file="src/components/RightArrowIcon.vue";const h=c.exports;var u=function(){var t=this.$createElement,e=this._self._c||t;return e("span",[e("svg",{staticStyle:{"enable-background":"new 0 0 512 512"},attrs:{fill:"white",x:"0px",y:"0px",width:"100%",height:"100%",viewBox:"0 0 512 512"}},[e("path",{attrs:{d:"M443.6,387.1L312.4,255.4l131.5-130c5.4-5.4,5.4-14.2,0-19.6l-37.4-37.6c-2.6-2.6-6.1-4-9.8-4c-3.7,0-7.2,1.5-9.8,4 L256,197.8L124.9,68.3c-2.6-2.6-6.1-4-9.8-4c-3.7,0-7.2,1.5-9.8,4L68,105.9c-5.4,5.4-5.4,14.2,0,19.6l131.5,130L68.4,387.1 c-2.6,2.6-4.1,6.1-4.1,9.8c0,3.7,1.4,7.2,4.1,9.8l37.4,37.6c2.7,2.7,6.2,4.1,9.8,4.1c3.5,0,7.1-1.3,9.8-4.1L256,313.1l130.7,131.1 c2.7,2.7,6.2,4.1,9.8,4.1c3.5,0,7.1-1.3,9.8-4.1l37.4-37.6c2.6-2.6,4.1-6.1,4.1-9.8C447.7,393.2,446.2,389.7,443.6,387.1z"}})])])};u._withStripped=!0;var l=s({},u,[],!1,null,null,null);l.options.__file="src/components/CloseIcon.vue";const p=l.exports;var d=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("svg",{staticStyle:{"enable-background":"new 0 0 271.953 271.953",margin:"12px"},attrs:{x:"0px",y:"0px",viewBox:"0 0 271.953 271.953","xml:space":"preserve"}},[i("g",[i("g",[i("path",{staticStyle:{fill:"#fff"},attrs:{d:"M135.977,271.953c75.097,0,135.977-60.879,135.977-135.977S211.074,0,135.977,0S0,60.879,0,135.977    S60.879,271.953,135.977,271.953z M250.197,135.977c0,62.979-51.241,114.22-114.22,114.22s-114.22-51.241-114.22-114.22    s51.241-114.22,114.22-114.22S250.197,72.998,250.197,135.977z"}}),t._v(" "),i("path",{staticStyle:{fill:"#fff"},attrs:{d:"M112.295,205.031c2.692,1.115,5.434,1.659,8.235,1.659c5.662,0,11.183-2.208,15.344-6.375    l48.93-48.952c8.496-8.496,8.485-22.273-0.011-30.769l-48.957-48.952c-4.161-4.161-9.73-6.375-15.393-6.375    c-2.801,0-5.461,0.544-8.153,1.659c-8.126,3.367-13.255,11.297-13.255,20.097v97.903    C99.034,193.729,104.164,201.664,112.295,205.031z M120.791,88.613v-1.588l48.952,48.952l-48.952,48.952V88.613z"}})])])])};d._withStripped=!0;var f=s({},d,[],!1,null,null,null);f.options.__file="src/components/VideoIcon.vue";const v=f.exports;var m;"undefined"!=typeof window&&(m=i(840));var g=s({components:{LeftArrowIcon:r,RightArrowIcon:h,CloseIcon:p,VideoIcon:v},props:{media:{type:Array,required:!0},disableScroll:{type:Boolean,default:!0},showLightBox:{type:Boolean,default:!0},closable:{type:Boolean,default:!0},startAt:{type:Number,default:0},nThumbs:{type:Number,default:7},showThumbs:{type:Boolean,default:!0},autoPlay:{type:Boolean,default:!1},autoPlayTime:{type:Number,default:3e3},interfaceHideTime:{type:Number,default:3e3},showCaption:{type:Boolean,default:!1},lengthToLoadMore:{type:Number,default:0},closeText:{type:String,default:"Close (Esc)"},previousText:{type:String,default:"Previous"},nextText:{type:String,default:"Next"}},data:function(){return{select:this.startAt,lightBoxShown:this.showLightBox,controlsHidden:!1,imageTransitionName:"vib-image-no-transition",timer:null,interactionTimer:null,interfaceHovered:!1}},computed:{currentMedia:function(){return this.media[this.select]},thumbIndex:function(){var t=Math.floor(this.nThumbs/2);return this.select>=t&&this.select<this.media.length-t?{begin:this.select-t+(1-this.nThumbs%2),end:this.select+t}:this.select<t?{begin:0,end:this.nThumbs-1}:{begin:this.media.length-this.nThumbs,end:this.media.length-1}},imagesThumb:function(){return this.media.map((function(t){return{thumb:t.thumb,type:t.type}}))}},watch:{lightBoxShown:function(t){null!=document&&this.onToggleLightBox(t)},select:function(){this.$emit("onImageChanged",this.select),this.select>=this.media.length-this.lengthToLoadMore-1&&this.$emit("onLoad"),this.select===this.media.length-1&&this.$emit("onLastIndex"),0===this.select&&this.$emit("onFirstIndex"),this.select===this.startAt&&this.$emit("onStartIndex")}},mounted:function(){if(this.autoPlay&&(this.timer=setInterval(this.nextImage,this.autoPlayTime)),this.onToggleLightBox(this.lightBoxShown),this.$refs.container){var t=new m(this.$refs.container);t.on("swiperight",this.previousImage),t.on("swipeleft",this.nextImage),this.$refs.container.addEventListener("mousedown",this.handleMouseActivity),this.$refs.container.addEventListener("mousemove",this.handleMouseActivity),this.$refs.container.addEventListener("touchmove",this.handleMouseActivity)}},beforeDestroy:function(){document.removeEventListener("keydown",this.addKeyEvent),this.autoPlay&&clearInterval(this.timer),this.$refs.container&&(this.$refs.container.removeEventListener("mousedown",this.handleMouseActivity),this.$refs.container.removeEventListener("mousemove",this.handleMouseActivity),this.$refs.container.removeEventListener("touchmove",this.handleMouseActivity))},methods:{onLightBoxOpen:function(){this.$emit("onOpened"),this.disableScroll&&document.querySelector("html").classList.add("no-scroll"),document.querySelector("body").classList.add("vib-open"),document.addEventListener("keydown",this.addKeyEvent),this.$refs.video&&this.$refs.video.autoplay&&this.$refs.video.play()},onLightBoxClose:function(){this.$emit("onClosed"),this.disableScroll&&document.querySelector("html").classList.remove("no-scroll"),document.querySelector("body").classList.remove("vib-open"),document.removeEventListener("keydown",this.addKeyEvent),this.$refs.video&&(this.$refs.video.pause(),this.$refs.video.currentTime="0")},onToggleLightBox:function(t){t?this.onLightBoxOpen():this.onLightBoxClose()},showImage:function(t){this.select=t,this.controlsHidden=!1,this.lightBoxShown=!0},addKeyEvent:function(t){switch(t.keyCode){case 37:this.previousImage();break;case 39:this.nextImage();break;case 27:this.closeLightBox()}},closeLightBox:function(){this.$refs.video&&this.$refs.video.pause(),this.closable&&this.$set(this,"lightBoxShown",!1)},nextImage:function(){this.$set(this,"select",(this.select+1)%this.media.length)},previousImage:function(){this.$set(this,"select",(this.select+this.media.length-1)%this.media.length)},enableImageTransition:function(){this.handleMouseActivity(),this.imageTransitionName="vib-image-transition"},disableImageTransition:function(){this.imageTransitionName="vib-image-no-transition"},handleMouseActivity:function(){clearTimeout(this.interactionTimer),this.controlsHidden&&(this.controlsHidden=!1),this.interfaceHovered?this.stopInteractionTimer():this.startInteractionTimer()},startInteractionTimer:function(){var t=this;this.interactionTimer=setTimeout((function(){t.controlsHidden=!0}),this.interfaceHideTime)},stopInteractionTimer:function(){this.interactionTimer=null}}},t,[],!1,null,null,null);g.options.__file="src/components/LightBox.vue";const y=g.exports})(),n})()}));
//# sourceMappingURL=vue-it-bigger.min.js.map