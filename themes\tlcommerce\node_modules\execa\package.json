{"_args": [["execa@5.1.1", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "execa@5.1.1", "_id": "execa@5.1.1", "_inBundle": false, "_integrity": "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==", "_location": "/execa", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "execa@5.1.1", "name": "execa", "escapedName": "execa", "rawSpec": "5.1.1", "saveSpec": null, "fetchSpec": "5.1.1"}, "_requiredBy": ["/default-gateway"], "_resolved": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "_spec": "5.1.1", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/execa/issues"}, "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "description": "Process execution for humans", "devDependencies": {"@types/node": "^14.14.10", "ava": "^2.4.0", "get-node": "^11.0.1", "is-running": "^2.1.0", "nyc": "^15.1.0", "p-event": "^4.2.0", "tempfile": "^3.0.0", "tsd": "^0.13.1", "xo": "^0.35.0"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts", "lib"], "funding": "https://github.com/sindresorhus/execa?sponsor=1", "homepage": "https://github.com/sindresorhus/execa#readme", "keywords": ["exec", "child", "process", "execute", "fork", "execfile", "spawn", "file", "shell", "bin", "binary", "binaries", "npm", "path", "local"], "license": "MIT", "name": "execa", "nyc": {"reporter": ["text", "lcov"], "exclude": ["**/fixtures/**", "**/test.js", "**/test/**"]}, "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/execa.git"}, "scripts": {"test": "xo && nyc ava && tsd"}, "version": "5.1.1"}