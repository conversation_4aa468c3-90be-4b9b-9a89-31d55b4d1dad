.v-toast {
  position: fixed;
  display: flex;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2em;
  overflow: hidden;
  z-index: 1052;
  pointer-events: none;

  &__item {
    display: inline-flex;
    align-items: center;
    animation-duration: 150ms;
    margin: 0.5em 0;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    border-radius: 0.25em;
    pointer-events: auto;
    opacity: 0.92;
    color: #fff;
    min-height: 3em;
    cursor: pointer;

    // Colors
    @each $color, $value in $toast-colors {
      &--#{$color} {
        background-color: $value;
      }
    }

    &--warning {
      color: #000
    }

    // Individual toast position
    &.v-toast__item--top, &.v-toast__item--bottom {
      align-self: center;
    }

    &.v-toast__item--top-right, &.v-toast__item--bottom-right {
      align-self: flex-end;
    }

    &.v-toast__item--top-left, &.v-toast__item--bottom-left {
      align-self: flex-start;
    }
  }

  &__text {
    margin: 0;
    padding: 0.5em 1em;
    word-break: break-word;
  }

  &__icon {
    display: none;
  }

  // Notice container positions
  &.v-toast--top {
    flex-direction: column;
  }

  &.v-toast--bottom {
    flex-direction: column-reverse;
  }

  &.v-toast--custom-parent {
    position: absolute;
  }

  @media screen and (max-width: 768px) {
    padding: 0;
    position: fixed !important;
  }
}
