{"_args": [["hash-sum@2.0.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "hash-sum@2.0.0", "_id": "hash-sum@2.0.0", "_inBundle": false, "_integrity": "sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==", "_location": "/vue-loader/hash-sum", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "hash-sum@2.0.0", "name": "hash-sum", "escapedName": "hash-sum", "rawSpec": "2.0.0", "saveSpec": null, "fetchSpec": "2.0.0"}, "_requiredBy": ["/vue-loader"], "_resolved": "https://registry.npmjs.org/hash-sum/-/hash-sum-2.0.0.tgz", "_spec": "2.0.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "authors": ["<PERSON> <nicolas<PERSON><PERSON><PERSON><PERSON>@gmail.com>"], "bugs": {"url": "https://github.com/bevacqua/hash-sum/issues"}, "dependencies": {}, "description": "Blazing fast unique hash generator", "devDependencies": {"jshint": "2.5.0", "jshint-stylish": "0.2.0", "lodash": "4.17.11", "tape": "3.0.3"}, "homepage": "https://github.com/bevacqua/hash-sum", "license": "MIT", "main": "hash-sum.js", "name": "hash-sum", "repository": {"type": "git", "url": "git://github.com/bevacqua/hash-sum.git"}, "scripts": {"test": "jshint . && tape test.js"}, "version": "2.0.0"}