{"_args": [["vuex-multi-tab-state@1.0.17", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vuex-multi-tab-state@1.0.17", "_id": "vuex-multi-tab-state@1.0.17", "_inBundle": false, "_integrity": "sha512-u9UeDLlHhSHLtLpJ8Hg1hklLB0+YY9q8zhMf837FZg22L4MnpgtTFDX1ozS0WK8l1oVFrMvofAhDN8SZWA1p2w==", "_location": "/vuex-multi-tab-state", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vuex-multi-tab-state@1.0.17", "name": "vuex-multi-tab-state", "escapedName": "vuex-multi-tab-state", "rawSpec": "1.0.17", "saveSpec": null, "fetchSpec": "1.0.17"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vuex-multi-tab-state/-/vuex-multi-tab-state-1.0.17.tgz", "_spec": "1.0.17", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/gabrielmbmb/vuex-multi-tab-state/issues"}, "dependencies": {"dot-object": "^2.1.4"}, "description": "Share and synchronize status between multiple tabs with this plugin for Vuex.", "devDependencies": {"@types/chai": "^4.2.9", "@types/chai-spies": "^1.0.1", "@types/dot-object": "^2.1.2", "@types/mocha": "^7.0.1", "@typescript-eslint/eslint-plugin": "^2.19.2", "@typescript-eslint/parser": "^2.19.2", "chai": "^4.2.0", "chai-spies": "^1.0.0", "codecov": "^3.7.2", "eslint": "^6.8.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.10.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-prettier": "^3.1.2", "jsdom": "^16.1.0", "jsdom-global": "^3.0.2", "microbundle": "^0.12.4", "mocha": "^7.0.1", "mock-local-storage": "^1.1.11", "nyc": "^15.0.0", "prettier": "^1.19.1", "ts-node": "^8.6.2", "typescript": "^3.7.5", "vue": "^2.6.11", "vuex": "^3.1.2"}, "exports": "./lib/index.modern.js", "files": ["/lib/**/*"], "homepage": "https://github.com/gabrielmbmb/vuex-multi-tab-state#readme", "keywords": ["vue", "vuex", "plugin"], "license": "MIT", "main": "./lib/index.js", "module": "./lib/index.esm.js", "name": "vuex-multi-tab-state", "repository": {"type": "git", "url": "git+https://github.com/gabrielmbmb/vuex-multi-tab-state.git"}, "scripts": {"build": "microbundle -o lib/index.js", "coverage": "nyc report --reporter=text-lcov > coverage.lcov && codecov", "format": "prettier --write \"src/**/*\"", "lint": "tsc --noEmit && eslint 'src/**/*' --quiet --fix", "prepare": "npm run build", "prepublishOnly": "npm test && npm run lint", "preversion": "npm run lint", "test": "nyc ./node_modules/mocha/bin/mocha"}, "source": "./src/index.ts", "types": "./lib/index.d.ts", "umd:main": "./lib/index.umd.js", "unpkg": "./lib/index.umd.js", "version": "1.0.17"}