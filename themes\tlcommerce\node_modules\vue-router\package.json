{"_args": [["vue-router@4.0.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "vue-router@4.0.0", "_id": "vue-router@4.0.0", "_inBundle": false, "_integrity": "sha512-RiAsDsSV73TBqppgpAKghb5exHHKsFuTii7hbhj9QHjWJNDE4lBDTr7bVALG9DH9vVcFI+7PUTBGYbytBFsWsQ==", "_location": "/vue-router", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-router@4.0.0", "name": "vue-router", "escapedName": "vue-router", "rawSpec": "4.0.0", "saveSpec": null, "fetchSpec": "4.0.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/vue-router/-/vue-router-4.0.0.tgz", "_spec": "4.0.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "description": "This is the repository for Vue Router 4 (for Vue 3)", "devDependencies": {"@docsearch/css": "^1.0.0-alpha.27", "@docsearch/js": "^1.0.0-alpha.27", "@microsoft/api-extractor": "7.8.1", "@rollup/plugin-alias": "^3.1.1", "@rollup/plugin-commonjs": "^17.0.0", "@rollup/plugin-node-resolve": "^11.0.0", "@rollup/plugin-replace": "^2.3.4", "@types/jest": "^26.0.16", "@types/jsdom": "^16.2.3", "@types/webpack": "^4.41.25", "@types/webpack-env": "^1.16.0", "@vue/compiler-sfc": "^3.0.3", "@vue/devtools-api": "^6.0.0-beta.2", "@vue/server-renderer": "^3.0.3", "algoliasearch": "^4.8.3", "axios": "^0.21.0", "brotli": "^1.3.2", "browserstack-local": "^1.4.5", "chalk": "^4.1.0", "chromedriver": "^87.0.2", "codecov": "^3.8.0", "conventional-changelog-cli": "^2.1.1", "css-loader": "^5.0.1", "dotenv": "^8.2.0", "faked-promise": "^2.2.2", "html-webpack-plugin": "^4.5.0", "jest": "^26.6.1", "jest-mock-warn": "^1.1.0", "lint-staged": "^10.5.3", "nightwatch": "^1.5.1", "nightwatch-helpers": "^1.2.0", "prettier": "^2.2.1", "rollup": "^2.34.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.29.0", "selenium-server": "^3.141.59", "serve-handler": "^6.1.3", "style-loader": "^2.0.0", "ts-jest": "^26.4.4", "ts-loader": "^8.0.11", "ts-node": "^9.0.0", "typescript": "^4.1.2", "vitepress": "^0.8.1", "vue": "^3.0.3", "vue-loader": "^16.1.0", "webpack": "^5.10.0", "webpack-bundle-analyzer": "^4.2.0", "webpack-cli": "^4.2.0", "webpack-dev-server": "^3.11.0", "yorkie": "^2.0.0"}, "files": ["dist/*.js", "dist/vue-router.d.ts", "vetur/tags.json", "vetur/attributes.json", "README.md"], "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verifyCommit.js"}, "jsdelivr": "dist/vue-router.global.js", "license": "MIT", "lint-staged": {"*.js": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "main": "dist/vue-router.cjs.js", "module": "dist/vue-router.esm-bundler.js", "name": "vue-router", "peerDependencies": {"vue": "^3.0.0"}, "scripts": {"build": "rollup -c rollup.config.js", "build:dts": "api-extractor run --local --verbose && tail -n +7 src/globalExtensions.ts >> dist/vue-router.d.ts", "build:e2e": "webpack --env.prod --config e2e/webpack.config.js", "build:playground": "webpack --env.prod", "build:size": "yarn run build && rollup -c size-checks/rollup.config.js", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "dev": "webpack serve --mode=development", "dev:e2e": "webpack serve --mode=development --config e2e/webpack.config.js", "docs": "vitepress dev docs", "docs:build": "vitepress build docs", "lint": "prettier -c --parser typescript \"{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "yarn run lint --write", "release": "bash scripts/release.sh", "test": "yarn run test:types && yarn run test:unit && yarn run build && yarn run build:dts && yarn run test:e2e", "test:dts": "tsc -p ./test-dts/tsconfig.json", "test:e2e": "yarn run test:e2e:headless && yarn run test:e2e:native", "test:e2e:bs": "node e2e/runner.js --local -e edge_pre_chrome,android44 -c e2e/nightwatch.browserstack.js --tag browserstack", "test:e2e:ci": "node e2e/runner.js -e firefox --retries 2", "test:e2e:headless": "node e2e/runner.js -e chrome-headless --skiptags no-headless", "test:e2e:native": "node e2e/runner.js -e chrome --tag no-headless", "test:types": "tsc --build tsconfig.json", "test:unit": "jest --coverage"}, "sideEffects": false, "types": "dist/vue-router.d.ts", "unpkg": "dist/vue-router.global.js", "version": "4.0.0", "vetur": {"tags": "vetur/tags.json", "attributes": "vetur/attributes.json"}}