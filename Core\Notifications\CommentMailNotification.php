<?php

namespace Core\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class CommentMailNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private $data;
    private $keywords;
    private $subject;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($data, $keywords, $subject)
    {
        $this->data = $data;
        $this->keywords = $keywords;
        $this->subject = $subject;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject($this->subject)
            ->markdown('core::base.email.comment_email_template', ['data' => $this->data,'keywords'=>$this->keywords]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
