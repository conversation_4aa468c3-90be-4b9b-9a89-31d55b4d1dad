{"_args": [["eslint-visitor-keys@3.3.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "eslint-visitor-keys@3.3.0", "_id": "eslint-visitor-keys@3.3.0", "_inBundle": false, "_integrity": "sha512-mQ+suqKJVyeuwGYHAdjMFqjCyfl8+Ldnxuyp3ldiMBFKkvytrXUZWaiPCEav8qDHKty44bD+qV1IP4T+w+xXRA==", "_location": "/eslint-visitor-keys", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "eslint-visitor-keys@3.3.0", "name": "eslint-visitor-keys", "escapedName": "eslint-visitor-keys", "rawSpec": "3.3.0", "saveSpec": null, "fetchSpec": "3.3.0"}, "_requiredBy": ["/@eslint-community/eslint-utils", "/eslint", "/espree", "/vue-eslint-parser"], "_resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.3.0.tgz", "_spec": "3.3.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/mysticatea"}, "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "description": "Constants and utilities about visitor keys to traverse AST.", "devDependencies": {"c8": "^7.7.3", "eslint": "^7.29.0", "eslint-config-eslint": "^7.0.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "mocha": "^9.0.1", "opener": "^1.5.2", "rollup": "^2.52.1", "tsd": "^0.19.1", "typescript": "^4.5.5"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "files": ["dist/index.d.ts", "dist/visitor-keys.d.ts", "dist/eslint-visitor-keys.cjs", "lib"], "homepage": "https://github.com/eslint/eslint-visitor-keys#readme", "keywords": [], "license": "Apache-2.0", "main": "dist/eslint-visitor-keys.cjs", "name": "eslint-visitor-keys", "repository": {"type": "git", "url": "git+https://github.com/eslint/eslint-visitor-keys.git"}, "scripts": {"build": "rollup -c && npm run tsc", "coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-release": "eslint-generate-release", "lint": "eslint .", "prepare": "npm run build", "publish-release": "eslint-publish-release", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run tsd", "tsc": "tsc", "tsd": "tsd"}, "type": "module", "types": "./dist/index.d.ts", "version": "3.3.0"}