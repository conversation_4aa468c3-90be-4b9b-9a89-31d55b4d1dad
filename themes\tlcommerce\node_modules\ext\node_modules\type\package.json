{"_args": [["type@2.7.2", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "type@2.7.2", "_id": "type@2.7.2", "_inBundle": false, "_integrity": "sha512-dzlvlNlt6AXU7EBSfpAscydQ7gXB+pPGsPnfJnZpiNJBDj7IaJzQlBZYGdEi4R9HmPdBv2XmWJ6YUtoTa7lmCw==", "_location": "/ext/type", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "type@2.7.2", "name": "type", "escapedName": "type", "rawSpec": "2.7.2", "saveSpec": null, "fetchSpec": "2.7.2"}, "_requiredBy": ["/ext"], "_resolved": "https://registry.npmjs.org/type/-/type-2.7.2.tgz", "_spec": "2.7.2", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://www.medikoo.com/"}, "bugs": {"url": "https://github.com/medikoo/type/issues"}, "description": "Runtime validation and processing of JavaScript types", "devDependencies": {"chai": "^4.3.6", "eslint": "^8.21.0", "eslint-config-medikoo": "^4.1.2", "git-list-updated": "^1.2.1", "github-release-from-cc-changelog": "^2.3.0", "husky": "^4.3.8", "lint-staged": "^13.0.3", "mocha": "^6.2.3", "nyc": "^15.1.0", "prettier-elastic": "^2.2.1"}, "eslintConfig": {"extends": "medikoo/es3", "root": true, "globals": {"BigInt": true, "Map": true, "Promise": true, "Set": true, "Symbol": true}, "overrides": [{"files": "test/**/*.js", "env": {"mocha": true}, "rules": {"no-eval": "off", "no-new-wrappers": "off"}}, {"files": ["string/coerce.js", "number/coerce.js"], "rules": {"no-implicit-coercion": "off"}}, {"files": "plain-object/is.js", "rules": {"no-proto": "off"}}]}, "homepage": "https://github.com/medikoo/type#readme", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "keywords": ["type", "coercion"], "license": "ISC", "lint-staged": {"*.js": ["eslint"], "*.{css,html,js,json,md,yaml,yml}": ["prettier -c"]}, "name": "type", "nyc": {"all": true, "exclude": [".github", "coverage/**", "test/**", "*.config.js"], "reporter": ["lcov", "html", "text-summary"]}, "prettier": {"printWidth": 100, "tabWidth": 4, "overrides": [{"files": ["*.md", "*.yml"], "options": {"tabWidth": 2}}]}, "repository": {"type": "git", "url": "git+https://github.com/medikoo/type.git"}, "scripts": {"coverage": "nyc npm test", "lint:updated": "pipe-git-updated --base=main --ext=js -- eslint --ignore-pattern '!*'", "prettier-check": "prettier -c --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --base=main --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettify:updated": "pipe-git-updated ---base=main -ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write", "test": "mocha --recursive"}, "typesVersions": {">=4": {"*": ["ts-types/*"]}}, "version": "2.7.2"}