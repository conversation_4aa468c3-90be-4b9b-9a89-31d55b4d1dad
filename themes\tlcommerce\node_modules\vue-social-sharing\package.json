{"_args": [["vue-social-sharing@4.0.0-alpha4", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue-social-sharing@4.0.0-alpha4", "_id": "vue-social-sharing@4.0.0-alpha4", "_inBundle": false, "_integrity": "sha512-hMbgpZkY5aRAiznSB/sgzdMVgdSbkmIHEaELX7pUbuUd6KS1Z/GAi7a/q0Qn+GFn0+6qMMvwyp3ZOE0+WYPQ1w==", "_location": "/vue-social-sharing", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-social-sharing@4.0.0-alpha4", "name": "vue-social-sharing", "escapedName": "vue-social-sharing", "rawSpec": "4.0.0-alpha4", "saveSpec": null, "fetchSpec": "4.0.0-alpha4"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/vue-social-sharing/-/vue-social-sharing-4.0.0-alpha4.tgz", "_spec": "4.0.0-alpha4", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/nicolasbeauvais/vue-social-sharing/issues"}, "dependencies": {}, "description": "A Vue.js component for sharing links to social networks", "devDependencies": {"@babel/core": "^7.5.5", "@babel/plugin-proposal-class-properties": "^7.5.5", "@babel/plugin-transform-runtime": "^7.5.5", "@babel/preset-env": "^7.5.5", "@storybook/addon-notes": "^5.1.10", "@storybook/vue": "^5.1.10", "@vue/compiler-sfc": "^3.0.11", "@vue/test-utils": "^2.0.0-rc.6", "babel-eslint": "^10.1.0", "babel-jest": "^26.0.1", "babel-loader": "^8.0.6", "babel-preset-vue": "^2.0.2", "conventional-github-releaser": "^3.1.3", "cross-env": "^7.0.3", "eslint": "^7.0.0", "eslint-config-vue": "^2.0.2", "eslint-plugin-jest": "^23.11.0", "eslint-plugin-vue": "^6.2.2", "husky": "^3.0.2", "jest": "^26.0.1", "lint-staged": "^9.2.1", "poi": "^12.7.1", "uglifyjs-webpack-plugin": "^2.2.0", "vue": "^3.0.0", "vue-loader": "^16.2.0", "webpack": "^4.39.1", "webpack-cli": "^3.3.6"}, "files": ["dist", "src", "nuxt", "vetur", "types/*.d.ts"], "homepage": "https://github.com/nicolasbeauvais/vue-social-sharing#readme", "keywords": ["nuxtjs", "plugin", "renderless-components", "share", "social", "social-network", "social-networks", "social-shares", "ssr", "vue", "vue-component", "vue-components", "v<PERSON><PERSON><PERSON>", "vuejs-components"], "license": "MIT", "main": "dist/vue-social-sharing.js", "name": "vue-social-sharing", "peerDependencies": {"vue": "^3.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/nicolasbeauvais/vue-social-sharing.git"}, "scripts": {"build": "yarn run build-dist && yarn run build-storybook", "build-dist": "cross-env NODE_ENV=production webpack --config webpack.config.js", "build-storybook": "build-storybook -c .storybook -o docs", "example": "poi -s examples/index.js", "lint": "eslint --ext js --ext vue src nuxt examples .storybook tests", "release": "yarn run lint && yarn run test && conventional-github-releaser -p angular && npm publish", "storybook": "start-storybook", "test": "jest tests"}, "types": "types/index.d.ts", "version": "4.0.0-alpha4", "vetur": {"tags": "vetur/vetur-tags.json"}}