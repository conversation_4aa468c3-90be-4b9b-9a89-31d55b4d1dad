{"_args": [["vue@2.7.14", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue@2.7.14", "_id": "vue@2.7.14", "_inBundle": false, "_integrity": "sha512-b2qkFyOM0kwqWFuQmgd4o+uHGU7T+2z3T+WQp8UBjADfEv2n4FEMffzBmCKNP0IGzOEEfYjvtcC62xaSKeQDrQ==", "_location": "/vue-cool-lightbox/vue", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue@2.7.14", "name": "vue", "escapedName": "vue", "rawSpec": "2.7.14", "saveSpec": null, "fetchSpec": "2.7.14"}, "_requiredBy": ["/vue-cool-lightbox"], "_resolved": "https://registry.npmjs.org/vue/-/vue-2.7.14.tgz", "_spec": "2.7.14", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/vue/issues"}, "dependencies": {"@vue/compiler-sfc": "2.7.14", "csstype": "^3.1.0"}, "description": "Reactive, component-oriented view layer for modern web interfaces.", "devDependencies": {"@babel/parser": "^7.18.4", "@microsoft/api-extractor": "^7.25.0", "@rollup/plugin-alias": "^3.1.9", "@rollup/plugin-commonjs": "^22.0.0", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-replace": "^4.0.0", "@types/he": "^1.1.2", "@types/node": "^17.0.41", "chalk": "^4.1.2", "conventional-changelog-cli": "^2.2.2", "cross-spawn": "^7.0.3", "enquirer": "^2.3.6", "esbuild": "^0.14.43", "execa": "^4.1.0", "he": "^1.2.0", "jasmine-core": "^4.2.0", "jsdom": "^19.0.0", "karma": "^6.3.20", "karma-chrome-launcher": "^3.1.1", "karma-cli": "^2.0.0", "karma-esbuild": "^2.2.4", "karma-jasmine": "^5.0.1", "lint-staged": "^12.5.0", "lodash": "^4.17.21", "marked": "^4.0.16", "minimist": "^1.2.6", "postcss": "^8.4.14", "prettier": "^2.6.2", "puppeteer": "^14.3.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-typescript2": "^0.32.0", "semver": "^7.3.7", "shelljs": "^0.8.5", "terser": "^5.14.0", "todomvc-app-css": "^2.4.2", "ts-node": "^10.8.1", "tslib": "^2.4.0", "typescript": "^4.8.4", "vitest": "^0.12.10", "yorkie": "^2.0.0"}, "exports": {".": {"import": {"node": "./dist/vue.runtime.mjs", "default": "./dist/vue.runtime.esm.js"}, "require": "./dist/vue.runtime.common.js", "types": "./types/index.d.ts"}, "./compiler-sfc": {"import": "./compiler-sfc/index.mjs", "require": "./compiler-sfc/index.js"}, "./dist/*": "./dist/*", "./types/*": "./types/*", "./package.json": "./package.json"}, "files": ["src", "dist/*.js", "dist/*.mjs", "types/*.d.ts", "compiler-sfc", "packages/compiler-sfc"], "gitHooks": {"pre-commit": "lint-staged", "commit-msg": "node scripts/verify-commit-msg.js"}, "homepage": "https://github.com/vuejs/vue#readme", "jsdelivr": "dist/vue.js", "keywords": ["vue"], "license": "MIT", "lint-staged": {"*.js": ["prettier --write"], "*.ts": ["prettier --parser=typescript --write"]}, "main": "dist/vue.runtime.common.js", "module": "dist/vue.runtime.esm.js", "name": "vue", "packageManager": "pnpm@7.1.0", "repository": {"type": "git", "url": "git+https://github.com/vuejs/vue.git"}, "scripts": {"bench:ssr": "npm run build:ssr && node benchmarks/ssr/renderToString.js && node benchmarks/ssr/renderToStream.js", "build": "node scripts/build.js", "build:ssr": "npm run build -- runtime-cjs,server-renderer", "build:types": "rimraf temp && tsc --declaration --emitDeclarationOnly --outDir temp && api-extractor run && api-extractor run -c packages/compiler-sfc/api-extractor.json", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "dev": "rollup -w -c scripts/config.js --environment TARGET:full-dev", "dev:cjs": "rollup -w -c scripts/config.js --environment TARGET:runtime-cjs-dev", "dev:compiler": "rollup -w -c scripts/config.js --environment TARGET:compiler ", "dev:esm": "rollup -w -c scripts/config.js --environment TARGET:runtime-esm", "dev:ssr": "rollup -w -c scripts/config.js --environment TARGET:server-renderer", "format": "prettier --write --parser typescript \"(src|test|packages|types)/**/*.ts\"", "release": "node scripts/release.js", "test": "npm run ts-check && npm run test:types && npm run test:unit && npm run test:e2e && npm run test:ssr && npm run test:sfc", "test:e2e": "npm run build -- full-prod,server-renderer-basic && vitest run test/e2e", "test:sfc": "vitest run compiler-sfc", "test:ssr": "npm run build:ssr && vitest run server-renderer", "test:transition": "karma start test/transition/karma.conf.js", "test:types": "npm run build:types && tsc -p ./types/tsconfig.json", "test:unit": "vitest run test/unit", "ts-check": "tsc -p tsconfig.json --noEmit", "ts-check:test": "tsc -p test/tsconfig.json --noEmit"}, "sideEffects": false, "typings": "types/index.d.ts", "unpkg": "dist/vue.js", "version": "2.7.14"}