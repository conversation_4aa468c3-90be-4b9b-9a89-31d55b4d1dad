/*!
  * vue-i18n v9.2.2
  * (c) 2022 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
import{createVNode as e,Text as t,ref as a,computed as n,watch as l,getCurrentInstance as r,Fragment as o,h as s,effectScope as i,inject as c,onMounted as u,onUnmounted as m,shallowRef as f,onBeforeMount as g,isRef as _}from"vue";const p="undefined"!=typeof window,v="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,b=e=>v?Symbol(e):e,d=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),k=e=>"number"==typeof e&&isFinite(e),h=e=>"[object RegExp]"===D(e),E=e=>C(e)&&0===Object.keys(e).length;function L(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const F=Object.assign;function y(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const T=Object.prototype.hasOwnProperty;function N(e,t){return T.call(e,t)}const R=Array.isArray,I=e=>"function"==typeof e,W=e=>"string"==typeof e,M=e=>"boolean"==typeof e,O=e=>null!==e&&"object"==typeof e,w=Object.prototype.toString,D=e=>w.call(e),C=e=>"[object Object]"===D(e),P=15;function A(e,t,a={}){const{domain:n,messages:l,args:r}=a,o=new SyntaxError(String(e));return o.code=e,t&&(o.location=t),o.domain=n,o}const S=[];S[0]={w:[0],i:[3,0],"[":[4],o:[7]},S[1]={w:[1],".":[2],"[":[4],o:[7]},S[2]={w:[2],i:[3,0],0:[3,0]},S[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},S[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},S[5]={"'":[4,0],o:8,l:[5,0]},S[6]={'"':[4,0],o:8,l:[6,0]};const $=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function U(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function H(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(a=t,$.test(a)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var a}const j=new Map;function V(e,t){return O(e)?e[t]:null}const x=e=>e,G=e=>"",B=e=>0===e.length?"":e.join(""),Y=e=>null==e?"":R(e)||C(e)&&e.toString===w?JSON.stringify(e,null,2):String(e);function X(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function z(e={}){const t=e.locale,a=function(e){const t=k(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(k(e.named.count)||k(e.named.n))?k(e.named.count)?e.named.count:k(e.named.n)?e.named.n:t:t}(e),n=O(e.pluralRules)&&W(t)&&I(e.pluralRules[t])?e.pluralRules[t]:X,l=O(e.pluralRules)&&W(t)&&I(e.pluralRules[t])?X:void 0,r=e.list||[],o=e.named||{};k(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(a,o);function s(t){const a=I(e.messages)?e.messages(t):!!O(e.messages)&&e.messages[t];return a||(e.parent?e.parent.message(t):G)}const i=C(e.processor)&&I(e.processor.normalize)?e.processor.normalize:B,c=C(e.processor)&&I(e.processor.interpolate)?e.processor.interpolate:Y,u={list:e=>r[e],named:e=>o[e],plural:e=>e[n(a,e.length,l)],linked:(t,...a)=>{const[n,l]=a;let r="text",o="";1===a.length?O(n)?(o=n.modifier||o,r=n.type||r):W(n)&&(o=n||o):2===a.length&&(W(n)&&(o=n||o),W(l)&&(r=l||r));let i=s(t)(u);return"vnode"===r&&R(i)&&o&&(i=i[0]),o?(c=o,e.modifiers?e.modifiers[c]:x)(i,r):i;var c},message:s,type:C(e.processor)&&W(e.processor.type)?e.processor.type:"text",interpolate:c,normalize:i};return u}function J(e,t,a){return[...new Set([a,...R(t)?t:O(t)?Object.keys(t):W(t)?[t]:[a]])]}function q(e,t,a){const n=W(a)?a:ee,l=e;l.__localeChainCache||(l.__localeChainCache=new Map);let r=l.__localeChainCache.get(n);if(!r){r=[];let e=[a];for(;R(e);)e=Z(r,e,t);const o=R(t)||!C(t)?t:t.default?t.default:null;e=W(o)?[o]:o,R(e)&&Z(r,e,!1),l.__localeChainCache.set(n,r)}return r}function Z(e,t,a){let n=!0;for(let l=0;l<t.length&&M(n);l++){const r=t[l];W(r)&&(n=Q(e,t[l],a))}return n}function Q(e,t,a){let n;const l=t.split("-");do{n=K(e,l.join("-"),a),l.splice(-1,1)}while(l.length&&!0===n);return n}function K(e,t,a){let n=!1;if(!e.includes(t)&&(n=!0,t)){n="!"!==t[t.length-1];const l=t.replace(/!/g,"");e.push(l),(R(a)||C(a))&&a[l]&&(n=a[l])}return n}const ee="en-US",te=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let ae,ne;let le=0;function re(e={}){const t=W(e.version)?e.version:"9.2.2",a=W(e.locale)?e.locale:ee,n=R(e.fallbackLocale)||C(e.fallbackLocale)||W(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,l=C(e.messages)?e.messages:{[a]:{}},r=C(e.datetimeFormats)?e.datetimeFormats:{[a]:{}},o=C(e.numberFormats)?e.numberFormats:{[a]:{}},s=F({},e.modifiers||{},{upper:(e,t)=>"text"===t&&W(e)?e.toUpperCase():"vnode"===t&&O(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&W(e)?e.toLowerCase():"vnode"===t&&O(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&W(e)?te(e):"vnode"===t&&O(e)&&"__v_isVNode"in e?te(e.children):e}),i=e.pluralRules||{},c=I(e.missing)?e.missing:null,u=!M(e.missingWarn)&&!h(e.missingWarn)||e.missingWarn,m=!M(e.fallbackWarn)&&!h(e.fallbackWarn)||e.fallbackWarn,f=!!e.fallbackFormat,g=!!e.unresolving,_=I(e.postTranslation)?e.postTranslation:null,p=C(e.processor)?e.processor:null,v=!M(e.warnHtmlMessage)||e.warnHtmlMessage,b=!!e.escapeParameter,d=I(e.messageCompiler)?e.messageCompiler:undefined,k=I(e.messageResolver)?e.messageResolver:ae||V,E=I(e.localeFallbacker)?e.localeFallbacker:ne||J,y=O(e.fallbackContext)?e.fallbackContext:void 0,T=I(e.onWarn)?e.onWarn:L,N=e,w=O(N.__datetimeFormatters)?N.__datetimeFormatters:new Map,D=O(N.__numberFormatters)?N.__numberFormatters:new Map,P=O(N.__meta)?N.__meta:{};le++;const A={version:t,cid:le,locale:a,fallbackLocale:n,messages:l,modifiers:s,pluralRules:i,missing:c,missingWarn:u,fallbackWarn:m,fallbackFormat:f,unresolving:g,postTranslation:_,processor:p,warnHtmlMessage:v,escapeParameter:b,messageCompiler:d,messageResolver:k,localeFallbacker:E,fallbackContext:y,onWarn:T,__meta:P};return A.datetimeFormats=r,A.numberFormats=o,A.__datetimeFormatters=w,A.__numberFormatters=D,A}function oe(e,t,a,n,l){const{missing:r,onWarn:o}=e;if(null!==r){const n=r(e,a,t,l);return W(n)?n:t}return t}function se(e,t,a){e.__localeChainCache=new Map,e.localeFallbacker(e,a,t)}let ie=P;const ce=()=>++ie,ue={INVALID_ARGUMENT:ie,INVALID_DATE_ARGUMENT:ce(),INVALID_ISO_DATE_ARGUMENT:ce(),__EXTEND_POINT__:ce()},me=()=>"",fe=e=>I(e);function ge(e,...t){const{fallbackFormat:a,postTranslation:n,unresolving:l,messageCompiler:r,fallbackLocale:o,messages:s}=e,[i,c]=ve(...t),u=M(c.missingWarn)?c.missingWarn:e.missingWarn,m=M(c.fallbackWarn)?c.fallbackWarn:e.fallbackWarn,f=M(c.escapeParameter)?c.escapeParameter:e.escapeParameter,g=!!c.resolvedMessage,_=W(c.default)||M(c.default)?M(c.default)?r?i:()=>i:c.default:a?r?i:()=>i:"",p=a||""!==_,v=W(c.locale)?c.locale:e.locale;f&&function(e){R(e.list)?e.list=e.list.map((e=>W(e)?y(e):e)):O(e.named)&&Object.keys(e.named).forEach((t=>{W(e.named[t])&&(e.named[t]=y(e.named[t]))}))}(c);let[b,d,h]=g?[i,v,s[v]||{}]:_e(e,i,v,o,m,u),E=b,L=i;if(g||W(E)||fe(E)||p&&(E=_,L=E),!(g||(W(E)||fe(E))&&W(d)))return l?-1:i;let F=!1;const T=fe(E)?E:pe(e,i,d,E,L,(()=>{F=!0}));if(F)return E;const N=function(e,t,a,n){const{modifiers:l,pluralRules:r,messageResolver:o,fallbackLocale:s,fallbackWarn:i,missingWarn:c,fallbackContext:u}=e,m=n=>{let l=o(a,n);if(null==l&&u){const[,,e]=_e(u,n,t,s,i,c);l=o(e,n)}if(W(l)){let a=!1;const r=pe(e,n,t,l,n,(()=>{a=!0}));return a?me:r}return fe(l)?l:me},f={locale:t,modifiers:l,pluralRules:r,messages:m};e.processor&&(f.processor=e.processor);n.list&&(f.list=n.list);n.named&&(f.named=n.named);k(n.plural)&&(f.pluralIndex=n.plural);return f}(e,d,h,c),I=function(e,t,a){return t(a)}(0,T,z(N));return n?n(I,i):I}function _e(e,t,a,n,l,r){const{messages:o,onWarn:s,messageResolver:i,localeFallbacker:c}=e,u=c(e,n,a);let m,f={},g=null;for(let a=0;a<u.length&&(m=u[a],f=o[m]||{},null===(g=i(f,t))&&(g=f[t]),!W(g)&&!I(g));a++){const a=oe(e,t,m,0,"translate");a!==t&&(g=a)}return[g,m,f]}function pe(e,t,a,n,l,r){const{messageCompiler:o,warnHtmlMessage:s}=e;if(fe(n)){const e=n;return e.locale=e.locale||a,e.key=e.key||t,e}if(null==o){const e=()=>n;return e.locale=a,e.key=t,e}const i=o(n,function(e,t,a,n,l,r){return{warnHtmlMessage:l,onError:e=>{throw r&&r(e),e},onCacheKey:e=>((e,t,a)=>d({l:e,k:t,s:a}))(t,a,e)}}(0,a,l,0,s,r));return i.locale=a,i.key=t,i.source=n,i}function ve(...e){const[t,a,n]=e,l={};if(!W(t)&&!k(t)&&!fe(t))throw Error(ue.INVALID_ARGUMENT);const r=k(t)?String(t):(fe(t),t);return k(a)?l.plural=a:W(a)?l.default=a:C(a)&&!E(a)?l.named=a:R(a)&&(l.list=a),k(n)?l.plural=n:W(n)?l.default=n:C(n)&&F(l,n),[r,l]}function be(e,...t){const{datetimeFormats:a,unresolving:n,fallbackLocale:l,onWarn:r,localeFallbacker:o}=e,{__datetimeFormatters:s}=e,[i,c,u,m]=ke(...t);M(u.missingWarn)?u.missingWarn:e.missingWarn;M(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const f=!!u.part,g=W(u.locale)?u.locale:e.locale,_=o(e,l,g);if(!W(i)||""===i)return new Intl.DateTimeFormat(g,m).format(c);let p,v={},b=null;for(let t=0;t<_.length&&(p=_[t],v=a[p]||{},b=v[i],!C(b));t++)oe(e,i,p,0,"datetime format");if(!C(b)||!W(p))return n?-1:i;let d=`${p}__${i}`;E(m)||(d=`${d}__${JSON.stringify(m)}`);let k=s.get(d);return k||(k=new Intl.DateTimeFormat(p,F({},b,m)),s.set(d,k)),f?k.formatToParts(c):k.format(c)}const de=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function ke(...e){const[t,a,n,l]=e,r={};let o,s={};if(W(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(ue.INVALID_ISO_DATE_ARGUMENT);const a=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();o=new Date(a);try{o.toISOString()}catch(e){throw Error(ue.INVALID_ISO_DATE_ARGUMENT)}}else if("[object Date]"===D(t)){if(isNaN(t.getTime()))throw Error(ue.INVALID_DATE_ARGUMENT);o=t}else{if(!k(t))throw Error(ue.INVALID_ARGUMENT);o=t}return W(a)?r.key=a:C(a)&&Object.keys(a).forEach((e=>{de.includes(e)?s[e]=a[e]:r[e]=a[e]})),W(n)?r.locale=n:C(n)&&(s=n),C(l)&&(s=l),[r.key||"",o,r,s]}function he(e,t,a){const n=e;for(const e in a){const a=`${t}__${e}`;n.__datetimeFormatters.has(a)&&n.__datetimeFormatters.delete(a)}}function Ee(e,...t){const{numberFormats:a,unresolving:n,fallbackLocale:l,onWarn:r,localeFallbacker:o}=e,{__numberFormatters:s}=e,[i,c,u,m]=Fe(...t);M(u.missingWarn)?u.missingWarn:e.missingWarn;M(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn;const f=!!u.part,g=W(u.locale)?u.locale:e.locale,_=o(e,l,g);if(!W(i)||""===i)return new Intl.NumberFormat(g,m).format(c);let p,v={},b=null;for(let t=0;t<_.length&&(p=_[t],v=a[p]||{},b=v[i],!C(b));t++)oe(e,i,p,0,"number format");if(!C(b)||!W(p))return n?-1:i;let d=`${p}__${i}`;E(m)||(d=`${d}__${JSON.stringify(m)}`);let k=s.get(d);return k||(k=new Intl.NumberFormat(p,F({},b,m)),s.set(d,k)),f?k.formatToParts(c):k.format(c)}const Le=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Fe(...e){const[t,a,n,l]=e,r={};let o={};if(!k(t))throw Error(ue.INVALID_ARGUMENT);const s=t;return W(a)?r.key=a:C(a)&&Object.keys(a).forEach((e=>{Le.includes(e)?o[e]=a[e]:r[e]=a[e]})),W(n)?r.locale=n:C(n)&&(o=n),C(l)&&(o=l),[r.key||"",s,r,o]}function ye(e,t,a){const n=e;for(const e in a){const a=`${t}__${e}`;n.__numberFormatters.has(a)&&n.__numberFormatters.delete(a)}}const Te="9.2.2";let Ne=P;const Re=()=>++Ne,Ie={UNEXPECTED_RETURN_TYPE:Ne,INVALID_ARGUMENT:Re(),MUST_BE_CALL_SETUP_TOP:Re(),NOT_INSLALLED:Re(),NOT_AVAILABLE_IN_LEGACY_MODE:Re(),REQUIRED_VALUE:Re(),INVALID_VALUE:Re(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Re(),NOT_INSLALLED_WITH_PROVIDE:Re(),UNEXPECTED_ERROR:Re(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Re(),BRIDGE_SUPPORT_VUE_2_ONLY:Re(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Re(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Re(),__EXTEND_POINT__:Re()};const We=b("__transrateVNode"),Me=b("__datetimeParts"),Oe=b("__numberParts"),we=b("__setPluralRules"),De=b("__injectWithOption");function Ce(e){if(!O(e))return e;for(const t in e)if(N(e,t))if(t.includes(".")){const a=t.split("."),n=a.length-1;let l=e;for(let e=0;e<n;e++)a[e]in l||(l[a[e]]={}),l=l[a[e]];l[a[n]]=e[t],delete e[t],O(l[a[n]])&&Ce(l[a[n]])}else O(e[t])&&Ce(e[t]);return e}function Pe(e,t){const{messages:a,__i18n:n,messageResolver:l,flatJson:r}=t,o=C(a)?a:R(n)?{}:{[e]:{}};if(R(n)&&n.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:a}=e;t?(o[t]=o[t]||{},Se(a,o[t])):Se(a,o)}else W(e)&&Se(JSON.parse(e),o)})),null==l&&r)for(const e in o)N(o,e)&&Ce(o[e]);return o}const Ae=e=>!O(e)||R(e);function Se(e,t){if(Ae(e)||Ae(t))throw Error(Ie.INVALID_VALUE);for(const a in e)N(e,a)&&(Ae(e[a])||Ae(t[a])?t[a]=e[a]:Se(e[a],t[a]))}function $e(e,t,a){let n=O(t.messages)?t.messages:{};"__i18nGlobal"in a&&(n=Pe(e.locale.value,{messages:n,__i18n:a.__i18nGlobal}));const l=Object.keys(n);if(l.length&&l.forEach((t=>{e.mergeLocaleMessage(t,n[t])})),O(t.datetimeFormats)){const a=Object.keys(t.datetimeFormats);a.length&&a.forEach((a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])}))}if(O(t.numberFormats)){const a=Object.keys(t.numberFormats);a.length&&a.forEach((a=>{e.mergeNumberFormat(a,t.numberFormats[a])}))}}function Ue(a){return e(t,null,a,0)}let He=0;function je(e){return(t,a,n,l)=>e(a,n,r()||void 0,l)}function Ve(e={},t){const{__root:r}=e,o=void 0===r;let s=!M(e.inheritLocale)||e.inheritLocale;const i=a(r&&s?r.locale.value:W(e.locale)?e.locale:ee),c=a(r&&s?r.fallbackLocale.value:W(e.fallbackLocale)||R(e.fallbackLocale)||C(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:i.value),u=a(Pe(i.value,e)),m=a(C(e.datetimeFormats)?e.datetimeFormats:{[i.value]:{}}),f=a(C(e.numberFormats)?e.numberFormats:{[i.value]:{}});let g=r?r.missingWarn:!M(e.missingWarn)&&!h(e.missingWarn)||e.missingWarn,_=r?r.fallbackWarn:!M(e.fallbackWarn)&&!h(e.fallbackWarn)||e.fallbackWarn,v=r?r.fallbackRoot:!M(e.fallbackRoot)||e.fallbackRoot,b=!!e.fallbackFormat,d=I(e.missing)?e.missing:null,E=I(e.missing)?je(e.missing):null,L=I(e.postTranslation)?e.postTranslation:null,y=r?r.warnHtmlMessage:!M(e.warnHtmlMessage)||e.warnHtmlMessage,T=!!e.escapeParameter;const N=r?r.modifiers:C(e.modifiers)?e.modifiers:{};let w,D=e.pluralRules||r&&r.pluralRules;w=(()=>{const t={version:"9.2.2",locale:i.value,fallbackLocale:c.value,messages:u.value,modifiers:N,pluralRules:D,missing:null===E?void 0:E,missingWarn:g,fallbackWarn:_,fallbackFormat:b,unresolving:!0,postTranslation:null===L?void 0:L,warnHtmlMessage:y,escapeParameter:T,messageResolver:e.messageResolver,__meta:{framework:"vue"}};t.datetimeFormats=m.value,t.numberFormats=f.value,t.__datetimeFormatters=C(w)?w.__datetimeFormatters:void 0,t.__numberFormatters=C(w)?w.__numberFormatters:void 0;return re(t)})(),se(w,i.value,c.value);const P=n({get:()=>i.value,set:e=>{i.value=e,w.locale=i.value}}),A=n({get:()=>c.value,set:e=>{c.value=e,w.fallbackLocale=c.value,se(w,i.value,e)}}),S=n((()=>u.value)),$=n((()=>m.value)),U=n((()=>f.value));const H=(e,t,a,n,l,o)=>{let s;if(i.value,c.value,u.value,m.value,f.value,s=e(w),k(s)&&-1===s){const[e,a]=t();return r&&v?n(r):l(e)}if(o(s))return s;throw Error(Ie.UNEXPECTED_RETURN_TYPE)};function j(...e){return H((t=>Reflect.apply(ge,null,[t,...e])),(()=>ve(...e)),0,(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>W(e)))}const V={normalize:function(e){return e.map((e=>W(e)||k(e)||M(e)?Ue(String(e)):e))},interpolate:e=>e,type:"vnode"};function x(e){return u.value[e]||{}}He++,r&&p&&(l(r.locale,(e=>{s&&(i.value=e,w.locale=e,se(w,i.value,c.value))})),l(r.fallbackLocale,(e=>{s&&(c.value=e,w.fallbackLocale=e,se(w,i.value,c.value))})));const G={id:He,locale:P,fallbackLocale:A,get inheritLocale(){return s},set inheritLocale(e){s=e,e&&r&&(i.value=r.locale.value,c.value=r.fallbackLocale.value,se(w,i.value,c.value))},get availableLocales(){return Object.keys(u.value).sort()},messages:S,get modifiers(){return N},get pluralRules(){return D||{}},get isGlobal(){return o},get missingWarn(){return g},set missingWarn(e){g=e,w.missingWarn=g},get fallbackWarn(){return _},set fallbackWarn(e){_=e,w.fallbackWarn=_},get fallbackRoot(){return v},set fallbackRoot(e){v=e},get fallbackFormat(){return b},set fallbackFormat(e){b=e,w.fallbackFormat=b},get warnHtmlMessage(){return y},set warnHtmlMessage(e){y=e,w.warnHtmlMessage=e},get escapeParameter(){return T},set escapeParameter(e){T=e,w.escapeParameter=e},t:j,getLocaleMessage:x,setLocaleMessage:function(e,t){u.value[e]=t,w.messages=u.value},mergeLocaleMessage:function(e,t){u.value[e]=u.value[e]||{},Se(t,u.value[e]),w.messages=u.value},getPostTranslationHandler:function(){return I(L)?L:null},setPostTranslationHandler:function(e){L=e,w.postTranslation=e},getMissingHandler:function(){return d},setMissingHandler:function(e){null!==e&&(E=je(e)),d=e,w.missing=E},[we]:function(e){D=e,w.pluralRules=D}};return G.datetimeFormats=$,G.numberFormats=U,G.rt=function(...e){const[t,a,n]=e;if(n&&!O(n))throw Error(Ie.INVALID_ARGUMENT);return j(t,a,F({resolvedMessage:!0},n||{}))},G.te=function(e,t){const a=x(W(t)?t:i.value);return null!==w.messageResolver(a,e)},G.tm=function(e){const t=function(e){let t=null;const a=q(w,c.value,i.value);for(let n=0;n<a.length;n++){const l=u.value[a[n]]||{},r=w.messageResolver(l,e);if(null!=r){t=r;break}}return t}(e);return null!=t?t:r&&r.tm(e)||{}},G.d=function(...e){return H((t=>Reflect.apply(be,null,[t,...e])),(()=>ke(...e)),0,(t=>Reflect.apply(t.d,t,[...e])),(()=>""),(e=>W(e)))},G.n=function(...e){return H((t=>Reflect.apply(Ee,null,[t,...e])),(()=>Fe(...e)),0,(t=>Reflect.apply(t.n,t,[...e])),(()=>""),(e=>W(e)))},G.getDateTimeFormat=function(e){return m.value[e]||{}},G.setDateTimeFormat=function(e,t){m.value[e]=t,w.datetimeFormats=m.value,he(w,e,t)},G.mergeDateTimeFormat=function(e,t){m.value[e]=F(m.value[e]||{},t),w.datetimeFormats=m.value,he(w,e,t)},G.getNumberFormat=function(e){return f.value[e]||{}},G.setNumberFormat=function(e,t){f.value[e]=t,w.numberFormats=f.value,ye(w,e,t)},G.mergeNumberFormat=function(e,t){f.value[e]=F(f.value[e]||{},t),w.numberFormats=f.value,ye(w,e,t)},G[De]=e.__injectWithOption,G[We]=function(...e){return H((t=>{let a;const n=t;try{n.processor=V,a=Reflect.apply(ge,null,[n,...e])}finally{n.processor=null}return a}),(()=>ve(...e)),0,(t=>t[We](...e)),(e=>[Ue(e)]),(e=>R(e)))},G[Me]=function(...e){return H((t=>Reflect.apply(be,null,[t,...e])),(()=>ke(...e)),0,(t=>t[Me](...e)),(()=>[]),(e=>W(e)||R(e)))},G[Oe]=function(...e){return H((t=>Reflect.apply(Ee,null,[t,...e])),(()=>Fe(...e)),0,(t=>t[Oe](...e)),(()=>[]),(e=>W(e)||R(e)))},G}function xe(e={},t){{const t=Ve(function(e){const t=W(e.locale)?e.locale:ee,a=W(e.fallbackLocale)||R(e.fallbackLocale)||C(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,n=I(e.missing)?e.missing:void 0,l=!M(e.silentTranslationWarn)&&!h(e.silentTranslationWarn)||!e.silentTranslationWarn,r=!M(e.silentFallbackWarn)&&!h(e.silentFallbackWarn)||!e.silentFallbackWarn,o=!M(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,i=C(e.modifiers)?e.modifiers:{},c=e.pluralizationRules,u=I(e.postTranslation)?e.postTranslation:void 0,m=!W(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,f=!!e.escapeParameterHtml,g=!M(e.sync)||e.sync;let _=e.messages;if(C(e.sharedMessages)){const t=e.sharedMessages;_=Object.keys(t).reduce(((e,a)=>{const n=e[a]||(e[a]={});return F(n,t[a]),e}),_||{})}const{__i18n:p,__root:v,__injectWithOption:b}=e,d=e.datetimeFormats,k=e.numberFormats;return{locale:t,fallbackLocale:a,messages:_,flatJson:e.flatJson,datetimeFormats:d,numberFormats:k,missing:n,missingWarn:l,fallbackWarn:r,fallbackRoot:o,fallbackFormat:s,modifiers:i,pluralRules:c,postTranslation:u,warnHtmlMessage:m,escapeParameter:f,messageResolver:e.messageResolver,inheritLocale:g,__i18n:p,__root:v,__injectWithOption:b}}(e)),a={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return M(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=M(e)?!e:e},get silentFallbackWarn(){return M(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=M(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[a,n,l]=e,r={};let o=null,s=null;if(!W(a))throw Error(Ie.INVALID_ARGUMENT);const i=a;return W(n)?r.locale=n:R(n)?o=n:C(n)&&(s=n),R(l)?o=l:C(l)&&(s=l),Reflect.apply(t.t,t,[i,o||s||{},r])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[a,n,l]=e,r={plural:1};let o=null,s=null;if(!W(a))throw Error(Ie.INVALID_ARGUMENT);const i=a;return W(n)?r.locale=n:k(n)?r.plural=n:R(n)?o=n:C(n)&&(s=n),W(l)?r.locale=l:R(l)?o=l:C(l)&&(s=l),Reflect.apply(t.t,t,[i,o||s||{},r])},te:(e,a)=>t.te(e,a),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,a){t.setLocaleMessage(e,a)},mergeLocaleMessage(e,a){t.mergeLocaleMessage(e,a)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,a){t.setDateTimeFormat(e,a)},mergeDateTimeFormat(e,a){t.mergeDateTimeFormat(e,a)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,a){t.setNumberFormat(e,a)},mergeNumberFormat(e,a){t.mergeNumberFormat(e,a)},getChoiceIndex:(e,t)=>-1,__onComponentInstanceCreated(t){const{componentInstanceCreatedListener:n}=e;n&&n(t,a)}};return a}}const Ge={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Be(e){return o}const Ye={name:"i18n-t",props:F({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>k(e)||!isNaN(e)}},Ge),setup(e,t){const{slots:a,attrs:n}=t,l=e.i18n||at({useScope:e.scope,__useComponent:!0});return()=>{const r=Object.keys(a).filter((e=>"_"!==e)),o={};e.locale&&(o.locale=e.locale),void 0!==e.plural&&(o.plural=W(e.plural)?+e.plural:e.plural);const i=function({slots:e},t){if(1===t.length&&"default"===t[0])return(e.default?e.default():[]).reduce(((e,t)=>[...e,...R(t.children)?t.children:[t]]),[]);return t.reduce(((t,a)=>{const n=e[a];return n&&(t[a]=n()),t}),{})}(t,r),c=l[We](e.keypath,i,o),u=F({},n),m=W(e.tag)||O(e.tag)?e.tag:Be();return s(m,u,c)}}};function Xe(e,t,a,n){const{slots:l,attrs:r}=t;return()=>{const t={part:!0};let o={};e.locale&&(t.locale=e.locale),W(e.format)?t.key=e.format:O(e.format)&&(W(e.format.key)&&(t.key=e.format.key),o=Object.keys(e.format).reduce(((t,n)=>a.includes(n)?F({},t,{[n]:e.format[n]}):t),{}));const i=n(e.value,t,o);let c=[t.key];R(i)?c=i.map(((e,t)=>{const a=l[e.type],n=a?a({[e.type]:e.value,index:t,parts:i}):[e.value];var r;return R(r=n)&&!W(r[0])&&(n[0].key=`${e.type}-${t}`),n})):W(i)&&(c=[i]);const u=F({},r),m=W(e.tag)||O(e.tag)?e.tag:Be();return s(m,u,c)}}const ze={name:"i18n-n",props:F({value:{type:Number,required:!0},format:{type:[String,Object]}},Ge),setup(e,t){const a=e.i18n||at({useScope:"parent",__useComponent:!0});return Xe(e,t,Le,((...e)=>a[Oe](...e)))}},Je={name:"i18n-d",props:F({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},Ge),setup(e,t){const a=e.i18n||at({useScope:"parent",__useComponent:!0});return Xe(e,t,de,((...e)=>a[Me](...e)))}};function qe(e){const t=t=>{const{instance:a,modifiers:n,value:l}=t;if(!a||!a.$)throw Error(Ie.UNEXPECTED_ERROR);const r=function(e,t){const a=e;if("composition"===e.mode)return a.__getInstance(t)||e.global;{const n=a.__getInstance(t);return null!=n?n.__composer:e.global.__composer}}(e,a.$),o=Ze(l);return[Reflect.apply(r.t,r,[...Qe(o)]),r]};return{created:(a,n)=>{const[r,o]=t(n);p&&e.global===o&&(a.__i18nWatcher=l(o.locale,(()=>{n.instance&&n.instance.$forceUpdate()}))),a.__composer=o,a.textContent=r},unmounted:e=>{p&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const a=e.__composer,n=Ze(t);e.textContent=Reflect.apply(a.t,a,[...Qe(n)])}},getSSRProps:e=>{const[a]=t(e);return{textContent:a}}}}function Ze(e){if(W(e))return{path:e};if(C(e)){if(!("path"in e))throw Error(Ie.REQUIRED_VALUE,"path");return e}throw Error(Ie.INVALID_VALUE)}function Qe(e){const{path:t,locale:a,args:n,choice:l,plural:r}=e,o={},s=n||{};return W(a)&&(o.locale=a),k(l)&&(o.plural=l),k(r)&&(o.plural=r),[t,s,o]}function Ke(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[we](t.pluralizationRules||e.pluralizationRules);const a=Pe(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(a).forEach((t=>e.mergeLocaleMessage(t,a[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((a=>e.mergeDateTimeFormat(a,t.datetimeFormats[a]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((a=>e.mergeNumberFormat(a,t.numberFormats[a]))),e}const et=b("global-vue-i18n");function tt(e={},t){const a=!M(e.legacy)||e.legacy,n=!M(e.globalInjection)||e.globalInjection,l=!a||!!e.allowComposition,o=new Map,[s,c]=function(e,t,a){const n=i();{const a=t?n.run((()=>xe(e))):n.run((()=>Ve(e)));if(null==a)throw Error(Ie.UNEXPECTED_ERROR);return[n,a]}}(e,a),u=b("");{const e={get mode(){return a?"legacy":"composition"},get allowComposition(){return l},async install(t,...l){t.__VUE_I18N_SYMBOL__=u,t.provide(t.__VUE_I18N_SYMBOL__,e),!a&&n&&function(e,t){const a=Object.create(null);lt.forEach((e=>{const n=Object.getOwnPropertyDescriptor(t,e);if(!n)throw Error(Ie.UNEXPECTED_ERROR);const l=_(n.value)?{get:()=>n.value.value,set(e){n.value.value=e}}:{get:()=>n.get&&n.get()};Object.defineProperty(a,e,l)})),e.config.globalProperties.$i18n=a,rt.forEach((a=>{const n=Object.getOwnPropertyDescriptor(t,a);if(!n||!n.value)throw Error(Ie.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${a}`,n)}))}(t,e.global),function(e,t,...a){const n=C(a[0])?a[0]:{},l=!!n.useI18nComponentName;(!M(n.globalInstall)||n.globalInstall)&&(e.component(l?"i18n":Ye.name,Ye),e.component(ze.name,ze),e.component(Je.name,Je)),e.directive("t",qe(t))}(t,e,...l),a&&t.mixin(function(e,t,a){return{beforeCreate(){const n=r();if(!n)throw Error(Ie.UNEXPECTED_ERROR);const l=this.$options;if(l.i18n){const a=l.i18n;l.__i18n&&(a.__i18n=l.__i18n),a.__root=t,this===this.$root?this.$i18n=Ke(e,a):(a.__injectWithOption=!0,this.$i18n=xe(a))}else l.__i18n?this===this.$root?this.$i18n=Ke(e,l):this.$i18n=xe({__i18n:l.__i18n,__injectWithOption:!0,__root:t}):this.$i18n=e;l.__i18nGlobal&&$e(t,l,l),e.__onComponentInstanceCreated(this.$i18n),a.__setInstance(n,this.$i18n),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e)},mounted(){},unmounted(){const e=r();if(!e)throw Error(Ie.UNEXPECTED_ERROR);delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,a.__deleteInstance(e),delete this.$i18n}}}(c,c.__composer,e));const o=t.unmount;t.unmount=()=>{e.dispose(),o()}},get global(){return c},dispose(){s.stop()},__instances:o,__getInstance:function(e){return o.get(e)||null},__setInstance:function(e,t){o.set(e,t)},__deleteInstance:function(e){o.delete(e)}};return e}}function at(e={}){const t=r();if(null==t)throw Error(Ie.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&null!=t.appContext.app&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw Error(Ie.NOT_INSLALLED);const l=function(e){{const t=c(e.isCE?et:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw function(e,...t){return A(e,null,void 0)}(e.isCE?Ie.NOT_INSLALLED_WITH_PROVIDE:Ie.UNEXPECTED_ERROR);return t}}(t),o=function(e){return"composition"===e.mode?e.global:e.global.__composer}(l),s=function(e){return e.type}(t),i=function(e,t){return E(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,s);if("legacy"===l.mode&&!e.__useComponent){if(!l.allowComposition)throw Error(Ie.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,t,l,r={}){const o="local"===t,s=f(null);if(o&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(Ie.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const i=!M(r.inheritLocale)||r.inheritLocale,c=a(o&&i?l.locale.value:W(r.locale)?r.locale:ee),u=a(o&&i?l.fallbackLocale.value:W(r.fallbackLocale)||R(r.fallbackLocale)||C(r.fallbackLocale)||!1===r.fallbackLocale?r.fallbackLocale:c.value),m=a(Pe(c.value,r)),_=a(C(r.datetimeFormats)?r.datetimeFormats:{[c.value]:{}}),p=a(C(r.numberFormats)?r.numberFormats:{[c.value]:{}}),v=o?l.missingWarn:!M(r.missingWarn)&&!h(r.missingWarn)||r.missingWarn,b=o?l.fallbackWarn:!M(r.fallbackWarn)&&!h(r.fallbackWarn)||r.fallbackWarn,d=o?l.fallbackRoot:!M(r.fallbackRoot)||r.fallbackRoot,k=!!r.fallbackFormat,E=I(r.missing)?r.missing:null,L=I(r.postTranslation)?r.postTranslation:null,F=o?l.warnHtmlMessage:!M(r.warnHtmlMessage)||r.warnHtmlMessage,y=!!r.escapeParameter,T=o?l.modifiers:C(r.modifiers)?r.modifiers:{},N=r.pluralRules||o&&l.pluralRules;function O(){return[c.value,u.value,m.value,_.value,p.value]}const w=n({get:()=>s.value?s.value.locale.value:c.value,set:e=>{s.value&&(s.value.locale.value=e),c.value=e}}),D=n({get:()=>s.value?s.value.fallbackLocale.value:u.value,set:e=>{s.value&&(s.value.fallbackLocale.value=e),u.value=e}}),P=n((()=>s.value?s.value.messages.value:m.value)),A=n((()=>_.value)),S=n((()=>p.value));function $(){return s.value?s.value.getPostTranslationHandler():L}function U(e){s.value&&s.value.setPostTranslationHandler(e)}function H(){return s.value?s.value.getMissingHandler():E}function j(e){s.value&&s.value.setMissingHandler(e)}function V(e){return O(),e()}function x(...e){return s.value?V((()=>Reflect.apply(s.value.t,null,[...e]))):V((()=>""))}function G(...e){return s.value?Reflect.apply(s.value.rt,null,[...e]):""}function B(...e){return s.value?V((()=>Reflect.apply(s.value.d,null,[...e]))):V((()=>""))}function Y(...e){return s.value?V((()=>Reflect.apply(s.value.n,null,[...e]))):V((()=>""))}function X(e){return s.value?s.value.tm(e):{}}function z(e,t){return!!s.value&&s.value.te(e,t)}function J(e){return s.value?s.value.getLocaleMessage(e):{}}function q(e,t){s.value&&(s.value.setLocaleMessage(e,t),m.value[e]=t)}function Z(e,t){s.value&&s.value.mergeLocaleMessage(e,t)}function Q(e){return s.value?s.value.getDateTimeFormat(e):{}}function K(e,t){s.value&&(s.value.setDateTimeFormat(e,t),_.value[e]=t)}function te(e,t){s.value&&s.value.mergeDateTimeFormat(e,t)}function ae(e){return s.value?s.value.getNumberFormat(e):{}}function ne(e,t){s.value&&(s.value.setNumberFormat(e,t),p.value[e]=t)}function le(e,t){s.value&&s.value.mergeNumberFormat(e,t)}const re={get id(){return s.value?s.value.id:-1},locale:w,fallbackLocale:D,messages:P,datetimeFormats:A,numberFormats:S,get inheritLocale(){return s.value?s.value.inheritLocale:i},set inheritLocale(e){s.value&&(s.value.inheritLocale=e)},get availableLocales(){return s.value?s.value.availableLocales:Object.keys(m.value)},get modifiers(){return s.value?s.value.modifiers:T},get pluralRules(){return s.value?s.value.pluralRules:N},get isGlobal(){return!!s.value&&s.value.isGlobal},get missingWarn(){return s.value?s.value.missingWarn:v},set missingWarn(e){s.value&&(s.value.missingWarn=e)},get fallbackWarn(){return s.value?s.value.fallbackWarn:b},set fallbackWarn(e){s.value&&(s.value.missingWarn=e)},get fallbackRoot(){return s.value?s.value.fallbackRoot:d},set fallbackRoot(e){s.value&&(s.value.fallbackRoot=e)},get fallbackFormat(){return s.value?s.value.fallbackFormat:k},set fallbackFormat(e){s.value&&(s.value.fallbackFormat=e)},get warnHtmlMessage(){return s.value?s.value.warnHtmlMessage:F},set warnHtmlMessage(e){s.value&&(s.value.warnHtmlMessage=e)},get escapeParameter(){return s.value?s.value.escapeParameter:y},set escapeParameter(e){s.value&&(s.value.escapeParameter=e)},t:x,getPostTranslationHandler:$,setPostTranslationHandler:U,getMissingHandler:H,setMissingHandler:j,rt:G,d:B,n:Y,tm:X,te:z,getLocaleMessage:J,setLocaleMessage:q,mergeLocaleMessage:Z,getDateTimeFormat:Q,setDateTimeFormat:K,mergeDateTimeFormat:te,getNumberFormat:ae,setNumberFormat:ne,mergeNumberFormat:le};function oe(e){e.locale.value=c.value,e.fallbackLocale.value=u.value,Object.keys(m.value).forEach((t=>{e.mergeLocaleMessage(t,m.value[t])})),Object.keys(_.value).forEach((t=>{e.mergeDateTimeFormat(t,_.value[t])})),Object.keys(p.value).forEach((t=>{e.mergeNumberFormat(t,p.value[t])})),e.escapeParameter=y,e.fallbackFormat=k,e.fallbackRoot=d,e.fallbackWarn=b,e.missingWarn=v,e.warnHtmlMessage=F}return g((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(Ie.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const a=s.value=e.proxy.$i18n.__composer;"global"===t?(c.value=a.locale.value,u.value=a.fallbackLocale.value,m.value=a.messages.value,_.value=a.datetimeFormats.value,p.value=a.numberFormats.value):o&&oe(a)})),re}(t,i,o,e)}if("global"===i)return $e(o,e,s),o;if("parent"===i){let a=function(e,t,a=!1){let n=null;const l=t.root;let r=t.parent;for(;null!=r;){const t=e;if("composition"===e.mode)n=t.__getInstance(r);else{const e=t.__getInstance(r);null!=e&&(n=e.__composer,a&&n&&!n[De]&&(n=null))}if(null!=n)break;if(l===r)break;r=r.parent}return n}(l,t,e.__useComponent);return null==a&&(a=o),a}const _=l;let p=_.__getInstance(t);if(null==p){const a=F({},e);"__i18n"in s&&(a.__i18n=s.__i18n),o&&(a.__root=o),p=Ve(a),function(e,t,a){u((()=>{}),t),m((()=>{e.__deleteInstance(t)}),t)}(_,t),_.__setInstance(t,p)}return p}const nt=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(Ie.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e};const lt=["locale","fallbackLocale","availableLocales"],rt=["t","rt","d","n","tm"];ae=function(e,t){if(!O(e))return null;let a=j.get(t);if(a||(a=function(e){const t=[];let a,n,l,r,o,s,i,c=-1,u=0,m=0;const f=[];function g(){const t=e[c+1];if(5===u&&"'"===t||6===u&&'"'===t)return c++,l="\\"+t,f[0](),!0}for(f[0]=()=>{void 0===n?n=l:n+=l},f[1]=()=>{void 0!==n&&(t.push(n),n=void 0)},f[2]=()=>{f[0](),m++},f[3]=()=>{if(m>0)m--,u=4,f[0]();else{if(m=0,void 0===n)return!1;if(n=H(n),!1===n)return!1;f[1]()}};null!==u;)if(c++,a=e[c],"\\"!==a||!g()){if(r=U(a),i=S[u],o=i[r]||i.l||8,8===o)return;if(u=o[0],void 0!==o[1]&&(s=f[o[1]],s&&(l=a,!1===s())))return;if(7===u)return t}}(t),a&&j.set(t,a)),!a)return null;const n=a.length;let l=e,r=0;for(;r<n;){const e=l[a[r]];if(void 0===e)return null;l=e,r++}return l},ne=q;export{Je as DatetimeFormat,et as I18nInjectionKey,ze as NumberFormat,Ye as Translation,Te as VERSION,nt as castToVueI18n,tt as createI18n,at as useI18n,qe as vTDirective};
