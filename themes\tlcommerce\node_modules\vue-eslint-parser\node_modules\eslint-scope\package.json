{"_args": [["eslint-scope@7.1.1", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "eslint-scope@7.1.1", "_id": "eslint-scope@7.1.1", "_inBundle": false, "_integrity": "sha512-QKQM/UXpIiHcLqJ5AOyIW7XZmzjkzQXYE54n1++wb0u9V/abW3l9uQnxX8Z5Xd18xyKIMTUAyQ0k1e8pz6LUrw==", "_location": "/vue-eslint-parser/eslint-scope", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "eslint-scope@7.1.1", "name": "eslint-scope", "escapedName": "eslint-scope", "rawSpec": "7.1.1", "saveSpec": null, "fetchSpec": "7.1.1"}, "_requiredBy": ["/vue-eslint-parser"], "_resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.1.1.tgz", "_spec": "7.1.1", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "bugs": {"url": "https://github.com/eslint/eslint-scope/issues"}, "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "description": "ECMAScript scope analyzer for ESLint", "devDependencies": {"@typescript-eslint/parser": "^4.28.1", "c8": "^7.7.3", "chai": "^4.3.4", "eslint": "^7.29.0", "eslint-config-eslint": "^7.0.0", "eslint-plugin-jsdoc": "^35.4.1", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "mocha": "^9.0.1", "npm-license": "^0.3.3", "rollup": "^2.52.7", "shelljs": "^0.8.4", "typescript": "^4.3.5"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "exports": {".": {"import": "./lib/index.js", "require": "./dist/eslint-scope.cjs"}, "./package.json": "./package.json"}, "files": ["LICENSE", "README.md", "lib", "dist/eslint-scope.cjs"], "homepage": "http://github.com/eslint/eslint-scope", "license": "BSD-2-<PERSON><PERSON>", "main": "./dist/eslint-scope.cjs", "name": "eslint-scope", "repository": {"type": "git", "url": "git+https://github.com/eslint/eslint-scope.git"}, "scripts": {"build": "rollup -c", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "generate-release": "eslint-generate-release", "lint": "npm run build && node Makefile.js lint", "prepublishOnly": "npm run update-version && npm run build", "publish-release": "eslint-publish-release", "test": "npm run build && node Makefile.js test", "update-version": "node tools/update-version.js"}, "type": "module", "version": "7.1.1"}