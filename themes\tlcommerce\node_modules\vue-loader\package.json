{"_args": [["vue-loader@16.8.3", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "vue-loader@16.8.3", "_id": "vue-loader@16.8.3", "_inBundle": false, "_integrity": "sha512-7vKN45IxsKxe5GcVCbc2qFU5aWzyiLrYJyUuMz4BQLKctCj/fmCa0w6fGiiQ2cLFetNcek1ppGJQDCup0c1hpA==", "_location": "/vue-loader", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "vue-loader@16.8.3", "name": "vue-loader", "escapedName": "vue-loader", "rawSpec": "16.8.3", "saveSpec": null, "fetchSpec": "16.8.3"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/vue-loader/-/vue-loader-16.8.3.tgz", "_spec": "16.8.3", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "dependencies": {"chalk": "^4.1.0", "hash-sum": "^2.0.0", "loader-utils": "^2.0.0"}, "description": "> webpack loader for Vue Single-File Components", "devDependencies": {"@babel/core": "^7.7.7", "@babel/preset-env": "^7.11.5", "@intlify/vue-i18n-loader": "^3.0.0", "@types/estree": "^0.0.45", "@types/hash-sum": "^1.0.0", "@types/jest": "^26.0.13", "@types/jsdom": "^16.2.13", "@types/loader-utils": "^2.0.1", "@types/mini-css-extract-plugin": "^0.9.1", "@types/webpack": "^4.41.0", "@types/webpack-merge": "^4.1.5", "@vue/compiler-sfc": "^3.2.12", "babel-loader": "^8.1.0", "cache-loader": "^4.1.0", "conventional-changelog-cli": "^2.1.1", "css-loader": "^4.3.0", "file-loader": "^6.1.0", "html-webpack-plugin": "^4.5.0", "html-webpack-plugin-v5": "npm:html-webpack-plugin@^5.3.2", "jest": "^26.4.1", "jsdom": "^16.4.0", "lint-staged": "^10.3.0", "markdown-loader": "^6.0.0", "memfs": "^3.1.2", "mini-css-extract-plugin": "^0.11.2", "normalize-newline": "^3.0.0", "null-loader": "^4.0.1", "postcss-loader": "^4.0.4", "prettier": "^2.1.1", "pug": "^2.0.0", "pug-plain-loader": "^1.0.0", "source-map": "^0.6.1", "style-loader": "^2.0.0", "stylus": "^0.54.7", "stylus-loader": "^4.1.1", "sugarss": "^3.0.1", "ts-jest": "^26.2.0", "ts-loader": "^8.0.6", "ts-loader-v9": "npm:ts-loader@^9.2.4", "typescript": "^4.4.3", "url-loader": "^4.1.0", "vue": "^3.2.13", "vue-i18n": "^9.1.7", "webpack": "^4.41.2", "webpack-cli": "^3.3.10", "webpack-dev-server": "^3.9.0", "webpack-merge": "^5.1.4", "webpack5": "npm:webpack@5", "yorkie": "^2.0.0"}, "files": ["dist"], "gitHooks": {"pre-commit": "lint-staged"}, "license": "MIT", "lint-staged": {"*.js": ["prettier --write"], "*.ts": ["prettier --parser=typescript --write"]}, "main": "dist/index.js", "name": "vue-loader", "peerDependencies": {"webpack": "^4.1.0 || ^5.0.0-0"}, "scripts": {"build": "tsc", "build-example": "rm -rf example/dist && webpack --config example/webpack.config.js --env.prod", "build-example-ssr": "rm -rf example/dist-ssr && webpack --config example/webpack.config.js --env.prod --env.ssr && node example/ssr.js", "dev": "tsc --watch", "dev-example": "webpack-dev-server --config example/webpack.config.js --inline --hot", "lint": "prettier --write --parser typescript \"{src,test}/**/*.{j,t}s\"", "prepublishOnly": "tsc && conventional-changelog -p angular -i CHANGELOG.md -s -r 2", "pretest": "tsc", "test": "jest --coverage", "test:webpack5": "WEBPACK5=true jest -c --coverage"}, "types": "dist/index.d.ts", "version": "16.8.3"}