{"_args": [["vue@3.2.36", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_from": "vue@3.2.36", "_id": "vue@3.2.36", "_inBundle": false, "_integrity": "sha512-5yTXmrE6gW8IQgttzHW5bfBiFA6mx35ZXHjGLDmKYzW6MMmYvCwuKybANRepwkMYeXw2v1buGg3/lPICY5YlZw==", "_location": "/vue", "_phantomChildren": {"@babel/parser": "7.21.2", "estree-walker": "2.0.2", "source-map": "0.6.1"}, "_requested": {"type": "version", "registry": true, "raw": "vue@3.2.36", "name": "vue", "escapedName": "vue", "rawSpec": "3.2.36", "saveSpec": null, "fetchSpec": "3.2.36"}, "_requiredBy": ["#DEV:/", "/v-pagination-3", "/vue3-runtime-template"], "_resolved": "https://registry.npmjs.org/vue/-/vue-3.2.36.tgz", "_spec": "3.2.36", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "<PERSON><PERSON>", "formats": ["esm-bundler", "esm-bundler-runtime", "cjs", "global", "global-runtime", "esm-browser", "esm-browser-runtime"]}, "dependencies": {"@vue/compiler-dom": "3.2.36", "@vue/compiler-sfc": "3.2.36", "@vue/runtime-dom": "3.2.36", "@vue/server-renderer": "3.2.36", "@vue/shared": "3.2.36"}, "description": "The progressive JavaScript framework for building modern web UI.", "exports": {".": {"import": {"node": "./index.mjs", "default": "./dist/vue.runtime.esm-bundler.js"}, "require": "./index.js", "types": "./dist/vue.d.ts"}, "./server-renderer": {"import": "./server-renderer/index.mjs", "require": "./server-renderer/index.js"}, "./compiler-sfc": {"import": "./compiler-sfc/index.mjs", "require": "./compiler-sfc/index.js"}, "./dist/*": "./dist/*", "./package.json": "./package.json", "./macros": "./macros.d.ts", "./macros-global": "./macros-global.d.ts", "./ref-macros": "./ref-macros.d.ts"}, "files": ["index.js", "index.mjs", "dist", "compiler-sfc", "server-renderer", "macros.d.ts", "macros-global.d.ts", "ref-macros.d.ts"], "homepage": "https://github.com/vuejs/core/tree/main/packages/vue#readme", "jsdelivr": "dist/vue.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/vue.runtime.esm-bundler.js", "name": "vue", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git"}, "types": "dist/vue.d.ts", "unpkg": "dist/vue.global.js", "version": "3.2.36"}