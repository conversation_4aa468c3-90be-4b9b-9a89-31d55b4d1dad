{"version": 3, "file": "esquery.lite.min.js", "sources": ["../parser.js", "../esquery.js"], "sourcesContent": ["/*\n * Generated by PEG.js 0.10.0.\n *\n * http://pegjs.org/\n */\n(function(root, factory) {\n  if (typeof define === \"function\" && define.amd) {\n    define([], factory);\n  } else if (typeof module === \"object\" && module.exports) {\n    module.exports = factory();\n  }\n})(this, function() {\n  \"use strict\";\n\n  function peg$subclass(child, parent) {\n    function ctor() { this.constructor = child; }\n    ctor.prototype = parent.prototype;\n    child.prototype = new ctor();\n  }\n\n  function peg$SyntaxError(message, expected, found, location) {\n    this.message  = message;\n    this.expected = expected;\n    this.found    = found;\n    this.location = location;\n    this.name     = \"SyntaxError\";\n\n    if (typeof Error.captureStackTrace === \"function\") {\n      Error.captureStackTrace(this, peg$SyntaxError);\n    }\n  }\n\n  peg$subclass(peg$SyntaxError, Error);\n\n  peg$SyntaxError.buildMessage = function(expected, found) {\n    var DESCRIBE_EXPECTATION_FNS = {\n          literal: function(expectation) {\n            return \"\\\"\" + literalEscape(expectation.text) + \"\\\"\";\n          },\n\n          \"class\": function(expectation) {\n            var escapedParts = \"\",\n                i;\n\n            for (i = 0; i < expectation.parts.length; i++) {\n              escapedParts += expectation.parts[i] instanceof Array\n                ? classEscape(expectation.parts[i][0]) + \"-\" + classEscape(expectation.parts[i][1])\n                : classEscape(expectation.parts[i]);\n            }\n\n            return \"[\" + (expectation.inverted ? \"^\" : \"\") + escapedParts + \"]\";\n          },\n\n          any: function(expectation) {\n            return \"any character\";\n          },\n\n          end: function(expectation) {\n            return \"end of input\";\n          },\n\n          other: function(expectation) {\n            return expectation.description;\n          }\n        };\n\n    function hex(ch) {\n      return ch.charCodeAt(0).toString(16).toUpperCase();\n    }\n\n    function literalEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\"/g,  '\\\\\"')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function classEscape(s) {\n      return s\n        .replace(/\\\\/g, '\\\\\\\\')\n        .replace(/\\]/g, '\\\\]')\n        .replace(/\\^/g, '\\\\^')\n        .replace(/-/g,  '\\\\-')\n        .replace(/\\0/g, '\\\\0')\n        .replace(/\\t/g, '\\\\t')\n        .replace(/\\n/g, '\\\\n')\n        .replace(/\\r/g, '\\\\r')\n        .replace(/[\\x00-\\x0F]/g,          function(ch) { return '\\\\x0' + hex(ch); })\n        .replace(/[\\x10-\\x1F\\x7F-\\x9F]/g, function(ch) { return '\\\\x'  + hex(ch); });\n    }\n\n    function describeExpectation(expectation) {\n      return DESCRIBE_EXPECTATION_FNS[expectation.type](expectation);\n    }\n\n    function describeExpected(expected) {\n      var descriptions = new Array(expected.length),\n          i, j;\n\n      for (i = 0; i < expected.length; i++) {\n        descriptions[i] = describeExpectation(expected[i]);\n      }\n\n      descriptions.sort();\n\n      if (descriptions.length > 0) {\n        for (i = 1, j = 1; i < descriptions.length; i++) {\n          if (descriptions[i - 1] !== descriptions[i]) {\n            descriptions[j] = descriptions[i];\n            j++;\n          }\n        }\n        descriptions.length = j;\n      }\n\n      switch (descriptions.length) {\n        case 1:\n          return descriptions[0];\n\n        case 2:\n          return descriptions[0] + \" or \" + descriptions[1];\n\n        default:\n          return descriptions.slice(0, -1).join(\", \")\n            + \", or \"\n            + descriptions[descriptions.length - 1];\n      }\n    }\n\n    function describeFound(found) {\n      return found ? \"\\\"\" + literalEscape(found) + \"\\\"\" : \"end of input\";\n    }\n\n    return \"Expected \" + describeExpected(expected) + \" but \" + describeFound(found) + \" found.\";\n  };\n\n  function peg$parse(input, options) {\n    options = options !== void 0 ? options : {};\n\n    var peg$FAILED = {},\n\n        peg$startRuleFunctions = { start: peg$parsestart },\n        peg$startRuleFunction  = peg$parsestart,\n\n        peg$c0 = function(ss) {\n            return ss.length === 1 ? ss[0] : { type: 'matches', selectors: ss };\n          },\n        peg$c1 = function() { return void 0; },\n        peg$c2 = \" \",\n        peg$c3 = peg$literalExpectation(\" \", false),\n        peg$c4 = /^[^ [\\],():#!=><~+.]/,\n        peg$c5 = peg$classExpectation([\" \", \"[\", \"]\", \",\", \"(\", \")\", \":\", \"#\", \"!\", \"=\", \">\", \"<\", \"~\", \"+\", \".\"], true, false),\n        peg$c6 = function(i) { return i.join(''); },\n        peg$c7 = \">\",\n        peg$c8 = peg$literalExpectation(\">\", false),\n        peg$c9 = function() { return 'child'; },\n        peg$c10 = \"~\",\n        peg$c11 = peg$literalExpectation(\"~\", false),\n        peg$c12 = function() { return 'sibling'; },\n        peg$c13 = \"+\",\n        peg$c14 = peg$literalExpectation(\"+\", false),\n        peg$c15 = function() { return 'adjacent'; },\n        peg$c16 = function() { return 'descendant'; },\n        peg$c17 = \",\",\n        peg$c18 = peg$literalExpectation(\",\", false),\n        peg$c19 = function(s, ss) {\n          return [s].concat(ss.map(function (s) { return s[3]; }));\n        },\n        peg$c20 = function(a, ops) {\n            return ops.reduce(function (memo, rhs) {\n              return { type: rhs[0], left: memo, right: rhs[1] };\n            }, a);\n          },\n        peg$c21 = \"!\",\n        peg$c22 = peg$literalExpectation(\"!\", false),\n        peg$c23 = function(subject, as) {\n            const b = as.length === 1 ? as[0] : { type: 'compound', selectors: as };\n            if(subject) b.subject = true;\n            return b;\n          },\n        peg$c24 = \"*\",\n        peg$c25 = peg$literalExpectation(\"*\", false),\n        peg$c26 = function(a) { return { type: 'wildcard', value: a }; },\n        peg$c27 = \"#\",\n        peg$c28 = peg$literalExpectation(\"#\", false),\n        peg$c29 = function(i) { return { type: 'identifier', value: i }; },\n        peg$c30 = \"[\",\n        peg$c31 = peg$literalExpectation(\"[\", false),\n        peg$c32 = \"]\",\n        peg$c33 = peg$literalExpectation(\"]\", false),\n        peg$c34 = function(v) { return v; },\n        peg$c35 = /^[><!]/,\n        peg$c36 = peg$classExpectation([\">\", \"<\", \"!\"], false, false),\n        peg$c37 = \"=\",\n        peg$c38 = peg$literalExpectation(\"=\", false),\n        peg$c39 = function(a) { return (a || '') + '='; },\n        peg$c40 = /^[><]/,\n        peg$c41 = peg$classExpectation([\">\", \"<\"], false, false),\n        peg$c42 = \".\",\n        peg$c43 = peg$literalExpectation(\".\", false),\n        peg$c44 = function(a, as) {\n            return [].concat.apply([a], as).join('');\n          },\n        peg$c45 = function(name, op, value) {\n              return { type: 'attribute', name: name, operator: op, value: value };\n            },\n        peg$c46 = function(name) { return { type: 'attribute', name: name }; },\n        peg$c47 = \"\\\"\",\n        peg$c48 = peg$literalExpectation(\"\\\"\", false),\n        peg$c49 = /^[^\\\\\"]/,\n        peg$c50 = peg$classExpectation([\"\\\\\", \"\\\"\"], true, false),\n        peg$c51 = \"\\\\\",\n        peg$c52 = peg$literalExpectation(\"\\\\\", false),\n        peg$c53 = peg$anyExpectation(),\n        peg$c54 = function(a, b) { return a + b; },\n        peg$c55 = function(d) {\n                return { type: 'literal', value: strUnescape(d.join('')) };\n              },\n        peg$c56 = \"'\",\n        peg$c57 = peg$literalExpectation(\"'\", false),\n        peg$c58 = /^[^\\\\']/,\n        peg$c59 = peg$classExpectation([\"\\\\\", \"'\"], true, false),\n        peg$c60 = /^[0-9]/,\n        peg$c61 = peg$classExpectation([[\"0\", \"9\"]], false, false),\n        peg$c62 = function(a, b) {\n                // Can use `a.flat().join('')` once supported\n                const leadingDecimals = a ? [].concat.apply([], a).join('') : '';\n                return { type: 'literal', value: parseFloat(leadingDecimals + b.join('')) };\n              },\n        peg$c63 = function(i) { return { type: 'literal', value: i }; },\n        peg$c64 = \"type(\",\n        peg$c65 = peg$literalExpectation(\"type(\", false),\n        peg$c66 = /^[^ )]/,\n        peg$c67 = peg$classExpectation([\" \", \")\"], true, false),\n        peg$c68 = \")\",\n        peg$c69 = peg$literalExpectation(\")\", false),\n        peg$c70 = function(t) { return { type: 'type', value: t.join('') }; },\n        peg$c71 = /^[imsu]/,\n        peg$c72 = peg$classExpectation([\"i\", \"m\", \"s\", \"u\"], false, false),\n        peg$c73 = \"/\",\n        peg$c74 = peg$literalExpectation(\"/\", false),\n        peg$c75 = /^[^\\/]/,\n        peg$c76 = peg$classExpectation([\"/\"], true, false),\n        peg$c77 = function(d, flgs) { return {\n              type: 'regexp', value: new RegExp(d.join(''), flgs ? flgs.join('') : '') };\n            },\n        peg$c78 = function(i, is) {\n          return { type: 'field', name: is.reduce(function(memo, p){ return memo + p[0] + p[1]; }, i)};\n        },\n        peg$c79 = \":not(\",\n        peg$c80 = peg$literalExpectation(\":not(\", false),\n        peg$c81 = function(ss) { return { type: 'not', selectors: ss }; },\n        peg$c82 = \":matches(\",\n        peg$c83 = peg$literalExpectation(\":matches(\", false),\n        peg$c84 = function(ss) { return { type: 'matches', selectors: ss }; },\n        peg$c85 = \":has(\",\n        peg$c86 = peg$literalExpectation(\":has(\", false),\n        peg$c87 = function(ss) { return { type: 'has', selectors: ss }; },\n        peg$c88 = \":first-child\",\n        peg$c89 = peg$literalExpectation(\":first-child\", false),\n        peg$c90 = function() { return nth(1); },\n        peg$c91 = \":last-child\",\n        peg$c92 = peg$literalExpectation(\":last-child\", false),\n        peg$c93 = function() { return nthLast(1); },\n        peg$c94 = \":nth-child(\",\n        peg$c95 = peg$literalExpectation(\":nth-child(\", false),\n        peg$c96 = function(n) { return nth(parseInt(n.join(''), 10)); },\n        peg$c97 = \":nth-last-child(\",\n        peg$c98 = peg$literalExpectation(\":nth-last-child(\", false),\n        peg$c99 = function(n) { return nthLast(parseInt(n.join(''), 10)); },\n        peg$c100 = \":\",\n        peg$c101 = peg$literalExpectation(\":\", false),\n        peg$c102 = function(c) {\n          return { type: 'class', name: c };\n        },\n\n        peg$currPos          = 0,\n        peg$savedPos         = 0,\n        peg$posDetailsCache  = [{ line: 1, column: 1 }],\n        peg$maxFailPos       = 0,\n        peg$maxFailExpected  = [],\n        peg$silentFails      = 0,\n\n        peg$resultsCache = {},\n\n        peg$result;\n\n    if (\"startRule\" in options) {\n      if (!(options.startRule in peg$startRuleFunctions)) {\n        throw new Error(\"Can't start parsing from rule \\\"\" + options.startRule + \"\\\".\");\n      }\n\n      peg$startRuleFunction = peg$startRuleFunctions[options.startRule];\n    }\n\n    function text() {\n      return input.substring(peg$savedPos, peg$currPos);\n    }\n\n    function location() {\n      return peg$computeLocation(peg$savedPos, peg$currPos);\n    }\n\n    function expected(description, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildStructuredError(\n        [peg$otherExpectation(description)],\n        input.substring(peg$savedPos, peg$currPos),\n        location\n      );\n    }\n\n    function error(message, location) {\n      location = location !== void 0 ? location : peg$computeLocation(peg$savedPos, peg$currPos)\n\n      throw peg$buildSimpleError(message, location);\n    }\n\n    function peg$literalExpectation(text, ignoreCase) {\n      return { type: \"literal\", text: text, ignoreCase: ignoreCase };\n    }\n\n    function peg$classExpectation(parts, inverted, ignoreCase) {\n      return { type: \"class\", parts: parts, inverted: inverted, ignoreCase: ignoreCase };\n    }\n\n    function peg$anyExpectation() {\n      return { type: \"any\" };\n    }\n\n    function peg$endExpectation() {\n      return { type: \"end\" };\n    }\n\n    function peg$otherExpectation(description) {\n      return { type: \"other\", description: description };\n    }\n\n    function peg$computePosDetails(pos) {\n      var details = peg$posDetailsCache[pos], p;\n\n      if (details) {\n        return details;\n      } else {\n        p = pos - 1;\n        while (!peg$posDetailsCache[p]) {\n          p--;\n        }\n\n        details = peg$posDetailsCache[p];\n        details = {\n          line:   details.line,\n          column: details.column\n        };\n\n        while (p < pos) {\n          if (input.charCodeAt(p) === 10) {\n            details.line++;\n            details.column = 1;\n          } else {\n            details.column++;\n          }\n\n          p++;\n        }\n\n        peg$posDetailsCache[pos] = details;\n        return details;\n      }\n    }\n\n    function peg$computeLocation(startPos, endPos) {\n      var startPosDetails = peg$computePosDetails(startPos),\n          endPosDetails   = peg$computePosDetails(endPos);\n\n      return {\n        start: {\n          offset: startPos,\n          line:   startPosDetails.line,\n          column: startPosDetails.column\n        },\n        end: {\n          offset: endPos,\n          line:   endPosDetails.line,\n          column: endPosDetails.column\n        }\n      };\n    }\n\n    function peg$fail(expected) {\n      if (peg$currPos < peg$maxFailPos) { return; }\n\n      if (peg$currPos > peg$maxFailPos) {\n        peg$maxFailPos = peg$currPos;\n        peg$maxFailExpected = [];\n      }\n\n      peg$maxFailExpected.push(expected);\n    }\n\n    function peg$buildSimpleError(message, location) {\n      return new peg$SyntaxError(message, null, null, location);\n    }\n\n    function peg$buildStructuredError(expected, found, location) {\n      return new peg$SyntaxError(\n        peg$SyntaxError.buildMessage(expected, found),\n        expected,\n        found,\n        location\n      );\n    }\n\n    function peg$parsestart() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 0,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseselectors();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c0(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c1();\n        }\n        s0 = s1;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parse_() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 1,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (input.charCodeAt(peg$currPos) === 32) {\n        s1 = peg$c2;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c3); }\n      }\n      while (s1 !== peg$FAILED) {\n        s0.push(s1);\n        if (input.charCodeAt(peg$currPos) === 32) {\n          s1 = peg$c2;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c3); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifierName() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 2,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = [];\n      if (peg$c4.test(input.charAt(peg$currPos))) {\n        s2 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s2 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c5); }\n      }\n      if (s2 !== peg$FAILED) {\n        while (s2 !== peg$FAILED) {\n          s1.push(s2);\n          if (peg$c4.test(input.charAt(peg$currPos))) {\n            s2 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c5); }\n          }\n        }\n      } else {\n        s1 = peg$FAILED;\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c6(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsebinaryOp() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 3,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parse_();\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 62) {\n          s2 = peg$c7;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c8); }\n        }\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parse_();\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c9();\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parse_();\n        if (s1 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 126) {\n            s2 = peg$c10;\n            peg$currPos++;\n          } else {\n            s2 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c11); }\n          }\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parse_();\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c12();\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parse_();\n          if (s1 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 43) {\n              s2 = peg$c13;\n              peg$currPos++;\n            } else {\n              s2 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c14); }\n            }\n            if (s2 !== peg$FAILED) {\n              s3 = peg$parse_();\n              if (s3 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c15();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n          if (s0 === peg$FAILED) {\n            s0 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 32) {\n              s1 = peg$c2;\n              peg$currPos++;\n            } else {\n              s1 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c3); }\n            }\n            if (s1 !== peg$FAILED) {\n              s2 = peg$parse_();\n              if (s2 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c16();\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselectors() {\n      var s0, s1, s2, s3, s4, s5, s6, s7;\n\n      var key    = peg$currPos * 30 + 4,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseselector();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parse_();\n        if (s4 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 44) {\n            s5 = peg$c17;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c18); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parse_();\n            if (s6 !== peg$FAILED) {\n              s7 = peg$parseselector();\n              if (s7 !== peg$FAILED) {\n                s4 = [s4, s5, s6, s7];\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parse_();\n          if (s4 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 44) {\n              s5 = peg$c17;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c18); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parse_();\n              if (s6 !== peg$FAILED) {\n                s7 = peg$parseselector();\n                if (s7 !== peg$FAILED) {\n                  s4 = [s4, s5, s6, s7];\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c19(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseselector() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 5,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parsesequence();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        s4 = peg$parsebinaryOp();\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parsesequence();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          s4 = peg$parsebinaryOp();\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parsesequence();\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c20(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsesequence() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 6,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c21;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c22); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$parseatom();\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            s3 = peg$parseatom();\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c23(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseatom() {\n      var s0;\n\n      var key    = peg$currPos * 30 + 7,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$parsewildcard();\n      if (s0 === peg$FAILED) {\n        s0 = peg$parseidentifier();\n        if (s0 === peg$FAILED) {\n          s0 = peg$parseattr();\n          if (s0 === peg$FAILED) {\n            s0 = peg$parsefield();\n            if (s0 === peg$FAILED) {\n              s0 = peg$parsenegation();\n              if (s0 === peg$FAILED) {\n                s0 = peg$parsematches();\n                if (s0 === peg$FAILED) {\n                  s0 = peg$parsehas();\n                  if (s0 === peg$FAILED) {\n                    s0 = peg$parsefirstChild();\n                    if (s0 === peg$FAILED) {\n                      s0 = peg$parselastChild();\n                      if (s0 === peg$FAILED) {\n                        s0 = peg$parsenthChild();\n                        if (s0 === peg$FAILED) {\n                          s0 = peg$parsenthLastChild();\n                          if (s0 === peg$FAILED) {\n                            s0 = peg$parseclass();\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsewildcard() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 8,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 42) {\n        s1 = peg$c24;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c25); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c26(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseidentifier() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 9,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 35) {\n        s1 = peg$c27;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c28); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c29(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattr() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 10,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 91) {\n        s1 = peg$c30;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c31); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrValue();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 93) {\n                s5 = peg$c32;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c33); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c34(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 11,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (peg$c35.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c36); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c37;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c38); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c39(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        if (peg$c40.test(input.charAt(peg$currPos))) {\n          s0 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s0 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c41); }\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrEqOps() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 12,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 33) {\n        s1 = peg$c21;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c22); }\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 61) {\n          s2 = peg$c37;\n          peg$currPos++;\n        } else {\n          s2 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c38); }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c39(s1);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrName() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 13,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseidentifierName();\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        s3 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s4 = peg$c42;\n          peg$currPos++;\n        } else {\n          s4 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c43); }\n        }\n        if (s4 !== peg$FAILED) {\n          s5 = peg$parseidentifierName();\n          if (s5 !== peg$FAILED) {\n            s4 = [s4, s5];\n            s3 = s4;\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s3;\n          s3 = peg$FAILED;\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s4 = peg$c42;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c43); }\n          }\n          if (s4 !== peg$FAILED) {\n            s5 = peg$parseidentifierName();\n            if (s5 !== peg$FAILED) {\n              s4 = [s4, s5];\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c44(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseattrValue() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 14,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseattrName();\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseattrEqOps();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              s5 = peg$parsetype();\n              if (s5 === peg$FAILED) {\n                s5 = peg$parseregex();\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c45(s1, s3, s5);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        s1 = peg$parseattrName();\n        if (s1 !== peg$FAILED) {\n          s2 = peg$parse_();\n          if (s2 !== peg$FAILED) {\n            s3 = peg$parseattrOps();\n            if (s3 !== peg$FAILED) {\n              s4 = peg$parse_();\n              if (s4 !== peg$FAILED) {\n                s5 = peg$parsestring();\n                if (s5 === peg$FAILED) {\n                  s5 = peg$parsenumber();\n                  if (s5 === peg$FAILED) {\n                    s5 = peg$parsepath();\n                  }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s0;\n                  s1 = peg$c45(s1, s3, s5);\n                  s0 = s1;\n                } else {\n                  peg$currPos = s0;\n                  s0 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n        if (s0 === peg$FAILED) {\n          s0 = peg$currPos;\n          s1 = peg$parseattrName();\n          if (s1 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c46(s1);\n          }\n          s0 = s1;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsestring() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 15,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 34) {\n        s1 = peg$c47;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c48); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c49.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c50); }\n        }\n        if (s3 === peg$FAILED) {\n          s3 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 92) {\n            s4 = peg$c51;\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c52); }\n          }\n          if (s4 !== peg$FAILED) {\n            if (input.length > peg$currPos) {\n              s5 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c53); }\n            }\n            if (s5 !== peg$FAILED) {\n              peg$savedPos = s3;\n              s4 = peg$c54(s4, s5);\n              s3 = s4;\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s3;\n            s3 = peg$FAILED;\n          }\n        }\n        while (s3 !== peg$FAILED) {\n          s2.push(s3);\n          if (peg$c49.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c50); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c51;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c52); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c53); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c54(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 34) {\n            s3 = peg$c47;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c48); }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c55(s2);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n      if (s0 === peg$FAILED) {\n        s0 = peg$currPos;\n        if (input.charCodeAt(peg$currPos) === 39) {\n          s1 = peg$c56;\n          peg$currPos++;\n        } else {\n          s1 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c57); }\n        }\n        if (s1 !== peg$FAILED) {\n          s2 = [];\n          if (peg$c58.test(input.charAt(peg$currPos))) {\n            s3 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c59); }\n          }\n          if (s3 === peg$FAILED) {\n            s3 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 92) {\n              s4 = peg$c51;\n              peg$currPos++;\n            } else {\n              s4 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c52); }\n            }\n            if (s4 !== peg$FAILED) {\n              if (input.length > peg$currPos) {\n                s5 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c53); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s3;\n                s4 = peg$c54(s4, s5);\n                s3 = s4;\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s3;\n              s3 = peg$FAILED;\n            }\n          }\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c58.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c59); }\n            }\n            if (s3 === peg$FAILED) {\n              s3 = peg$currPos;\n              if (input.charCodeAt(peg$currPos) === 92) {\n                s4 = peg$c51;\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c52); }\n              }\n              if (s4 !== peg$FAILED) {\n                if (input.length > peg$currPos) {\n                  s5 = input.charAt(peg$currPos);\n                  peg$currPos++;\n                } else {\n                  s5 = peg$FAILED;\n                  if (peg$silentFails === 0) { peg$fail(peg$c53); }\n                }\n                if (s5 !== peg$FAILED) {\n                  peg$savedPos = s3;\n                  s4 = peg$c54(s4, s5);\n                  s3 = s4;\n                } else {\n                  peg$currPos = s3;\n                  s3 = peg$FAILED;\n                }\n              } else {\n                peg$currPos = s3;\n                s3 = peg$FAILED;\n              }\n            }\n          }\n          if (s2 !== peg$FAILED) {\n            if (input.charCodeAt(peg$currPos) === 39) {\n              s3 = peg$c56;\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c57); }\n            }\n            if (s3 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c55(s2);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenumber() {\n      var s0, s1, s2, s3;\n\n      var key    = peg$currPos * 30 + 16,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$currPos;\n      s2 = [];\n      if (peg$c60.test(input.charAt(peg$currPos))) {\n        s3 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s3 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c61); }\n      }\n      while (s3 !== peg$FAILED) {\n        s2.push(s3);\n        if (peg$c60.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c61); }\n        }\n      }\n      if (s2 !== peg$FAILED) {\n        if (input.charCodeAt(peg$currPos) === 46) {\n          s3 = peg$c42;\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c43); }\n        }\n        if (s3 !== peg$FAILED) {\n          s2 = [s2, s3];\n          s1 = s2;\n        } else {\n          peg$currPos = s1;\n          s1 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s1;\n        s1 = peg$FAILED;\n      }\n      if (s1 === peg$FAILED) {\n        s1 = null;\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c60.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c61); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c60.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c61); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c62(s1, s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsepath() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 17,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      s1 = peg$parseidentifierName();\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c63(s1);\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsetype() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 18,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c64) {\n        s1 = peg$c64;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c65); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c66.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c67); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c66.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c67); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c70(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseflags() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 19,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = [];\n      if (peg$c71.test(input.charAt(peg$currPos))) {\n        s1 = input.charAt(peg$currPos);\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c72); }\n      }\n      if (s1 !== peg$FAILED) {\n        while (s1 !== peg$FAILED) {\n          s0.push(s1);\n          if (peg$c71.test(input.charAt(peg$currPos))) {\n            s1 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s1 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c72); }\n          }\n        }\n      } else {\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseregex() {\n      var s0, s1, s2, s3, s4;\n\n      var key    = peg$currPos * 30 + 20,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 47) {\n        s1 = peg$c73;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c74); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = [];\n        if (peg$c75.test(input.charAt(peg$currPos))) {\n          s3 = input.charAt(peg$currPos);\n          peg$currPos++;\n        } else {\n          s3 = peg$FAILED;\n          if (peg$silentFails === 0) { peg$fail(peg$c76); }\n        }\n        if (s3 !== peg$FAILED) {\n          while (s3 !== peg$FAILED) {\n            s2.push(s3);\n            if (peg$c75.test(input.charAt(peg$currPos))) {\n              s3 = input.charAt(peg$currPos);\n              peg$currPos++;\n            } else {\n              s3 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c76); }\n            }\n          }\n        } else {\n          s2 = peg$FAILED;\n        }\n        if (s2 !== peg$FAILED) {\n          if (input.charCodeAt(peg$currPos) === 47) {\n            s3 = peg$c73;\n            peg$currPos++;\n          } else {\n            s3 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c74); }\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parseflags();\n            if (s4 === peg$FAILED) {\n              s4 = null;\n            }\n            if (s4 !== peg$FAILED) {\n              peg$savedPos = s0;\n              s1 = peg$c77(s2, s4);\n              s0 = s1;\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefield() {\n      var s0, s1, s2, s3, s4, s5, s6;\n\n      var key    = peg$currPos * 30 + 21,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 46) {\n        s1 = peg$c42;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c43); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          s4 = peg$currPos;\n          if (input.charCodeAt(peg$currPos) === 46) {\n            s5 = peg$c42;\n            peg$currPos++;\n          } else {\n            s5 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c43); }\n          }\n          if (s5 !== peg$FAILED) {\n            s6 = peg$parseidentifierName();\n            if (s6 !== peg$FAILED) {\n              s5 = [s5, s6];\n              s4 = s5;\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s4;\n            s4 = peg$FAILED;\n          }\n          while (s4 !== peg$FAILED) {\n            s3.push(s4);\n            s4 = peg$currPos;\n            if (input.charCodeAt(peg$currPos) === 46) {\n              s5 = peg$c42;\n              peg$currPos++;\n            } else {\n              s5 = peg$FAILED;\n              if (peg$silentFails === 0) { peg$fail(peg$c43); }\n            }\n            if (s5 !== peg$FAILED) {\n              s6 = peg$parseidentifierName();\n              if (s6 !== peg$FAILED) {\n                s5 = [s5, s6];\n                s4 = s5;\n              } else {\n                peg$currPos = s4;\n                s4 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s4;\n              s4 = peg$FAILED;\n            }\n          }\n          if (s3 !== peg$FAILED) {\n            peg$savedPos = s0;\n            s1 = peg$c78(s2, s3);\n            s0 = s1;\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenegation() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 22,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c79) {\n        s1 = peg$c79;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c80); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c81(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsematches() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 23,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 9) === peg$c82) {\n        s1 = peg$c82;\n        peg$currPos += 9;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c83); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c84(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsehas() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 24,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 5) === peg$c85) {\n        s1 = peg$c85;\n        peg$currPos += 5;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c86); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = peg$parseselectors();\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c87(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsefirstChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 25,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 12) === peg$c88) {\n        s1 = peg$c88;\n        peg$currPos += 12;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c89); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c90();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parselastChild() {\n      var s0, s1;\n\n      var key    = peg$currPos * 30 + 26,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c91) {\n        s1 = peg$c91;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c92); }\n      }\n      if (s1 !== peg$FAILED) {\n        peg$savedPos = s0;\n        s1 = peg$c93();\n      }\n      s0 = s1;\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 27,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 11) === peg$c94) {\n        s1 = peg$c94;\n        peg$currPos += 11;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c95); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c60.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c61); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c60.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c61); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c96(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parsenthLastChild() {\n      var s0, s1, s2, s3, s4, s5;\n\n      var key    = peg$currPos * 30 + 28,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.substr(peg$currPos, 16) === peg$c97) {\n        s1 = peg$c97;\n        peg$currPos += 16;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c98); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parse_();\n        if (s2 !== peg$FAILED) {\n          s3 = [];\n          if (peg$c60.test(input.charAt(peg$currPos))) {\n            s4 = input.charAt(peg$currPos);\n            peg$currPos++;\n          } else {\n            s4 = peg$FAILED;\n            if (peg$silentFails === 0) { peg$fail(peg$c61); }\n          }\n          if (s4 !== peg$FAILED) {\n            while (s4 !== peg$FAILED) {\n              s3.push(s4);\n              if (peg$c60.test(input.charAt(peg$currPos))) {\n                s4 = input.charAt(peg$currPos);\n                peg$currPos++;\n              } else {\n                s4 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c61); }\n              }\n            }\n          } else {\n            s3 = peg$FAILED;\n          }\n          if (s3 !== peg$FAILED) {\n            s4 = peg$parse_();\n            if (s4 !== peg$FAILED) {\n              if (input.charCodeAt(peg$currPos) === 41) {\n                s5 = peg$c68;\n                peg$currPos++;\n              } else {\n                s5 = peg$FAILED;\n                if (peg$silentFails === 0) { peg$fail(peg$c69); }\n              }\n              if (s5 !== peg$FAILED) {\n                peg$savedPos = s0;\n                s1 = peg$c99(s3);\n                s0 = s1;\n              } else {\n                peg$currPos = s0;\n                s0 = peg$FAILED;\n              }\n            } else {\n              peg$currPos = s0;\n              s0 = peg$FAILED;\n            }\n          } else {\n            peg$currPos = s0;\n            s0 = peg$FAILED;\n          }\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n    function peg$parseclass() {\n      var s0, s1, s2;\n\n      var key    = peg$currPos * 30 + 29,\n          cached = peg$resultsCache[key];\n\n      if (cached) {\n        peg$currPos = cached.nextPos;\n\n        return cached.result;\n      }\n\n      s0 = peg$currPos;\n      if (input.charCodeAt(peg$currPos) === 58) {\n        s1 = peg$c100;\n        peg$currPos++;\n      } else {\n        s1 = peg$FAILED;\n        if (peg$silentFails === 0) { peg$fail(peg$c101); }\n      }\n      if (s1 !== peg$FAILED) {\n        s2 = peg$parseidentifierName();\n        if (s2 !== peg$FAILED) {\n          peg$savedPos = s0;\n          s1 = peg$c102(s2);\n          s0 = s1;\n        } else {\n          peg$currPos = s0;\n          s0 = peg$FAILED;\n        }\n      } else {\n        peg$currPos = s0;\n        s0 = peg$FAILED;\n      }\n\n      peg$resultsCache[key] = { nextPos: peg$currPos, result: s0 };\n\n      return s0;\n    }\n\n\n      function nth(n) { return { type: 'nth-child', index: { type: 'literal', value: n } }; }\n      function nthLast(n) { return { type: 'nth-last-child', index: { type: 'literal', value: n } }; }\n      function strUnescape(s) {\n        return s.replace(/\\\\(.)/g, function(match, ch) {\n          switch(ch) {\n            case 'b': return '\\b';\n            case 'f': return '\\f';\n            case 'n': return '\\n';\n            case 'r': return '\\r';\n            case 't': return '\\t';\n            case 'v': return '\\v';\n            default: return ch;\n          }\n        });\n      }\n\n\n    peg$result = peg$startRuleFunction();\n\n    if (peg$result !== peg$FAILED && peg$currPos === input.length) {\n      return peg$result;\n    } else {\n      if (peg$result !== peg$FAILED && peg$currPos < input.length) {\n        peg$fail(peg$endExpectation());\n      }\n\n      throw peg$buildStructuredError(\n        peg$maxFailExpected,\n        peg$maxFailPos < input.length ? input.charAt(peg$maxFailPos) : null,\n        peg$maxFailPos < input.length\n          ? peg$computeLocation(peg$maxFailPos, peg$maxFailPos + 1)\n          : peg$computeLocation(peg$maxFailPos, peg$maxFailPos)\n      );\n    }\n  }\n\n  return {\n    SyntaxError: peg$SyntaxError,\n    parse:       peg$parse\n  };\n});\n", "/* vim: set sw=4 sts=4 : */\nimport estraverse from 'estraverse';\nimport parser from './parser.js';\n\n/**\n* @typedef {\"LEFT_SIDE\"|\"RIGHT_SIDE\"} Side\n*/\n\nconst LEFT_SIDE = 'LEFT_SIDE';\nconst RIGHT_SIDE = 'RIGHT_SIDE';\n\n/**\n * @external AST\n * @see https://esprima.readthedocs.io/en/latest/syntax-tree-format.html\n */\n\n/**\n * One of the rules of `grammar.pegjs`\n * @typedef {PlainObject} SelectorAST\n * @see grammar.pegjs\n*/\n\n/**\n * The `sequence` production of `grammar.pegjs`\n * @typedef {PlainObject} SelectorSequenceAST\n*/\n\n/**\n * Get the value of a property which may be multiple levels down\n * in the object.\n * @param {?PlainObject} obj\n * @param {string[]} keys\n * @returns {undefined|boolean|string|number|external:AST}\n */\nfunction getPath(obj, keys) {\n    for (let i = 0; i < keys.length; ++i) {\n        if (obj == null) { return obj; }\n        obj = obj[keys[i]];\n    }\n    return obj;\n}\n\n/**\n * Determine whether `node` can be reached by following `path`,\n * starting at `ancestor`.\n * @param {?external:AST} node\n * @param {?external:AST} ancestor\n * @param {string[]} path\n * @param {Integer} fromPathIndex\n * @returns {boolean}\n */\nfunction inPath(node, ancestor, path, fromPathIndex) {\n    let current = ancestor;\n    for (let i = fromPathIndex; i < path.length; ++i) {\n        if (current == null) {\n            return false;\n        }\n        const field = current[path[i]];\n        if (Array.isArray(field)) {\n            for (let k = 0; k < field.length; ++k) {\n                if (inPath(node, field[k], path, i + 1)) {\n                    return true;\n                }\n            }\n            return false;\n        }\n        current = field;\n    }\n    return node === current;\n}\n\n/**\n * A generated matcher function for a selector.\n * @typedef {function} SelectorMatcher\n*/\n\n/**\n * A WeakMap for holding cached matcher functions for selectors.\n * @type {WeakMap<SelectorAST, SelectorMatcher>}\n*/\nconst MATCHER_CACHE = typeof WeakMap === 'function' ? new WeakMap : null;\n\n/**\n * Look up a matcher function for `selector` in the cache.\n * If it does not exist, generate it with `generateMatcher` and add it to the cache.\n * In engines without WeakMap, the caching is skipped and matchers are generated with every call.\n * @param {?SelectorAST} selector\n * @returns {SelectorMatcher}\n */\nfunction getMatcher(selector) {\n    if (selector == null) {\n        return () => true;\n    }\n\n    if (MATCHER_CACHE != null) {\n        let matcher = MATCHER_CACHE.get(selector);\n        if (matcher != null) {\n            return matcher;\n        }\n        matcher = generateMatcher(selector);\n        MATCHER_CACHE.set(selector, matcher);\n        return matcher;\n    }\n\n    return generateMatcher(selector);\n}\n\n/**\n * Create a matcher function for `selector`,\n * @param {?SelectorAST} selector\n * @returns {SelectorMatcher}\n */\nfunction generateMatcher(selector) {\n    switch(selector.type) {\n        case 'wildcard':\n            return () => true;\n\n        case 'identifier': {\n            const value = selector.value.toLowerCase();\n            return (node, ancestry, options) => {\n                const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n                return value === node[nodeTypeKey].toLowerCase();\n            };\n        }\n\n        case 'field': {\n            const path = selector.name.split('.');\n            return (node, ancestry) => {\n                const ancestor = ancestry[path.length - 1];\n                return inPath(node, ancestor, path, 0);\n            };\n        }\n\n        case 'matches': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (matchers[i](node, ancestry, options)) { return true; }\n                }\n                return false;\n            };\n        }\n\n        case 'compound': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (!matchers[i](node, ancestry, options)) { return false; }\n                }\n                return true;\n            };\n        }\n\n        case 'not': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                for (let i = 0; i < matchers.length; ++i) {\n                    if (matchers[i](node, ancestry, options)) { return false; }\n                }\n                return true;\n            };\n        }\n\n        case 'has': {\n            const matchers = selector.selectors.map(getMatcher);\n            return (node, ancestry, options) => {\n                let result = false;\n\n                const a = [];\n                estraverse.traverse(node, {\n                    enter (node, parent) {\n                        if (parent != null) { a.unshift(parent); }\n\n                        for (let i = 0; i < matchers.length; ++i) {\n                            if (matchers[i](node, a, options)) {\n                                result = true;\n                                this.break();\n                                return;\n                            }\n                        }\n                    },\n                    leave () { a.shift(); },\n                    keys: options && options.visitorKeys,\n                    fallback: options && options.fallback || 'iteration'\n                });\n\n                return result;\n            };\n        }\n\n        case 'child': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) => {\n                if (ancestry.length > 0 && right(node, ancestry, options)) {\n                    return left(ancestry[0], ancestry.slice(1), options);\n                }\n                return false;\n            };\n        }\n\n        case 'descendant': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) => {\n                if (right(node, ancestry, options)) {\n                    for (let i = 0, l = ancestry.length; i < l; ++i) {\n                        if (left(ancestry[i], ancestry.slice(i + 1), options)) {\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            };\n        }\n\n        case 'attribute': {\n            const path = selector.name.split('.');\n            switch (selector.operator) {\n                case void 0:\n                    return (node) => getPath(node, path) != null;\n                case '=':\n                    switch (selector.value.type) {\n                        case 'regexp':\n                            return (node) => {\n                                const p = getPath(node, path);\n                                return typeof p === 'string' && selector.value.value.test(p);\n                            };\n                        case 'literal': {\n                            const literal = `${selector.value.value}`;\n                            return (node) => literal === `${getPath(node, path)}`;\n                        }\n                        case 'type':\n                            return (node) => selector.value.value === typeof getPath(node, path);\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '!=':\n                    switch (selector.value.type) {\n                        case 'regexp':\n                            return (node) => !selector.value.value.test(getPath(node, path));\n                        case 'literal': {\n                            const literal = `${selector.value.value}`;\n                            return (node) => literal !== `${getPath(node, path)}`;\n                        }\n                        case 'type':\n                            return (node) => selector.value.value !== typeof getPath(node, path);\n                    }\n                    throw new Error(`Unknown selector value type: ${selector.value.type}`);\n                case '<=':\n                    return (node) => getPath(node, path) <= selector.value.value;\n                case '<':\n                    return (node) => getPath(node, path) < selector.value.value;\n                case '>':\n                    return (node) => getPath(node, path) > selector.value.value;\n                case '>=':\n                    return (node) => getPath(node, path) >= selector.value.value;\n            }\n            throw new Error(`Unknown operator: ${selector.operator}`);\n        }\n\n        case 'sibling': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    sibling(node, left, ancestry, LEFT_SIDE, options) ||\n                    selector.left.subject &&\n                    left(node, ancestry, options) &&\n                    sibling(node, right, ancestry, RIGHT_SIDE, options);\n        }\n\n        case 'adjacent': {\n            const left = getMatcher(selector.left);\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    adjacent(node, left, ancestry, LEFT_SIDE, options) ||\n                    selector.right.subject &&\n                    left(node, ancestry, options) &&\n                    adjacent(node, right, ancestry, RIGHT_SIDE, options);\n        }\n\n        case 'nth-child': {\n            const nth = selector.index.value;\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    nthChild(node, ancestry, nth, options);\n        }\n\n        case 'nth-last-child': {\n            const nth = -selector.index.value;\n            const right = getMatcher(selector.right);\n            return (node, ancestry, options) =>\n                right(node, ancestry, options) &&\n                    nthChild(node, ancestry, nth, options);\n        }\n\n        case 'class': {\n\n            return (node, ancestry, options) => {\n                \n                if (options && options.matchClass) {\n                    return options.matchClass(selector.name, node, ancestry);\n                }\n                \n                if (options && options.nodeTypeKey) return false;\n                \n                const name = selector.name.toLowerCase();\n\n                switch(name){\n                    case 'statement':\n                        if(node.type.slice(-9) === 'Statement') return true;\n                        // fallthrough: interface Declaration <: Statement { }\n                    case 'declaration':\n                        return node.type.slice(-11) === 'Declaration';\n                    case 'pattern':\n                        if(node.type.slice(-7) === 'Pattern') return true;\n                        // fallthrough: interface Expression <: Node, Pattern { }\n                    case 'expression':\n                        return node.type.slice(-10) === 'Expression' ||\n                            node.type.slice(-7) === 'Literal' ||\n                            (\n                                node.type === 'Identifier' &&\n                                (ancestry.length === 0 || ancestry[0].type !== 'MetaProperty')\n                            ) ||\n                            node.type === 'MetaProperty';\n                    case 'function':\n                        return node.type === 'FunctionDeclaration' ||\n                            node.type === 'FunctionExpression' ||\n                            node.type === 'ArrowFunctionExpression';\n                }\n                throw new Error(`Unknown class name: ${selector.name}`);\n            };\n        }\n    }\n\n    throw new Error(`Unknown selector type: ${selector.type}`);\n}\n\n/**\n * @callback TraverseOptionFallback\n * @param {external:AST} node The given node.\n * @returns {string[]} An array of visitor keys for the given node.\n */\n\n/**\n * @callback ClassMatcher\n * @param {string} className The name of the class to match.\n * @param {external:AST} node The node to match against.\n * @param {Array<external:AST>} ancestry The ancestry of the node.\n * @returns {boolean} True if the node matches the class, false if not.\n */\n\n/**\n * @typedef {object} ESQueryOptions\n * @property {string} [nodeTypeKey=\"type\"] By passing `nodeTypeKey`, we can allow other ASTs to use ESQuery.\n * @property { { [nodeType: string]: string[] } } [visitorKeys] By passing `visitorKeys` mapping, we can extend the properties of the nodes that traverse the node.\n * @property {TraverseOptionFallback} [fallback] By passing `fallback` option, we can control the properties of traversing nodes when encountering unknown nodes.\n * @property {ClassMatcher} [matchClass] By passing `matchClass` option, we can customize the interpretation of classes.\n */\n\n/**\n * Given a `node` and its ancestors, determine if `node` is matched\n * by `selector`.\n * @param {?external:AST} node\n * @param {?SelectorAST} selector\n * @param {external:AST[]} [ancestry=[]]\n * @param {ESQueryOptions} [options]\n * @throws {Error} Unknowns (operator, class name, selector type, or\n * selector value type)\n * @returns {boolean}\n */\nfunction matches(node, selector, ancestry, options) {\n    if (!selector) { return true; }\n    if (!node) { return false; }\n    if (!ancestry) { ancestry = []; }\n\n    return getMatcher(selector)(node, ancestry, options);\n}\n\n/**\n * Get visitor keys of a given node.\n * @param {external:AST} node The AST node to get keys.\n * @param {ESQueryOptions|undefined} options\n * @returns {string[]} Visitor keys of the node.\n */\nfunction getVisitorKeys(node, options) {\n    const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n\n    const nodeType = node[nodeTypeKey];\n    if (options && options.visitorKeys && options.visitorKeys[nodeType]) {\n        return options.visitorKeys[nodeType];\n    }\n    if (estraverse.VisitorKeys[nodeType]) {\n        return estraverse.VisitorKeys[nodeType];\n    }\n    if (options && typeof options.fallback === 'function') {\n        return options.fallback(node);\n    }\n    // 'iteration' fallback\n    return Object.keys(node).filter(function (key) {\n        return key !== nodeTypeKey;\n    });\n}\n\n\n/**\n * Check whether the given value is an ASTNode or not.\n * @param {any} node The value to check.\n * @param {ESQueryOptions|undefined} options The options to use.\n * @returns {boolean} `true` if the value is an ASTNode.\n */\nfunction isNode(node, options) {\n    const nodeTypeKey = (options && options.nodeTypeKey) || 'type';\n    return node !== null && typeof node === 'object' && typeof node[nodeTypeKey] === 'string';\n}\n\n/**\n * Determines if the given node has a sibling that matches the\n * given selector matcher.\n * @param {external:AST} node\n * @param {SelectorMatcher} matcher\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction sibling(node, matcher, ancestry, side, options) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const startIndex = listProp.indexOf(node);\n            if (startIndex < 0) { continue; }\n            let lowerBound, upperBound;\n            if (side === LEFT_SIDE) {\n                lowerBound = 0;\n                upperBound = startIndex;\n            } else {\n                lowerBound = startIndex + 1;\n                upperBound = listProp.length;\n            }\n            for (let k = lowerBound; k < upperBound; ++k) {\n                if (isNode(listProp[k], options) && matcher(listProp[k], ancestry, options)) {\n                    return true;\n                }\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * Determines if the given node has an adjacent sibling that matches\n * the given selector matcher.\n * @param {external:AST} node\n * @param {SelectorMatcher} matcher\n * @param {external:AST[]} ancestry\n * @param {Side} side\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction adjacent(node, matcher, ancestry, side, options) {\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)) {\n            const idx = listProp.indexOf(node);\n            if (idx < 0) { continue; }\n            if (side === LEFT_SIDE && idx > 0 && isNode(listProp[idx - 1], options) && matcher(listProp[idx - 1], ancestry, options)) {\n                return true;\n            }\n            if (side === RIGHT_SIDE && idx < listProp.length - 1 && isNode(listProp[idx + 1], options) &&  matcher(listProp[idx + 1], ancestry, options)) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * Determines if the given node is the `nth` child.\n * If `nth` is negative then the position is counted\n * from the end of the list of children.\n * @param {external:AST} node\n * @param {external:AST[]} ancestry\n * @param {Integer} nth\n * @param {ESQueryOptions|undefined} options\n * @returns {boolean}\n */\nfunction nthChild(node, ancestry, nth, options) {\n    if (nth === 0) { return false; }\n    const [parent] = ancestry;\n    if (!parent) { return false; }\n    const keys = getVisitorKeys(parent, options);\n    for (let i = 0; i < keys.length; ++i) {\n        const listProp = parent[keys[i]];\n        if (Array.isArray(listProp)){\n            const idx = nth < 0 ? listProp.length + nth : nth - 1;\n            if (idx >= 0 && idx < listProp.length && listProp[idx] === node) {\n                return true;\n            }\n        }\n    }\n    return false;\n}\n\n/**\n * For each selector node marked as a subject, find the portion of the\n * selector that the subject must match.\n * @param {SelectorAST} selector\n * @param {SelectorAST} [ancestor] Defaults to `selector`\n * @returns {SelectorAST[]}\n */\nfunction subjects(selector, ancestor) {\n    if (selector == null || typeof selector != 'object') { return []; }\n    if (ancestor == null) { ancestor = selector; }\n    const results = selector.subject ? [ancestor] : [];\n    const keys = Object.keys(selector);\n    for (let i = 0; i < keys.length; ++i) {\n        const p = keys[i];\n        const sel = selector[p];\n        results.push(...subjects(sel, p === 'left' ? sel : ancestor));\n    }\n    return results;\n}\n\n/**\n* @callback TraverseVisitor\n* @param {?external:AST} node\n* @param {?external:AST} parent\n* @param {external:AST[]} ancestry\n*/\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @param {TraverseVisitor} visitor\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction traverse(ast, selector, visitor, options) {\n    if (!selector) { return; }\n    const ancestry = [];\n    const matcher = getMatcher(selector);\n    const altSubjects = subjects(selector).map(getMatcher);\n    estraverse.traverse(ast, {\n        enter (node, parent) {\n            if (parent != null) { ancestry.unshift(parent); }\n            if (matcher(node, ancestry, options)) {\n                if (altSubjects.length) {\n                    for (let i = 0, l = altSubjects.length; i < l; ++i) {\n                        if (altSubjects[i](node, ancestry, options)) {\n                            visitor(node, parent, ancestry);\n                        }\n                        for (let k = 0, m = ancestry.length; k < m; ++k) {\n                            const succeedingAncestry = ancestry.slice(k + 1);\n                            if (altSubjects[i](ancestry[k], succeedingAncestry, options)) {\n                                visitor(ancestry[k], parent, succeedingAncestry);\n                            }\n                        }\n                    }\n                } else {\n                    visitor(node, parent, ancestry);\n                }\n            }\n        },\n        leave () { ancestry.shift(); },\n        keys: options && options.visitorKeys,\n        fallback: options && options.fallback || 'iteration'\n    });\n}\n\n\n/**\n * From a JS AST and a selector AST, collect all JS AST nodes that\n * match the selector.\n * @param {external:AST} ast\n * @param {?SelectorAST} selector\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction match(ast, selector, options) {\n    const results = [];\n    traverse(ast, selector, function (node) {\n        results.push(node);\n    }, options);\n    return results;\n}\n\n/**\n * Parse a selector string and return its AST.\n * @param {string} selector\n * @returns {SelectorAST}\n */\nfunction parse(selector) {\n    return parser.parse(selector);\n}\n\n/**\n * Query the code AST using the selector string.\n * @param {external:AST} ast\n * @param {string} selector\n * @param {ESQueryOptions} [options]\n * @returns {external:AST[]}\n */\nfunction query(ast, selector, options) {\n    return match(ast, parse(selector), options);\n}\n\nquery.parse = parse;\nquery.match = match;\nquery.traverse = traverse;\nquery.matches = matches;\nquery.query = query;\n\nexport default query;\n"], "names": ["module", "exports", "peg$SyntaxError", "message", "expected", "found", "location", "this", "name", "Error", "captureStackTrace", "child", "parent", "ctor", "constructor", "prototype", "peg$subclass", "buildMessage", "DESCRIBE_EXPECTATION_FNS", "literal", "expectation", "literalEscape", "text", "class", "i", "escapedParts", "parts", "length", "Array", "classEscape", "inverted", "any", "end", "other", "description", "hex", "ch", "charCodeAt", "toString", "toUpperCase", "s", "replace", "j", "descriptions", "type", "sort", "slice", "join", "describeExpected", "describeFound", "SyntaxError", "parse", "input", "options", "peg$result", "peg$FAILED", "peg$startRuleFunctions", "start", "peg$parsestart", "peg$startRuleFunction", "peg$c3", "peg$literalExpectation", "peg$c4", "peg$c5", "peg$classExpectation", "peg$c8", "peg$c11", "peg$c14", "peg$c18", "peg$c22", "peg$c25", "peg$c28", "peg$c31", "peg$c33", "peg$c35", "peg$c36", "peg$c38", "peg$c39", "a", "peg$c40", "peg$c41", "peg$c43", "peg$c45", "op", "value", "operator", "peg$c48", "peg$c49", "peg$c50", "peg$c52", "peg$c53", "peg$c54", "b", "peg$c55", "d", "match", "peg$c57", "peg$c58", "peg$c59", "peg$c60", "peg$c61", "peg$c65", "peg$c66", "peg$c67", "peg$c69", "peg$c71", "peg$c72", "peg$c74", "peg$c75", "peg$c76", "peg$c80", "peg$c83", "peg$c86", "peg$c89", "peg$c92", "peg$c95", "peg$c98", "peg$c101", "peg$currPos", "peg$posDetailsCache", "line", "column", "peg$maxFailPos", "peg$maxFailExpected", "peg$resultsCache", "startRule", "ignoreCase", "peg$computePosDetails", "pos", "p", "details", "peg$computeLocation", "startPos", "endPos", "startPosDetails", "endPosDetails", "offset", "peg$fail", "push", "s0", "s1", "s2", "ss", "key", "cached", "nextPos", "result", "peg$parse_", "peg$parseselectors", "selectors", "peg$c1", "peg$parseidentifierName", "test", "char<PERSON>t", "peg$parsebinaryOp", "s3", "s4", "s5", "s6", "s7", "peg$parseselector", "concat", "map", "peg$parsesequence", "reduce", "memo", "rhs", "left", "right", "subject", "as", "peg$parseatom", "peg$parsewildcard", "peg$parseidentifier", "peg$parseattrName", "peg$parseattrEqOps", "substr", "peg$parsetype", "flgs", "peg$parseflags", "RegExp", "peg$parseregex", "peg$parseattrOps", "peg$parsestring", "leadingDecimals", "apply", "parseFloat", "peg$parsenumber", "peg$parsepath", "peg$parseattrValue", "peg$parseattr", "peg$parsefield", "peg$parsenegation", "peg$parsematches", "peg$parsehas", "nth", "peg$parsefirstChild", "nthLast", "peg$parselastChild", "parseInt", "peg$parsenthChild", "peg$parsenthLastChild", "peg$parseclass", "n", "index", "factory", "<PERSON><PERSON><PERSON>", "obj", "keys", "MATCHER_CACHE", "WeakMap", "getMatcher", "selector", "matcher", "get", "generateMatcher", "set", "toLowerCase", "node", "ancestry", "nodeTypeKey", "path", "split", "inPath", "ancestor", "fromPathIndex", "current", "field", "isArray", "k", "matchers", "estraverse", "traverse", "enter", "unshift", "leave", "shift", "visitorKeys", "fallback", "l", "sibling", "adjacent", "nthChild", "matchClass", "getVisitorKeys", "nodeType", "VisitorKeys", "Object", "filter", "isNode", "_typeof", "side", "listProp", "startIndex", "indexOf", "lowerBound", "upperBound", "idx", "ast", "visitor", "altSubjects", "subjects", "results", "sel", "m", "succeedingAncestry", "parser", "query", "matches"], "mappings": "mnEAQ2CA,EAAOC,UAC9CD,UAEK,WASP,SAASE,EAAgBC,EAASC,EAAUC,EAAOC,GACjDC,KAAKJ,QAAWA,EAChBI,KAAKH,SAAWA,EAChBG,KAAKF,MAAWA,EAChBE,KAAKD,SAAWA,EAChBC,KAAKC,KAAW,cAEuB,mBAA5BC,MAAMC,mBACfD,MAAMC,kBAAkBH,KAAML,GAq9ElC,OAn+EA,SAAsBS,EAAOC,GAC3B,SAASC,IAASN,KAAKO,YAAcH,EACrCE,EAAKE,UAAYH,EAAOG,UACxBJ,EAAMI,UAAY,IAAIF,EAexBG,CAAad,EAAiBO,OAE9BP,EAAgBe,aAAe,SAASb,EAAUC,GAChD,IAAIa,EAA2B,CACzBC,QAAS,SAASC,GAChB,MAAO,IAAOC,EAAcD,EAAYE,MAAQ,KAGlDC,MAAS,SAASH,GAChB,IACII,EADAC,EAAe,GAGnB,IAAKD,EAAI,EAAGA,EAAIJ,EAAYM,MAAMC,OAAQH,IACxCC,GAAgBL,EAAYM,MAAMF,aAAcI,MAC5CC,EAAYT,EAAYM,MAAMF,GAAG,IAAM,IAAMK,EAAYT,EAAYM,MAAMF,GAAG,IAC9EK,EAAYT,EAAYM,MAAMF,IAGpC,MAAO,KAAOJ,EAAYU,SAAW,IAAM,IAAML,EAAe,KAGlEM,IAAK,SAASX,GACZ,MAAO,iBAGTY,IAAK,SAASZ,GACZ,MAAO,gBAGTa,MAAO,SAASb,GACd,OAAOA,EAAYc,cAI3B,SAASC,EAAIC,GACX,OAAOA,EAAGC,WAAW,GAAGC,SAAS,IAAIC,cAGvC,SAASlB,EAAcmB,GACrB,OAAOA,EACJC,QAAQ,MAAO,QACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASL,GAAM,MAAO,OAASD,EAAIC,MACpEK,QAAQ,yBAAyB,SAASL,GAAM,MAAO,MAASD,EAAIC,MAGzE,SAASP,EAAYW,GACnB,OAAOA,EACJC,QAAQ,MAAO,QACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,KAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,MAAO,OACfA,QAAQ,gBAAyB,SAASL,GAAM,MAAO,OAASD,EAAIC,MACpEK,QAAQ,yBAAyB,SAASL,GAAM,MAAO,MAASD,EAAIC,MA6CzE,MAAO,YAtCP,SAA0BhC,GACxB,IACIoB,EAAGkB,EANoBtB,EAKvBuB,EAAe,IAAIf,MAAMxB,EAASuB,QAGtC,IAAKH,EAAI,EAAGA,EAAIpB,EAASuB,OAAQH,IAC/BmB,EAAanB,IATYJ,EASahB,EAASoB,GAR1CN,EAAyBE,EAAYwB,MAAMxB,IAalD,GAFAuB,EAAaE,OAETF,EAAahB,OAAS,EAAG,CAC3B,IAAKH,EAAI,EAAGkB,EAAI,EAAGlB,EAAImB,EAAahB,OAAQH,IACtCmB,EAAanB,EAAI,KAAOmB,EAAanB,KACvCmB,EAAaD,GAAKC,EAAanB,GAC/BkB,KAGJC,EAAahB,OAASe,EAGxB,OAAQC,EAAahB,QACnB,KAAK,EACH,OAAOgB,EAAa,GAEtB,KAAK,EACH,OAAOA,EAAa,GAAK,OAASA,EAAa,GAEjD,QACE,OAAOA,EAAaG,MAAM,GAAI,GAAGC,KAAK,MAClC,QACAJ,EAAaA,EAAahB,OAAS,IAQxBqB,CAAiB5C,GAAY,QAJlD,SAAuBC,GACrB,OAAOA,EAAQ,IAAOgB,EAAchB,GAAS,IAAO,eAGM4C,CAAc5C,GAAS,WAu2E9E,CACL6C,YAAahD,EACbiD,MAt2EF,SAAmBC,EAAOC,GACxBA,OAAsB,IAAZA,EAAqBA,EAAU,OAoJrCC,EAwH8BlD,EAAUC,EAAOC,EA1Q/CiD,EAAa,GAEbC,EAAyB,CAAEC,MAAOC,IAClCC,EAAyBD,GAOzBE,EAASC,GAAuB,KAAK,GACrCC,EAAS,uBACTC,EAASC,GAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAAM,GAAM,GAGjHC,EAASJ,GAAuB,KAAK,GAGrCK,EAAUL,GAAuB,KAAK,GAGtCM,EAAUN,GAAuB,KAAK,GAItCO,EAAUP,GAAuB,KAAK,GAUtCQ,EAAUR,GAAuB,KAAK,GAOtCS,EAAUT,GAAuB,KAAK,GAGtCU,EAAUV,GAAuB,KAAK,GAGtCW,EAAUX,GAAuB,KAAK,GAEtCY,EAAUZ,GAAuB,KAAK,GAEtCa,EAAU,SACVC,EAAUX,GAAqB,CAAC,IAAK,IAAK,MAAM,GAAO,GAEvDY,EAAUf,GAAuB,KAAK,GACtCgB,EAAU,SAASC,GAAK,OAAQA,GAAK,IAAM,KAC3CC,EAAU,QACVC,EAAUhB,GAAqB,CAAC,IAAK,MAAM,GAAO,GAElDiB,EAAUpB,GAAuB,KAAK,GAItCqB,EAAU,SAAS1E,EAAM2E,EAAIC,GACvB,MAAO,CAAExC,KAAM,YAAapC,KAAMA,EAAM6E,SAAUF,EAAIC,MAAOA,IAInEE,EAAUzB,GAAuB,KAAM,GACvC0B,EAAU,UACVC,EAAUxB,GAAqB,CAAC,KAAM,MAAO,GAAM,GAEnDyB,EAAU5B,GAAuB,MAAM,GACvC6B,EAmHK,CAAE9C,KAAM,OAlHb+C,EAAU,SAASb,EAAGc,GAAK,OAAOd,EAAIc,GACtCC,EAAU,SAASC,GACX,MAAO,CAAElD,KAAM,UAAWwC,OAkvEf5C,EAlvEkCsD,EAAE/C,KAAK,IAmvErDP,EAAEC,QAAQ,UAAU,SAASsD,EAAO3D,GACzC,OAAOA,GACL,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,IAAK,IAAK,MAAO,KACjB,QAAS,OAAOA,QATtB,IAAqBI,GA/uEnBwD,EAAUnC,GAAuB,KAAK,GACtCoC,EAAU,UACVC,EAAUlC,GAAqB,CAAC,KAAM,MAAM,GAAM,GAClDmC,EAAU,SACVC,EAAUpC,GAAqB,CAAC,CAAC,IAAK,OAAO,GAAO,GAQpDqC,EAAUxC,GAAuB,SAAS,GAC1CyC,EAAU,SACVC,EAAUvC,GAAqB,CAAC,IAAK,MAAM,GAAM,GAEjDwC,EAAU3C,GAAuB,KAAK,GAEtC4C,EAAU,UACVC,EAAU1C,GAAqB,CAAC,IAAK,IAAK,IAAK,MAAM,GAAO,GAE5D2C,EAAU9C,GAAuB,KAAK,GACtC+C,EAAU,SACVC,EAAU7C,GAAqB,CAAC,MAAM,GAAM,GAQ5C8C,EAAUjD,GAAuB,SAAS,GAG1CkD,EAAUlD,GAAuB,aAAa,GAG9CmD,EAAUnD,GAAuB,SAAS,GAG1CoD,GAAUpD,GAAuB,gBAAgB,GAGjDqD,GAAUrD,GAAuB,eAAe,GAGhDsD,GAAUtD,GAAuB,eAAe,GAGhDuD,GAAUvD,GAAuB,oBAAoB,GAGrDwD,GAAWxD,GAAuB,KAAK,GAKvCyD,GAAuB,EAEvBC,GAAuB,CAAC,CAAEC,KAAM,EAAGC,OAAQ,IAC3CC,GAAuB,EACvBC,GAAuB,GAGvBC,GAAmB,GAIvB,GAAI,cAAevE,EAAS,CAC1B,KAAMA,EAAQwE,aAAarE,GACzB,MAAM,IAAI/C,MAAM,mCAAqC4C,EAAQwE,UAAY,MAG3ElE,EAAwBH,EAAuBH,EAAQwE,WA2BzD,SAAShE,GAAuBvC,EAAMwG,GACpC,MAAO,CAAElF,KAAM,UAAWtB,KAAMA,EAAMwG,WAAYA,GAGpD,SAAS9D,GAAqBtC,EAAOI,EAAUgG,GAC7C,MAAO,CAAElF,KAAM,QAASlB,MAAOA,EAAOI,SAAUA,EAAUgG,WAAYA,GAexE,SAASC,GAAsBC,GAC7B,IAAwCC,EAApCC,EAAUX,GAAoBS,GAElC,GAAIE,EACF,OAAOA,EAGP,IADAD,EAAID,EAAM,GACFT,GAAoBU,IAC1BA,IASF,IALAC,EAAU,CACRV,MAFFU,EAAUX,GAAoBU,IAEZT,KAChBC,OAAQS,EAAQT,QAGXQ,EAAID,GACmB,KAAxB5E,EAAMf,WAAW4F,IACnBC,EAAQV,OACRU,EAAQT,OAAS,GAEjBS,EAAQT,SAGVQ,IAIF,OADAV,GAAoBS,GAAOE,EACpBA,EAIX,SAASC,GAAoBC,EAAUC,GACrC,IAAIC,EAAkBP,GAAsBK,GACxCG,EAAkBR,GAAsBM,GAE5C,MAAO,CACL5E,MAAO,CACL+E,OAAQJ,EACRZ,KAAQc,EAAgBd,KACxBC,OAAQa,EAAgBb,QAE1BzF,IAAK,CACHwG,OAAQH,EACRb,KAAQe,EAAcf,KACtBC,OAAQc,EAAcd,SAK5B,SAASgB,GAASrI,GACZkH,GAAcI,KAEdJ,GAAcI,KAChBA,GAAiBJ,GACjBK,GAAsB,IAGxBA,GAAoBe,KAAKtI,IAgB3B,SAASsD,KACP,IAAIiF,EAAIC,EAAIC,EA/QQC,EAiRhBC,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,IACLsB,EAAKO,QACM5F,IACTsF,EAAKO,QACM7F,GACJ4F,OACM5F,EAGToF,EADAC,EAjSqB,KADPE,EAkSFD,GAjSFlH,OAAemH,EAAG,GAAK,CAAElG,KAAM,UAAWyG,UAAWP,IA4SnExB,GAAcqB,EACdA,EAAKpF,GAEHoF,IAAOpF,IACToF,EAAKrB,IACLsB,EAAKO,QACM5F,IAETqF,OAAKU,GAEPX,EAAKC,GAGPhB,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GAGT,SAASQ,KACP,IAAIR,EAAIC,EAEJG,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAWhB,IARAP,EAAK,GACiC,KAAlCvF,EAAMf,WAAWiF,KACnBsB,EAzUS,IA0UTtB,OAEAsB,EAAKrF,EACwBkF,GAAS7E,IAEjCgF,IAAOrF,GACZoF,EAAGD,KAAKE,GAC8B,KAAlCxF,EAAMf,WAAWiF,KACnBsB,EAlVO,IAmVPtB,OAEAsB,EAAKrF,EACwBkF,GAAS7E,IAM1C,OAFAgE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAGT,SAASY,KACP,IAAIZ,EAAIC,EAAIC,EAERE,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAYhB,GARAN,EAAK,GACD9E,EAAO0F,KAAKpG,EAAMqG,OAAOnC,MAC3BuB,EAAKzF,EAAMqG,OAAOnC,IAClBA,OAEAuB,EAAKtF,EACwBkF,GAAS1E,IAEpC8E,IAAOtF,EACT,KAAOsF,IAAOtF,GACZqF,EAAGF,KAAKG,GACJ/E,EAAO0F,KAAKpG,EAAMqG,OAAOnC,MAC3BuB,EAAKzF,EAAMqG,OAAOnC,IAClBA,OAEAuB,EAAKtF,EACwBkF,GAAS1E,SAI1C6E,EAAKrF,EAUP,OARIqF,IAAOrF,IAETqF,EAAYA,EAhYoB7F,KAAK,KAkYvC4F,EAAKC,EAELhB,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAGT,SAASe,KACP,IAAIf,EAAIC,EAAIC,EAERE,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,IACLsB,EAAKO,QACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuB,EAxZO,IAyZPvB,OAEAuB,EAAKtF,EACwBkF,GAASxE,IAEpC4E,IAAOtF,GACJ4F,OACM5F,EAGToF,EADAC,EAhayB,SAua3BtB,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,GAEHoF,IAAOpF,IACToF,EAAKrB,IACLsB,EAAKO,QACM5F,GAC6B,MAAlCH,EAAMf,WAAWiF,KACnBuB,EAlbM,IAmbNvB,OAEAuB,EAAKtF,EACwBkF,GAASvE,IAEpC2E,IAAOtF,GACJ4F,OACM5F,EAGToF,EADAC,EA1bwB,WAic1BtB,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,GAEHoF,IAAOpF,IACToF,EAAKrB,IACLsB,EAAKO,QACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuB,EA5cI,IA6cJvB,OAEAuB,EAAKtF,EACwBkF,GAAStE,IAEpC0E,IAAOtF,GACJ4F,OACM5F,EAGToF,EADAC,EApdsB,YA2dxBtB,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,GAEHoF,IAAOpF,IACToF,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EAlfG,IAmfHtB,OAEAsB,EAAKrF,EACwBkF,GAAS7E,IAEpCgF,IAAOrF,IACTsF,EAAKM,QACM5F,EAGToF,EADAC,EA9esB,cAqfxBtB,GAAcqB,EACdA,EAAKpF,MAMbqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GAGT,SAASS,KACP,IAAIT,EAAIC,EAAIC,EAAIc,EAAIC,EAAIC,EAAIC,EAAIC,EAE5BhB,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAKhB,GAFAP,EAAKrB,IACLsB,EAAKoB,QACMzG,EAAY,CAmCrB,IAlCAsF,EAAK,GACLc,EAAKrC,IACLsC,EAAKT,QACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuC,EAphBM,IAqhBNvC,OAEAuC,EAAKtG,EACwBkF,GAASrE,IAEpCyF,IAAOtG,IACTuG,EAAKX,QACM5F,IACTwG,EAAKC,QACMzG,EAEToG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBzC,GAAcqC,EACdA,EAAKpG,KAGP+D,GAAcqC,EACdA,EAAKpG,GAEAoG,IAAOpG,GACZsF,EAAGH,KAAKiB,GACRA,EAAKrC,IACLsC,EAAKT,QACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuC,EAvjBI,IAwjBJvC,OAEAuC,EAAKtG,EACwBkF,GAASrE,IAEpCyF,IAAOtG,IACTuG,EAAKX,QACM5F,IACTwG,EAAKC,QACMzG,EAEToG,EADAC,EAAK,CAACA,EAAIC,EAAIC,EAAIC,IAWtBzC,GAAcqC,EACdA,EAAKpG,KAGP+D,GAAcqC,EACdA,EAAKpG,GAGLsF,IAAOtF,EAGToF,EADAC,EAplBO,CAolBMA,GAplBFqB,OAolBMpB,EAplBIqB,KAAI,SAAU1H,GAAK,OAAOA,EAAE,QAulBjD8E,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAGT,SAASqB,KACP,IAAIrB,EAAIC,EAAIC,EAAIc,EAAIC,EAAIC,EAnmBH/E,EAqmBjBiE,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAKhB,GAFAP,EAAKrB,IACLsB,EAAKuB,QACM5G,EAAY,CAiBrB,IAhBAsF,EAAK,GACLc,EAAKrC,IACLsC,EAAKF,QACMnG,IACTsG,EAAKM,QACM5G,EAEToG,EADAC,EAAK,CAACA,EAAIC,IAOZvC,GAAcqC,EACdA,EAAKpG,GAEAoG,IAAOpG,GACZsF,EAAGH,KAAKiB,GACRA,EAAKrC,IACLsC,EAAKF,QACMnG,IACTsG,EAAKM,QACM5G,EAEToG,EADAC,EAAK,CAACA,EAAIC,IAOZvC,GAAcqC,EACdA,EAAKpG,GAGLsF,IAAOtF,GAnpBQuB,EAqpBJ8D,EACbD,EADAC,EAAiBC,EAppBJuB,QAAO,SAAUC,EAAMC,GAChC,MAAO,CAAE1H,KAAM0H,EAAI,GAAIC,KAAMF,EAAMG,MAAOF,EAAI,MAC7CxF,KAqpBLwC,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAGT,SAASwB,KACP,IAAIxB,EAAIC,EAAIC,EAAIc,EA/pBKc,EAASC,EAClB9E,EAgqBRmD,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAchB,GAXAP,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EA9qBU,IA+qBVtB,OAEAsB,EAAKrF,EACwBkF,GAASpE,IAEpCuE,IAAOrF,IACTqF,EAAK,MAEHA,IAAOrF,EAAY,CAGrB,GAFAsF,EAAK,IACLc,EAAKgB,QACMpH,EACT,KAAOoG,IAAOpG,GACZsF,EAAGH,KAAKiB,GACRA,EAAKgB,UAGP9B,EAAKtF,EAEHsF,IAAOtF,GAhsBQkH,EAksBJ7B,EAjsBLhD,EAAkB,KADA8E,EAksBT7B,GAjsBFlH,OAAe+I,EAAG,GAAK,CAAE9H,KAAM,WAAYyG,UAAWqB,GAChED,IAAS7E,EAAE6E,SAAU,GAisB1B9B,EADAC,EA/rBShD,IAksBT0B,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAGT,SAASgC,KACP,IAAIhC,EAEAI,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,UAGhBP,EAwCF,WACE,IAAIA,EAAIC,EAEJG,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAIsB,KAAlC9F,EAAMf,WAAWiF,KACnBsB,EA/wBU,IAgxBVtB,OAEAsB,EAAKrF,EACwBkF,GAASnE,IAEpCsE,IAAOrF,IAETqF,EArxB+B,CAAEhG,KAAM,WAAYwC,MAqxBtCwD,IAEfD,EAAKC,EAELhB,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GApEFiC,MACMrH,IACToF,EAqEJ,WACE,IAAIA,EAAIC,EAAIC,EAERE,EAAuB,GAAdzB,GAAmB,EAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EA3yBU,IA4yBVtB,OAEAsB,EAAKrF,EACwBkF,GAASlE,IAEpCqE,IAAOrF,IACTqF,EAAK,MAEHA,IAAOrF,IACTsF,EAAKU,QACMhG,EAGToF,EADAC,EAtzB6B,CAAEhG,KAAM,aAAcwC,MAszBtCyD,IAOfvB,GAAcqB,EACdA,EAAKpF,GAGPqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GA7GAkC,MACMtH,IACToF,EA8GN,WACE,IAAIA,EAAIC,EAAQe,EAAQE,EAEpBd,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EAn1BU,IAo1BVtB,OAEAsB,EAAKrF,EACwBkF,GAASjE,IAEpCoE,IAAOrF,GACJ4F,OACM5F,IACToG,EAmON,WACE,IAAIhB,EAAIC,EAAQe,EAAQE,EAEpBd,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,IACLsB,EAAKkC,QACMvH,GACJ4F,OACM5F,IACToG,EAjJN,WACE,IAAIhB,EAAIC,EAAIC,EAERE,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EA19BU,IA29BVtB,OAEAsB,EAAKrF,EACwBkF,GAASpE,IAEpCuE,IAAOrF,IACTqF,EAAK,MAEHA,IAAOrF,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuB,EAj9BQ,IAk9BRvB,OAEAuB,EAAKtF,EACwBkF,GAAS7D,IAEpCiE,IAAOtF,GAETqF,EAAK/D,EAAQ+D,GACbD,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,GAGPqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GAmGEoC,MACMxH,GACJ4F,OACM5F,IACTsG,EA+bV,WACE,IAAIlB,EAAIC,EAAQe,EAAIC,EAAIC,EAEpBd,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAWhB,GARAP,EAAKrB,GAn/CO,UAo/CRlE,EAAM4H,OAAO1D,GAAa,IAC5BsB,EAr/CU,QAs/CVtB,IAAe,IAEfsB,EAAKrF,EACwBkF,GAASpC,IAEpCuC,IAAOrF,EAET,GADK4F,OACM5F,EAAY,CASrB,GARAoG,EAAK,GACDrD,EAAQkD,KAAKpG,EAAMqG,OAAOnC,MAC5BsC,EAAKxG,EAAMqG,OAAOnC,IAClBA,OAEAsC,EAAKrG,EACwBkF,GAASlC,IAEpCqD,IAAOrG,EACT,KAAOqG,IAAOrG,GACZoG,EAAGjB,KAAKkB,GACJtD,EAAQkD,KAAKpG,EAAMqG,OAAOnC,MAC5BsC,EAAKxG,EAAMqG,OAAOnC,IAClBA,OAEAsC,EAAKrG,EACwBkF,GAASlC,SAI1CoD,EAAKpG,EAEHoG,IAAOpG,IACTqG,EAAKT,QACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuC,EAphDE,IAqhDFvC,OAEAuC,EAAKtG,EACwBkF,GAASjC,IAEpCqD,IAAOtG,GAETqF,EA1hDuB,CAAEhG,KAAM,OAAQwC,MA0hD1BuE,EA1hDmC5G,KAAK,KA2hDrD4F,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAOT+D,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,OAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAjhBMsC,MACM1H,IACTsG,EA0jBZ,WACE,IAAIlB,EAAIC,EAAIC,EAAIc,EAAIC,EAxlDIsB,EA0lDpBnC,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAWhB,GARAP,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EAzmDU,IA0mDVtB,OAEAsB,EAAKrF,EACwBkF,GAAS9B,IAEpCiC,IAAOrF,EAAY,CASrB,GARAsF,EAAK,GACDjC,EAAQ4C,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAAS5B,IAEpC8C,IAAOpG,EACT,KAAOoG,IAAOpG,GACZsF,EAAGH,KAAKiB,GACJ/C,EAAQ4C,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAAS5B,SAI1CgC,EAAKtF,EAEHsF,IAAOtF,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBqC,EAxoDM,IAyoDNrC,OAEAqC,EAAKpG,EACwBkF,GAAS9B,IAEpCgD,IAAOpG,IACTqG,EA5FR,WACE,IAAIjB,EAAIC,EAEJG,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAWhB,GARAP,EAAK,GACDlC,EAAQ+C,KAAKpG,EAAMqG,OAAOnC,MAC5BsB,EAAKxF,EAAMqG,OAAOnC,IAClBA,OAEAsB,EAAKrF,EACwBkF,GAAS/B,IAEpCkC,IAAOrF,EACT,KAAOqF,IAAOrF,GACZoF,EAAGD,KAAKE,GACJnC,EAAQ+C,KAAKpG,EAAMqG,OAAOnC,MAC5BsB,EAAKxF,EAAMqG,OAAOnC,IAClBA,OAEAsB,EAAKrF,EACwBkF,GAAS/B,SAI1CiC,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAuDIwC,MACM5H,IACTqG,EAAK,MAEHA,IAAOrG,GA/oDO2H,EAipDCtB,EAAjBhB,EAjpD+B,CAC/BhG,KAAM,SAAUwC,MAAO,IAAIgG,OAgpDdvC,EAhpDuB9F,KAAK,IAAKmI,EAAOA,EAAKnI,KAAK,IAAM,KAipDrE4F,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAzoBQ0C,IAEHxB,IAAOtG,GAETqF,EAAK1D,EAAQ0D,EAAIe,EAAIE,GACrBlB,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAeb+D,GAAcqB,EACdA,EAAKpF,GAEHoF,IAAOpF,IACToF,EAAKrB,IACLsB,EAAKkC,QACMvH,GACJ4F,OACM5F,IACToG,EAjPR,WACE,IAAIhB,EAAIC,EAAIC,EAERE,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,GACD5C,EAAQ8E,KAAKpG,EAAMqG,OAAOnC,MAC5BsB,EAAKxF,EAAMqG,OAAOnC,IAClBA,OAEAsB,EAAKrF,EACwBkF,GAAS9D,IAEpCiE,IAAOrF,IACTqF,EAAK,MAEHA,IAAOrF,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuB,EAv5BQ,IAw5BRvB,OAEAuB,EAAKtF,EACwBkF,GAAS7D,IAEpCiE,IAAOtF,GAETqF,EAAK/D,EAAQ+D,GACbD,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,GAEHoF,IAAOpF,IACLwB,EAAQyE,KAAKpG,EAAMqG,OAAOnC,MAC5BqB,EAAKvF,EAAMqG,OAAOnC,IAClBA,OAEAqB,EAAKpF,EACwBkF,GAASzD,KAI1C4C,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GA0LI2C,MACM/H,GACJ4F,OACM5F,IACTsG,EA+CZ,WACE,IAAIlB,EAAIC,EAAIC,EAAIc,EAAIC,EAAIC,EAEpBd,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAWhB,GARAP,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EAlqCU,IAmqCVtB,OAEAsB,EAAKrF,EACwBkF,GAASnD,IAEpCsD,IAAOrF,EAAY,CAuCrB,IAtCAsF,EAAK,GACDtD,EAAQiE,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAASjD,IAEpCmE,IAAOpG,IACToG,EAAKrC,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsC,EAhrCM,KAirCNtC,OAEAsC,EAAKrG,EACwBkF,GAAShD,IAEpCmE,IAAOrG,GACLH,EAAMzB,OAAS2F,IACjBuC,EAAKzG,EAAMqG,OAAOnC,IAClBA,OAEAuC,EAAKtG,EACwBkF,GAAS/C,IAEpCmE,IAAOtG,GAETqG,EAAKjE,EAAQiE,EAAIC,GACjBF,EAAKC,IAELtC,GAAcqC,EACdA,EAAKpG,KAGP+D,GAAcqC,EACdA,EAAKpG,IAGFoG,IAAOpG,GACZsF,EAAGH,KAAKiB,GACJpE,EAAQiE,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAASjD,IAEpCmE,IAAOpG,IACToG,EAAKrC,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsC,EAvtCI,KAwtCJtC,OAEAsC,EAAKrG,EACwBkF,GAAShD,IAEpCmE,IAAOrG,GACLH,EAAMzB,OAAS2F,IACjBuC,EAAKzG,EAAMqG,OAAOnC,IAClBA,OAEAuC,EAAKtG,EACwBkF,GAAS/C,IAEpCmE,IAAOtG,GAETqG,EAAKjE,EAAQiE,EAAIC,GACjBF,EAAKC,IAELtC,GAAcqC,EACdA,EAAKpG,KAGP+D,GAAcqC,EACdA,EAAKpG,IAIPsF,IAAOtF,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBqC,EAzvCM,IA0vCNrC,OAEAqC,EAAKpG,EACwBkF,GAASnD,IAEpCqE,IAAOpG,GAETqF,EAAK/C,EAAQgD,GACbF,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,EAEP,GAAIoF,IAAOpF,EAST,GARAoF,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EAvwCQ,IAwwCRtB,OAEAsB,EAAKrF,EACwBkF,GAASzC,IAEpC4C,IAAOrF,EAAY,CAuCrB,IAtCAsF,EAAK,GACD5C,EAAQuD,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAASvC,IAEpCyD,IAAOpG,IACToG,EAAKrC,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsC,EAhyCI,KAiyCJtC,OAEAsC,EAAKrG,EACwBkF,GAAShD,IAEpCmE,IAAOrG,GACLH,EAAMzB,OAAS2F,IACjBuC,EAAKzG,EAAMqG,OAAOnC,IAClBA,OAEAuC,EAAKtG,EACwBkF,GAAS/C,IAEpCmE,IAAOtG,GAETqG,EAAKjE,EAAQiE,EAAIC,GACjBF,EAAKC,IAELtC,GAAcqC,EACdA,EAAKpG,KAGP+D,GAAcqC,EACdA,EAAKpG,IAGFoG,IAAOpG,GACZsF,EAAGH,KAAKiB,GACJ1D,EAAQuD,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAASvC,IAEpCyD,IAAOpG,IACToG,EAAKrC,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsC,EAv0CE,KAw0CFtC,OAEAsC,EAAKrG,EACwBkF,GAAShD,IAEpCmE,IAAOrG,GACLH,EAAMzB,OAAS2F,IACjBuC,EAAKzG,EAAMqG,OAAOnC,IAClBA,OAEAuC,EAAKtG,EACwBkF,GAAS/C,IAEpCmE,IAAOtG,GAETqG,EAAKjE,EAAQiE,EAAIC,GACjBF,EAAKC,IAELtC,GAAcqC,EACdA,EAAKpG,KAGP+D,GAAcqC,EACdA,EAAKpG,IAIPsF,IAAOtF,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBqC,EA91CI,IA+1CJrC,OAEAqC,EAAKpG,EACwBkF,GAASzC,IAEpC2D,IAAOpG,GAETqF,EAAK/C,EAAQgD,GACbF,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAGP+D,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,EAMT,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EA9RQ4C,MACMhI,IACTsG,EA+Rd,WACE,IAAIlB,EAAIC,EAAIC,EAAIc,EAt3CK7E,EAAGc,EAER4F,EAs3CZzC,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAahB,IAVAP,EAAKrB,GACLsB,EAAKtB,GACLuB,EAAK,GACD1C,EAAQqD,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAASrC,IAEjCuD,IAAOpG,GACZsF,EAAGH,KAAKiB,GACJxD,EAAQqD,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAASrC,IAyB1C,GAtBIyC,IAAOtF,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBqC,EAj7CQ,IAk7CRrC,OAEAqC,EAAKpG,EACwBkF,GAASxD,IAEpC0E,IAAOpG,EAETqF,EADAC,EAAK,CAACA,EAAIc,IAGVrC,GAAcsB,EACdA,EAAKrF,KAGP+D,GAAcsB,EACdA,EAAKrF,GAEHqF,IAAOrF,IACTqF,EAAK,MAEHA,IAAOrF,EAAY,CASrB,GARAsF,EAAK,GACD1C,EAAQqD,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAASrC,IAEpCuD,IAAOpG,EACT,KAAOoG,IAAOpG,GACZsF,EAAGH,KAAKiB,GACJxD,EAAQqD,KAAKpG,EAAMqG,OAAOnC,MAC5BqC,EAAKvG,EAAMqG,OAAOnC,IAClBA,OAEAqC,EAAKpG,EACwBkF,GAASrC,SAI1CyC,EAAKtF,EAEHsF,IAAOtF,GAl8CWqC,EAo8CHiD,EAl8CL2C,GAFK1G,EAo8CJ8D,GAl8CqB,GAAGqB,OAAOwB,MAAM,GAAI3G,GAAG/B,KAAK,IAAM,GAk8CpE6F,EAj8Ca,CAAEhG,KAAM,UAAWwC,MAAOsG,WAAWF,EAAkB5F,EAAE7C,KAAK,MAk8C3E4F,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EA3XUgD,MACMpI,IACTsG,EA4XhB,WACE,IAAIlB,EAAIC,EAEJG,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,UAIhBN,EAAKW,QACMhG,IAETqF,EA/9C+B,CAAEhG,KAAM,UAAWwC,MA+9CrCwD,IAEfD,EAAKC,EAELhB,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GAlZYiD,IAGL/B,IAAOtG,GAETqF,EAAK1D,EAAQ0D,EAAIe,EAAIE,GACrBlB,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAeb+D,GAAcqB,EACdA,EAAKpF,GAEHoF,IAAOpF,IACToF,EAAKrB,IACLsB,EAAKkC,QACMvH,IAETqF,EA1oC8B,CAAEhG,KAAM,YAAapC,KA0oCtCoI,IAEfD,EAAKC,IAIThB,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GA1UEkD,MACMtI,GACJ4F,OACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuC,EA/1BE,IAg2BFvC,OAEAuC,EAAKtG,EACwBkF,GAAShE,IAEpCoF,IAAOtG,EAGToF,EADAC,EAAae,GAGbrC,GAAcqB,EACdA,EAAKpF,KAeb+D,GAAcqB,EACdA,EAAKpF,GAGPqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GA3KEmD,MACMvI,IACToF,EAygCR,WACE,IAAIA,EAAIC,EAAIC,EAAIc,EAAIC,EAAIC,EAAIC,EAvqDPtI,EAyqDjBuH,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAWhB,GARAP,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EApuDU,IAquDVtB,OAEAsB,EAAKrF,EACwBkF,GAASxD,IAEpC2D,IAAOrF,EAET,IADAsF,EAAKU,QACMhG,EAAY,CAuBrB,IAtBAoG,EAAK,GACLC,EAAKtC,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBuC,EAhvDM,IAivDNvC,OAEAuC,EAAKtG,EACwBkF,GAASxD,IAEpC4E,IAAOtG,IACTuG,EAAKP,QACMhG,EAETqG,EADAC,EAAK,CAACA,EAAIC,IAOZxC,GAAcsC,EACdA,EAAKrG,GAEAqG,IAAOrG,GACZoG,EAAGjB,KAAKkB,GACRA,EAAKtC,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBuC,EAvwDI,IAwwDJvC,OAEAuC,EAAKtG,EACwBkF,GAASxD,IAEpC4E,IAAOtG,IACTuG,EAAKP,QACMhG,EAETqG,EADAC,EAAK,CAACA,EAAIC,IAOZxC,GAAcsC,EACdA,EAAKrG,GAGLoG,IAAOpG,GA3uDM/B,EA6uDFqH,EAAbD,EA5uDK,CAAEhG,KAAM,QAASpC,KA4uDLmJ,EA5uDcS,QAAO,SAASC,EAAMpC,GAAI,OAAOoC,EAAOpC,EAAE,GAAKA,EAAE,KAAOzG,IA6uDvFmH,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,OAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAjmCIoD,MACMxI,IACToF,EAkmCV,WACE,IAAIA,EAAIC,EAAQe,EAAQE,EAEpBd,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,GA1wDO,UA2wDRlE,EAAM4H,OAAO1D,GAAa,IAC5BsB,EA5wDU,QA6wDVtB,IAAe,IAEfsB,EAAKrF,EACwBkF,GAAS3B,IAEpC8B,IAAOrF,GACJ4F,OACM5F,IACToG,EAAKP,QACM7F,GACJ4F,OACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuC,EAzyDE,IA0yDFvC,OAEAuC,EAAKtG,EACwBkF,GAASjC,IAEpCqD,IAAOtG,EAGToF,EADAC,EAhyDwB,CAAEhG,KAAM,MAAOyG,UAgyD1BM,IAGbrC,GAAcqB,EACdA,EAAKpF,KAeb+D,GAAcqB,EACdA,EAAKpF,GAGPqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GA/pCMqD,MACMzI,IACToF,EAgqCZ,WACE,IAAIA,EAAIC,EAAQe,EAAQE,EAEpBd,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,GAv0DO,cAw0DRlE,EAAM4H,OAAO1D,GAAa,IAC5BsB,EAz0DU,YA00DVtB,IAAe,IAEfsB,EAAKrF,EACwBkF,GAAS1B,IAEpC6B,IAAOrF,GACJ4F,OACM5F,IACToG,EAAKP,QACM7F,GACJ4F,OACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuC,EAz2DE,IA02DFvC,OAEAuC,EAAKtG,EACwBkF,GAASjC,IAEpCqD,IAAOtG,EAGToF,EADAC,EA71DwB,CAAEhG,KAAM,UAAWyG,UA61D9BM,IAGbrC,GAAcqB,EACdA,EAAKpF,KAeb+D,GAAcqB,EACdA,EAAKpF,GAGPqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GA7tCQsD,MACM1I,IACToF,EA8tCd,WACE,IAAIA,EAAIC,EAAQe,EAAQE,EAEpBd,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,GAp4DO,UAq4DRlE,EAAM4H,OAAO1D,GAAa,IAC5BsB,EAt4DU,QAu4DVtB,IAAe,IAEfsB,EAAKrF,EACwBkF,GAASzB,IAEpC4B,IAAOrF,GACJ4F,OACM5F,IACToG,EAAKP,QACM7F,GACJ4F,OACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuC,EAz6DE,IA06DFvC,OAEAuC,EAAKtG,EACwBkF,GAASjC,IAEpCqD,IAAOtG,EAGToF,EADAC,EA15DwB,CAAEhG,KAAM,MAAOyG,UA05D1BM,IAGbrC,GAAcqB,EACdA,EAAKpF,KAeb+D,GAAcqB,EACdA,EAAKpF,GAGPqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GA3xCUuD,MACM3I,IACToF,EA4xChB,WACE,IAAIA,EAAIC,EAEJG,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SA97DJ,iBAk8DR9F,EAAM4H,OAAO1D,GAAa,KAC5BsB,EAn8DU,eAo8DVtB,IAAe,KAEfsB,EAAKrF,EACwBkF,GAASxB,KAEpC2B,IAAOrF,IAETqF,EAz8D8BuD,GAAI,IA28DpCxD,EAAKC,EAELhB,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GAxzCYyD,MACM7I,IACToF,EAyzClB,WACE,IAAIA,EAAIC,EAEJG,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SA19DJ,gBA89DR9F,EAAM4H,OAAO1D,GAAa,KAC5BsB,EA/9DU,cAg+DVtB,IAAe,KAEfsB,EAAKrF,EACwBkF,GAASvB,KAEpC0B,IAAOrF,IAETqF,EAr+D8ByD,GAAQ,IAu+DxC1D,EAAKC,EAELhB,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GAr1Cc2D,MACM/I,IACToF,EAs1CpB,WACE,IAAIA,EAAIC,EAAQe,EAAIC,EAAIC,EAEpBd,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAWhB,GARAP,EAAKrB,GAz/DO,gBA0/DRlE,EAAM4H,OAAO1D,GAAa,KAC5BsB,EA3/DU,cA4/DVtB,IAAe,KAEfsB,EAAKrF,EACwBkF,GAAStB,KAEpCyB,IAAOrF,EAET,GADK4F,OACM5F,EAAY,CASrB,GARAoG,EAAK,GACDxD,EAAQqD,KAAKpG,EAAMqG,OAAOnC,MAC5BsC,EAAKxG,EAAMqG,OAAOnC,IAClBA,OAEAsC,EAAKrG,EACwBkF,GAASrC,IAEpCwD,IAAOrG,EACT,KAAOqG,IAAOrG,GACZoG,EAAGjB,KAAKkB,GACJzD,EAAQqD,KAAKpG,EAAMqG,OAAOnC,MAC5BsC,EAAKxG,EAAMqG,OAAOnC,IAClBA,OAEAsC,EAAKrG,EACwBkF,GAASrC,SAI1CuD,EAAKpG,EAEHoG,IAAOpG,IACTqG,EAAKT,QACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuC,EA5jEE,IA6jEFvC,OAEAuC,EAAKtG,EACwBkF,GAASjC,IAEpCqD,IAAOtG,GAETqF,EApiEuBuD,GAAII,SAoiEd5C,EApiEyB5G,KAAK,IAAK,KAqiEhD4F,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAOT+D,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,OAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAx6CgB6D,MACMjJ,IACToF,EAy6CtB,WACE,IAAIA,EAAIC,EAAQe,EAAIC,EAAIC,EAEpBd,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAWhB,GARAP,EAAKrB,GA3kEO,qBA4kERlE,EAAM4H,OAAO1D,GAAa,KAC5BsB,EA7kEU,mBA8kEVtB,IAAe,KAEfsB,EAAKrF,EACwBkF,GAASrB,KAEpCwB,IAAOrF,EAET,GADK4F,OACM5F,EAAY,CASrB,GARAoG,EAAK,GACDxD,EAAQqD,KAAKpG,EAAMqG,OAAOnC,MAC5BsC,EAAKxG,EAAMqG,OAAOnC,IAClBA,OAEAsC,EAAKrG,EACwBkF,GAASrC,IAEpCwD,IAAOrG,EACT,KAAOqG,IAAOrG,GACZoG,EAAGjB,KAAKkB,GACJzD,EAAQqD,KAAKpG,EAAMqG,OAAOnC,MAC5BsC,EAAKxG,EAAMqG,OAAOnC,IAClBA,OAEAsC,EAAKrG,EACwBkF,GAASrC,SAI1CuD,EAAKpG,EAEHoG,IAAOpG,IACTqG,EAAKT,QACM5F,GAC6B,KAAlCH,EAAMf,WAAWiF,KACnBuC,EAjpEE,IAkpEFvC,OAEAuC,EAAKtG,EACwBkF,GAASjC,IAEpCqD,IAAOtG,GAETqF,EAtnEuByD,GAAQE,SAsnElB5C,EAtnE6B5G,KAAK,IAAK,KAunEpD4F,EAAKC,IAELtB,GAAcqB,EACdA,EAAKpF,KAOT+D,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,OAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EA3/CkB8D,MACMlJ,IACToF,EA4/CxB,WACE,IAAIA,EAAIC,EAAIC,EAERE,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,OAAIC,GACF1B,GAAc0B,EAAOC,QAEdD,EAAOE,SAGhBP,EAAKrB,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsB,EA/pEW,IAgqEXtB,OAEAsB,EAAKrF,EACwBkF,GAASpB,KAEpCuB,IAAOrF,IACTsF,EAAKU,QACMhG,EAGToF,EADAC,EAtqEO,CAAEhG,KAAM,QAASpC,KAsqEVqI,IAOhBvB,GAAcqB,EACdA,EAAKpF,GAGPqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GAjiDoB+D,IAa3B9E,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,GAwPT,SAASmC,KACP,IAAInC,EAAIC,EAAIC,EAAIc,EAAIC,EAAIC,EAn+BH/E,EAAG4F,EAq+BpB3B,EAAuB,GAAdzB,GAAmB,GAC5B0B,EAASpB,GAAiBmB,GAE9B,GAAIC,EAGF,OAFA1B,GAAc0B,EAAOC,QAEdD,EAAOE,OAKhB,GAFAP,EAAKrB,IACLsB,EAAKW,QACMhG,EAAY,CAuBrB,IAtBAsF,EAAK,GACLc,EAAKrC,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsC,EAt/BQ,IAu/BRtC,OAEAsC,EAAKrG,EACwBkF,GAASxD,IAEpC2E,IAAOrG,IACTsG,EAAKN,QACMhG,EAEToG,EADAC,EAAK,CAACA,EAAIC,IAOZvC,GAAcqC,EACdA,EAAKpG,GAEAoG,IAAOpG,GACZsF,EAAGH,KAAKiB,GACRA,EAAKrC,GACiC,KAAlClE,EAAMf,WAAWiF,KACnBsC,EA7gCM,IA8gCNtC,OAEAsC,EAAKrG,EACwBkF,GAASxD,IAEpC2E,IAAOrG,IACTsG,EAAKN,QACMhG,EAEToG,EADAC,EAAK,CAACA,EAAIC,IAOZvC,GAAcqC,EACdA,EAAKpG,GAGLsF,IAAOtF,GA/hCQuB,EAiiCJ8D,EAjiCO8B,EAiiCH7B,EACjBF,EADAC,EAhiCS,GAAGqB,OAAOwB,MAAM,CAAC3G,GAAI4F,GAAI3H,KAAK,MAmiCvCuE,GAAcqB,EACdA,EAAKpF,QAGP+D,GAAcqB,EACdA,EAAKpF,EAKP,OAFAqE,GAAiBmB,GAAO,CAAEE,QAAS3B,GAAa4B,OAAQP,GAEjDA,EAktCP,SAASwD,GAAIQ,GAAK,MAAO,CAAE/J,KAAM,YAAagK,MAAO,CAAEhK,KAAM,UAAWwC,MAAOuH,IAC/E,SAASN,GAAQM,GAAK,MAAO,CAAE/J,KAAM,iBAAkBgK,MAAO,CAAEhK,KAAM,UAAWwC,MAAOuH,IAkB1F,IAFArJ,EAAaK,OAEMJ,GAAc+D,KAAgBlE,EAAMzB,OACrD,OAAO2B,EAMP,MAJIA,IAAeC,GAAc+D,GAAclE,EAAMzB,QACnD8G,GAnpEK,CAAE7F,KAAM,QAyEiBxC,EA8kE9BuH,GA9kEwCtH,EA+kExCqH,GAAiBtE,EAAMzB,OAASyB,EAAMqG,OAAO/B,IAAkB,KA/kEhBpH,EAglE/CoH,GAAiBtE,EAAMzB,OACnBwG,GAAoBT,GAAgBA,GAAiB,GACrDS,GAAoBT,GAAgBA,IAjlEnC,IAAIxH,EACTA,EAAgBe,aAAab,EAAUC,GACvCD,EACAC,EACAC,KAtZauM,OCyBrB,SAASC,EAAQC,EAAKC,GAClB,IAAK,IAAIxL,EAAI,EAAGA,EAAIwL,EAAKrL,SAAUH,EAAG,CAClC,GAAW,MAAPuL,EAAe,OAAOA,EAC1BA,EAAMA,EAAIC,EAAKxL,IAEnB,OAAOuL,EAyCX,IAAME,EAAmC,mBAAZC,QAAyB,IAAIA,QAAU,KASpE,SAASC,EAAWC,GAChB,GAAgB,MAAZA,EACA,OAAO,WAAA,OAAM,GAGjB,GAAqB,MAAjBH,EAAuB,CACvB,IAAII,EAAUJ,EAAcK,IAAIF,GAChC,OAAe,MAAXC,IAGJA,EAAUE,EAAgBH,GAC1BH,EAAcO,IAAIJ,EAAUC,IAHjBA,EAOf,OAAOE,EAAgBH,GAQ3B,SAASG,EAAgBH,GACrB,OAAOA,EAASxK,MACZ,IAAK,WACD,OAAO,WAAA,OAAM,GAEjB,IAAK,aACD,IAAMwC,EAAQgI,EAAShI,MAAMqI,cAC7B,OAAO,SAACC,EAAMC,EAAUtK,GACpB,IAAMuK,EAAevK,GAAWA,EAAQuK,aAAgB,OACxD,OAAOxI,IAAUsI,EAAKE,GAAaH,eAI3C,IAAK,QACD,IAAMI,EAAOT,EAAS5M,KAAKsN,MAAM,KACjC,OAAO,SAACJ,EAAMC,GAEV,OA9EhB,SAASI,EAAOL,EAAMM,EAAUH,EAAMI,GAElC,IADA,IAAIC,EAAUF,EACLxM,EAAIyM,EAAezM,EAAIqM,EAAKlM,SAAUH,EAAG,CAC9C,GAAe,MAAX0M,EACA,OAAO,EAEX,IAAMC,EAAQD,EAAQL,EAAKrM,IAC3B,GAAII,MAAMwM,QAAQD,GAAQ,CACtB,IAAK,IAAIE,EAAI,EAAGA,EAAIF,EAAMxM,SAAU0M,EAChC,GAAIN,EAAOL,EAAMS,EAAME,GAAIR,EAAMrM,EAAI,GACjC,OAAO,EAGf,OAAO,EAEX0M,EAAUC,EAEd,OAAOT,IAASQ,EA6DGH,CAAOL,EADGC,EAASE,EAAKlM,OAAS,GACVkM,EAAM,IAI5C,IAAK,UACD,IAAMS,EAAWlB,EAAS/D,UAAUa,IAAIiD,GACxC,OAAO,SAACO,EAAMC,EAAUtK,GACpB,IAAK,IAAI7B,EAAI,EAAGA,EAAI8M,EAAS3M,SAAUH,EACnC,GAAI8M,EAAS9M,GAAGkM,EAAMC,EAAUtK,GAAY,OAAO,EAEvD,OAAO,GAIf,IAAK,WACD,IAAMiL,EAAWlB,EAAS/D,UAAUa,IAAIiD,GACxC,OAAO,SAACO,EAAMC,EAAUtK,GACpB,IAAK,IAAI7B,EAAI,EAAGA,EAAI8M,EAAS3M,SAAUH,EACnC,IAAK8M,EAAS9M,GAAGkM,EAAMC,EAAUtK,GAAY,OAAO,EAExD,OAAO,GAIf,IAAK,MACD,IAAMiL,EAAWlB,EAAS/D,UAAUa,IAAIiD,GACxC,OAAO,SAACO,EAAMC,EAAUtK,GACpB,IAAK,IAAI7B,EAAI,EAAGA,EAAI8M,EAAS3M,SAAUH,EACnC,GAAI8M,EAAS9M,GAAGkM,EAAMC,EAAUtK,GAAY,OAAO,EAEvD,OAAO,GAIf,IAAK,MACD,IAAMiL,EAAWlB,EAAS/D,UAAUa,IAAIiD,GACxC,OAAO,SAACO,EAAMC,EAAUtK,GACpB,IAAI6F,GAAS,EAEPpE,EAAI,GAkBV,OAjBAyJ,EAAWC,SAASd,EAAM,CACtBe,eAAOf,EAAM9M,GACK,MAAVA,GAAkBkE,EAAE4J,QAAQ9N,GAEhC,IAAK,IAAIY,EAAI,EAAGA,EAAI8M,EAAS3M,SAAUH,EACnC,GAAI8M,EAAS9M,GAAGkM,EAAM5I,EAAGzB,GAGrB,OAFA6F,GAAS,OACT3I,cAKZoO,iBAAW7J,EAAE8J,SACb5B,KAAM3J,GAAWA,EAAQwL,YACzBC,SAAUzL,GAAWA,EAAQyL,UAAY,cAGtC5F,GAIf,IAAK,QACD,IAAMqB,EAAO4C,EAAWC,EAAS7C,MAC3BC,EAAQ2C,EAAWC,EAAS5C,OAClC,OAAO,SAACkD,EAAMC,EAAUtK,GACpB,SAAIsK,EAAShM,OAAS,GAAK6I,EAAMkD,EAAMC,EAAUtK,KACtCkH,EAAKoD,EAAS,GAAIA,EAAS7K,MAAM,GAAIO,IAMxD,IAAK,aACD,IAAMkH,EAAO4C,EAAWC,EAAS7C,MAC3BC,EAAQ2C,EAAWC,EAAS5C,OAClC,OAAO,SAACkD,EAAMC,EAAUtK,GACpB,GAAImH,EAAMkD,EAAMC,EAAUtK,GACtB,IAAK,IAAI7B,EAAI,EAAGuN,EAAIpB,EAAShM,OAAQH,EAAIuN,IAAKvN,EAC1C,GAAI+I,EAAKoD,EAASnM,GAAImM,EAAS7K,MAAMtB,EAAI,GAAI6B,GACzC,OAAO,EAInB,OAAO,GAIf,IAAK,YACD,IAAMwK,EAAOT,EAAS5M,KAAKsN,MAAM,KACjC,OAAQV,EAAS/H,UACb,UAAK,EACD,OAAO,SAACqI,GAAI,OAA4B,MAAvBZ,EAAQY,EAAMG,IACnC,IAAK,IACD,OAAQT,EAAShI,MAAMxC,MACnB,IAAK,SACD,OAAO,SAAC8K,GACJ,IAAMzF,EAAI6E,EAAQY,EAAMG,GACxB,MAAoB,iBAAN5F,GAAkBmF,EAAShI,MAAMA,MAAMoE,KAAKvB,IAElE,IAAK,UACD,IAAM9G,YAAaiM,EAAShI,MAAMA,OAClC,OAAO,SAACsI,GAAI,OAAKvM,cAAe2L,EAAQY,EAAMG,KAElD,IAAK,OACD,OAAO,SAACH,GAAI,OAAKN,EAAShI,MAAMA,UAAiB0H,EAAQY,EAAMG,KAEvE,MAAM,IAAIpN,6CAAsC2M,EAAShI,MAAMxC,OACnE,IAAK,KACD,OAAQwK,EAAShI,MAAMxC,MACnB,IAAK,SACD,OAAO,SAAC8K,GAAI,OAAMN,EAAShI,MAAMA,MAAMoE,KAAKsD,EAAQY,EAAMG,KAC9D,IAAK,UACD,IAAM1M,YAAaiM,EAAShI,MAAMA,OAClC,OAAO,SAACsI,GAAI,OAAKvM,cAAe2L,EAAQY,EAAMG,KAElD,IAAK,OACD,OAAO,SAACH,GAAI,OAAKN,EAAShI,MAAMA,UAAiB0H,EAAQY,EAAMG,KAEvE,MAAM,IAAIpN,6CAAsC2M,EAAShI,MAAMxC,OACnE,IAAK,KACD,OAAO,SAAC8K,GAAI,OAAKZ,EAAQY,EAAMG,IAAST,EAAShI,MAAMA,OAC3D,IAAK,IACD,OAAO,SAACsI,GAAI,OAAKZ,EAAQY,EAAMG,GAAQT,EAAShI,MAAMA,OAC1D,IAAK,IACD,OAAO,SAACsI,GAAI,OAAKZ,EAAQY,EAAMG,GAAQT,EAAShI,MAAMA,OAC1D,IAAK,KACD,OAAO,SAACsI,GAAI,OAAKZ,EAAQY,EAAMG,IAAST,EAAShI,MAAMA,OAE/D,MAAM,IAAI3E,kCAA2B2M,EAAS/H,WAGlD,IAAK,UACD,IAAMkF,EAAO4C,EAAWC,EAAS7C,MAC3BC,EAAQ2C,EAAWC,EAAS5C,OAClC,OAAO,SAACkD,EAAMC,EAAUtK,GAAO,OAC3BmH,EAAMkD,EAAMC,EAAUtK,IAClB2L,EAAQtB,EAAMnD,EAAMoD,EAjQtB,YAiQ2CtK,IACzC+J,EAAS7C,KAAKE,SACdF,EAAKmD,EAAMC,EAAUtK,IACrB2L,EAAQtB,EAAMlD,EAAOmD,EAnQtB,aAmQ4CtK,IAGvD,IAAK,WACD,IAAMkH,EAAO4C,EAAWC,EAAS7C,MAC3BC,EAAQ2C,EAAWC,EAAS5C,OAClC,OAAO,SAACkD,EAAMC,EAAUtK,GAAO,OAC3BmH,EAAMkD,EAAMC,EAAUtK,IAClB4L,EAASvB,EAAMnD,EAAMoD,EA5QvB,YA4Q4CtK,IAC1C+J,EAAS5C,MAAMC,SACfF,EAAKmD,EAAMC,EAAUtK,IACrB4L,EAASvB,EAAMlD,EAAOmD,EA9QvB,aA8Q6CtK,IAGxD,IAAK,YACD,IAAM8I,EAAMiB,EAASR,MAAMxH,MACrBoF,EAAQ2C,EAAWC,EAAS5C,OAClC,OAAO,SAACkD,EAAMC,EAAUtK,GAAO,OAC3BmH,EAAMkD,EAAMC,EAAUtK,IAClB6L,EAASxB,EAAMC,EAAUxB,EAAK9I,IAG1C,IAAK,iBACD,IAAM8I,GAAOiB,EAASR,MAAMxH,MACtBoF,EAAQ2C,EAAWC,EAAS5C,OAClC,OAAO,SAACkD,EAAMC,EAAUtK,GAAO,OAC3BmH,EAAMkD,EAAMC,EAAUtK,IAClB6L,EAASxB,EAAMC,EAAUxB,EAAK9I,IAG1C,IAAK,QAED,OAAO,SAACqK,EAAMC,EAAUtK,GAEpB,GAAIA,GAAWA,EAAQ8L,WACnB,OAAO9L,EAAQ8L,WAAW/B,EAAS5M,KAAMkN,EAAMC,GAGnD,GAAItK,GAAWA,EAAQuK,YAAa,OAAO,EAI3C,OAFaR,EAAS5M,KAAKiN,eAGvB,IAAK,YACD,GAA2B,cAAxBC,EAAK9K,KAAKE,OAAO,GAAoB,OAAO,EAEnD,IAAK,cACD,MAAgC,gBAAzB4K,EAAK9K,KAAKE,OAAO,IAC5B,IAAK,UACD,GAA2B,YAAxB4K,EAAK9K,KAAKE,OAAO,GAAkB,OAAO,EAEjD,IAAK,aACD,MAAgC,eAAzB4K,EAAK9K,KAAKE,OAAO,KACI,YAAxB4K,EAAK9K,KAAKE,OAAO,IAEC,eAAd4K,EAAK9K,OACgB,IAApB+K,EAAShM,QAAqC,iBAArBgM,EAAS,GAAG/K,OAE5B,iBAAd8K,EAAK9K,KACb,IAAK,WACD,MAAqB,wBAAd8K,EAAK9K,MACM,uBAAd8K,EAAK9K,MACS,4BAAd8K,EAAK9K,KAEjB,MAAM,IAAInC,oCAA6B2M,EAAS5M,QAK5D,MAAM,IAAIC,uCAAgC2M,EAASxK,OAkDvD,SAASwM,EAAe1B,EAAMrK,GAC1B,IAAMuK,EAAevK,GAAWA,EAAQuK,aAAgB,OAElDyB,EAAW3B,EAAKE,GACtB,OAAIvK,GAAWA,EAAQwL,aAAexL,EAAQwL,YAAYQ,GAC/ChM,EAAQwL,YAAYQ,GAE3Bd,EAAWe,YAAYD,GAChBd,EAAWe,YAAYD,GAE9BhM,GAAuC,mBAArBA,EAAQyL,SACnBzL,EAAQyL,SAASpB,GAGrB6B,OAAOvC,KAAKU,GAAM8B,QAAO,SAAUzG,GACtC,OAAOA,IAAQ6E,KAWvB,SAAS6B,EAAO/B,EAAMrK,GAClB,IAAMuK,EAAevK,GAAWA,EAAQuK,aAAgB,OACxD,OAAgB,OAATF,GAAiC,WAAhBgC,EAAOhC,IAAkD,iBAAtBA,EAAKE,GAapE,SAASoB,EAAQtB,EAAML,EAASM,EAAUgC,EAAMtM,GAC5C,IAAOzC,IAAU+M,QACjB,IAAK/M,EAAU,OAAO,EAEtB,IADA,IAAMoM,EAAOoC,EAAexO,EAAQyC,GAC3B7B,EAAI,EAAGA,EAAIwL,EAAKrL,SAAUH,EAAG,CAClC,IAAMoO,EAAWhP,EAAOoM,EAAKxL,IAC7B,GAAII,MAAMwM,QAAQwB,GAAW,CACzB,IAAMC,EAAaD,EAASE,QAAQpC,GACpC,GAAImC,EAAa,EAAK,SACtB,IAAIE,SAAYC,SA7aV,cA8aFL,GACAI,EAAa,EACbC,EAAaH,IAEbE,EAAaF,EAAa,EAC1BG,EAAaJ,EAASjO,QAE1B,IAAK,IAAI0M,EAAI0B,EAAY1B,EAAI2B,IAAc3B,EACvC,GAAIoB,EAAOG,EAASvB,GAAIhL,IAAYgK,EAAQuC,EAASvB,GAAIV,EAAUtK,GAC/D,OAAO,GAKvB,OAAO,EAaX,SAAS4L,EAASvB,EAAML,EAASM,EAAUgC,EAAMtM,GAC7C,IAAOzC,IAAU+M,QACjB,IAAK/M,EAAU,OAAO,EAEtB,IADA,IAAMoM,EAAOoC,EAAexO,EAAQyC,GAC3B7B,EAAI,EAAGA,EAAIwL,EAAKrL,SAAUH,EAAG,CAClC,IAAMoO,EAAWhP,EAAOoM,EAAKxL,IAC7B,GAAII,MAAMwM,QAAQwB,GAAW,CACzB,IAAMK,EAAML,EAASE,QAAQpC,GAC7B,GAAIuC,EAAM,EAAK,SACf,GAldM,cAkdFN,GAAsBM,EAAM,GAAKR,EAAOG,EAASK,EAAM,GAAI5M,IAAYgK,EAAQuC,EAASK,EAAM,GAAItC,EAAUtK,GAC5G,OAAO,EAEX,GApdO,eAodHsM,GAAuBM,EAAML,EAASjO,OAAS,GAAK8N,EAAOG,EAASK,EAAM,GAAI5M,IAAagK,EAAQuC,EAASK,EAAM,GAAItC,EAAUtK,GAChI,OAAO,GAInB,OAAO,EAaX,SAAS6L,EAASxB,EAAMC,EAAUxB,EAAK9I,GACnC,GAAY,IAAR8I,EAAa,OAAO,EACxB,IAAOvL,IAAU+M,QACjB,IAAK/M,EAAU,OAAO,EAEtB,IADA,IAAMoM,EAAOoC,EAAexO,EAAQyC,GAC3B7B,EAAI,EAAGA,EAAIwL,EAAKrL,SAAUH,EAAG,CAClC,IAAMoO,EAAWhP,EAAOoM,EAAKxL,IAC7B,GAAII,MAAMwM,QAAQwB,GAAU,CACxB,IAAMK,EAAM9D,EAAM,EAAIyD,EAASjO,OAASwK,EAAMA,EAAM,EACpD,GAAI8D,GAAO,GAAKA,EAAML,EAASjO,QAAUiO,EAASK,KAASvC,EACvD,OAAO,GAInB,OAAO,EAuCX,SAASc,EAAS0B,EAAK9C,EAAU+C,EAAS9M,GACtC,GAAK+J,EAAL,CACA,IAAMO,EAAW,GACXN,EAAUF,EAAWC,GACrBgD,EAjCV,SAASC,EAASjD,EAAUY,GACxB,GAAgB,MAAZZ,GAAuC,UAAnBsC,EAAOtC,GAAwB,MAAO,GAC9C,MAAZY,IAAoBA,EAAWZ,GAGnC,IAFA,IAAMkD,EAAUlD,EAAS3C,QAAU,CAACuD,GAAY,GAC1ChB,EAAOuC,OAAOvC,KAAKI,GAChB5L,EAAI,EAAGA,EAAIwL,EAAKrL,SAAUH,EAAG,CAClC,IAAMyG,EAAI+E,EAAKxL,GACT+O,EAAMnD,EAASnF,GACrBqI,EAAQ5H,WAAR4H,IAAgBD,EAASE,EAAW,SAANtI,EAAesI,EAAMvC,KAEvD,OAAOsC,EAuBaD,CAASjD,GAAUlD,IAAIiD,GAC3CoB,EAAWC,SAAS0B,EAAK,CACrBzB,eAAOf,EAAM9M,GAET,GADc,MAAVA,GAAkB+M,EAASe,QAAQ9N,GACnCyM,EAAQK,EAAMC,EAAUtK,GACxB,GAAI+M,EAAYzO,OACZ,IAAK,IAAIH,EAAI,EAAGuN,EAAIqB,EAAYzO,OAAQH,EAAIuN,IAAKvN,EAAG,CAC5C4O,EAAY5O,GAAGkM,EAAMC,EAAUtK,IAC/B8M,EAAQzC,EAAM9M,EAAQ+M,GAE1B,IAAK,IAAIU,EAAI,EAAGmC,EAAI7C,EAAShM,OAAQ0M,EAAImC,IAAKnC,EAAG,CAC7C,IAAMoC,EAAqB9C,EAAS7K,MAAMuL,EAAI,GAC1C+B,EAAY5O,GAAGmM,EAASU,GAAIoC,EAAoBpN,IAChD8M,EAAQxC,EAASU,GAAIzN,EAAQ6P,SAKzCN,EAAQzC,EAAM9M,EAAQ+M,IAIlCgB,iBAAWhB,EAASiB,SACpB5B,KAAM3J,GAAWA,EAAQwL,YACzBC,SAAUzL,GAAWA,EAAQyL,UAAY,eAajD,SAAS/I,EAAMmK,EAAK9C,EAAU/J,GAC1B,IAAMiN,EAAU,GAIhB,OAHA9B,EAAS0B,EAAK9C,GAAU,SAAUM,GAC9B4C,EAAQ5H,KAAKgF,KACdrK,GACIiN,EAQX,SAASnN,EAAMiK,GACX,OAAOsD,EAAOvN,MAAMiK,GAUxB,SAASuD,EAAMT,EAAK9C,EAAU/J,GAC1B,OAAO0C,EAAMmK,EAAK/M,EAAMiK,GAAW/J,UAGvCsN,EAAMxN,MAAQA,EACdwN,EAAM5K,MAAQA,EACd4K,EAAMnC,SAAWA,EACjBmC,EAAMC,QAvPN,SAAiBlD,EAAMN,EAAUO,EAAUtK,GACvC,OAAK+J,KACAM,IACAC,IAAYA,EAAW,IAErBR,EAAWC,EAAXD,CAAqBO,EAAMC,EAAUtK,KAmPhDsN,EAAMA,MAAQA"}