{"_args": [["express@4.18.2", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "express@4.18.2", "_id": "express@4.18.2", "_inBundle": false, "_integrity": "sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==", "_location": "/express", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "express@4.18.2", "name": "express", "escapedName": "express", "rawSpec": "4.18.2", "saveSpec": null, "fetchSpec": "4.18.2"}, "_requiredBy": ["/webpack-dev-server"], "_resolved": "https://registry.npmjs.org/express/-/express-4.18.2.tgz", "_spec": "4.18.2", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/expressjs/express/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> <PERSON>", "email": "<EMAIL>"}], "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.1", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.5.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.11.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "description": "Fast, unopinionated, minimalist web framework", "devDependencies": {"after": "0.8.2", "connect-redis": "3.4.2", "cookie-parser": "1.4.6", "cookie-session": "2.0.0", "ejs": "3.1.8", "eslint": "8.24.0", "express-session": "1.17.2", "hbs": "4.2.0", "marked": "0.7.0", "method-override": "3.0.0", "mocha": "10.0.0", "morgan": "1.10.0", "multiparty": "4.2.3", "nyc": "15.1.0", "pbkdf2-password": "1.2.1", "supertest": "6.3.0", "vhost": "~3.0.2"}, "engines": {"node": ">= 0.10.0"}, "files": ["LICENSE", "History.md", "Readme.md", "index.js", "lib/"], "homepage": "http://expressjs.com/", "keywords": ["express", "framework", "sinatra", "web", "http", "rest", "restful", "router", "app", "api"], "license": "MIT", "name": "express", "repository": {"type": "git", "url": "git+https://github.com/expressjs/express.git"}, "scripts": {"lint": "eslint .", "test": "mocha --require test/support/env --reporter spec --bail --check-leaks test/ test/acceptance/", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-tap": "mocha --require test/support/env --reporter tap --check-leaks test/ test/acceptance/"}, "version": "4.18.2"}