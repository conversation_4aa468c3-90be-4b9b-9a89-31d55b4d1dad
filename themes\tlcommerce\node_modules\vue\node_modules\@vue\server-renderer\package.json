{"_args": [["@vue/server-renderer@3.2.36", "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce"]], "_from": "@vue/server-renderer@3.2.36", "_id": "@vue/server-renderer@3.2.36", "_inBundle": false, "_integrity": "sha512-uZE0+jfye6yYXWvAQYeHZv+f50sRryvy16uiqzk3jn8hEY8zTjI+rzlmZSGoE915k+W/Ol9XSw6vxOUD8dGkUg==", "_location": "/vue/@vue/server-renderer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/server-renderer@3.2.36", "name": "@vue/server-renderer", "escapedName": "@vue%2fserver-renderer", "scope": "@vue", "rawSpec": "3.2.36", "saveSpec": null, "fetchSpec": "3.2.36"}, "_requiredBy": ["/vue"], "_resolved": "https://registry.npmjs.org/@vue/server-renderer/-/server-renderer-3.2.36.tgz", "_spec": "3.2.36", "_where": "/home/<USER>/server/lampp8.1/htdocs/TLcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueServerR<PERSON><PERSON>", "formats": ["esm-bundler", "cjs"]}, "dependencies": {"@vue/compiler-ssr": "3.2.36", "@vue/shared": "3.2.36"}, "description": "@vue/server-renderer", "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/server-renderer#readme", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/server-renderer.esm-bundler.js", "name": "@vue/server-renderer", "peerDependencies": {"vue": "3.2.36"}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/server-renderer"}, "types": "dist/server-renderer.d.ts", "version": "3.2.36"}