{"_args": [["esquery@1.5.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "esquery@1.5.0", "_id": "esquery@1.5.0", "_inBundle": false, "_integrity": "sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==", "_location": "/esquery", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "esquery@1.5.0", "name": "esquery", "escapedName": "esquery", "rawSpec": "1.5.0", "saveSpec": null, "fetchSpec": "1.5.0"}, "_requiredBy": ["/eslint", "/vue-eslint-parser"], "_resolved": "https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz", "_spec": "1.5.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/estools/esquery/issues"}, "contributors": [], "dependencies": {"estraverse": "^5.1.0"}, "description": "A query library for ECMAScript AST using a CSS selector like query language.", "devDependencies": {"@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "@babel/register": "^7.9.0", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-json": "^4.0.2", "@rollup/plugin-node-resolve": "^7.1.3", "babel-plugin-transform-es2017-object-entries": "0.0.5", "chai": "4.2.0", "eslint": "^6.8.0", "esprima": "~4.0.1", "mocha": "7.1.1", "nyc": "^15.0.1", "pegjs": "~0.10.0", "rollup": "^1.32.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0"}, "engines": {"node": ">=0.10"}, "files": ["dist/*.js", "dist/*.map", "parser.js", "license.txt", "README.md"], "homepage": "https://github.com/estools/esquery/", "keywords": ["ast", "ecmascript", "javascript", "query"], "license": "BSD-3-<PERSON><PERSON>", "main": "dist/esquery.min.js", "module": "dist/esquery.esm.min.js", "name": "esquery", "nyc": {"branches": 100, "lines": 100, "functions": 100, "statements": 100, "reporter": ["html", "text"], "exclude": ["parser.js", "dist", "tests"]}, "repository": {"type": "git", "url": "git+https://github.com/estools/esquery.git"}, "scripts": {"build": "npm run build:parser && npm run build:browser", "build:browser": "rollup -c", "build:parser": "rm parser.js && pegjs --cache --format umd -o \"parser.js\" \"grammar.pegjs\"", "lint": "eslint .", "mocha": "mocha --require chai/register-assert --require @babel/register tests", "prepublishOnly": "npm run build && npm test", "test": "nyc npm run mocha && npm run lint", "test:ci": "npm run mocha"}, "version": "1.5.0"}