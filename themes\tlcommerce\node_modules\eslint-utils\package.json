{"_args": [["eslint-utils@3.0.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "eslint-utils@3.0.0", "_id": "eslint-utils@3.0.0", "_inBundle": false, "_integrity": "sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==", "_location": "/eslint-utils", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "eslint-utils@3.0.0", "name": "eslint-utils", "escapedName": "eslint-utils", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/eslint-plugin-vue"], "_resolved": "https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz", "_spec": "3.0.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/mysticatea/eslint-utils/issues"}, "dependencies": {"eslint-visitor-keys": "^2.0.0"}, "description": "Utilities for ESLint plugins.", "devDependencies": {"@mysticatea/eslint-plugin": "^13.0.0", "codecov": "^3.6.1", "dot-prop": "^4.2.0", "eslint": "^7.24.0", "esm": "^3.2.25", "espree": "github:eslint/espree#1c744b3a602b783926344811a9459b92afe57444", "mocha": "^6.2.2", "npm-run-all": "^4.1.5", "nyc": "^14.1.1", "opener": "^1.5.1", "prettier": "~2.3.0", "rimraf": "^3.0.0", "rollup": "^1.25.0", "rollup-plugin-sourcemaps": "^0.4.2", "semver": "^7.3.2", "vuepress": "^1.2.0", "warun": "^1.0.0"}, "engines": {"node": "^10.0.0 || ^12.0.0 || >= 14.0.0"}, "exports": {".": {"import": "./index.mjs", "require": "./index.js"}, "./package.json": "./package.json"}, "files": ["index.*"], "funding": "https://github.com/sponsors/mysticatea", "homepage": "https://github.com/mysticatea/eslint-utils#readme", "keywords": ["eslint"], "license": "MIT", "main": "index", "module": "index.mjs", "name": "eslint-utils", "peerDependencies": {"eslint": ">=5"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/eslint-utils.git"}, "scripts": {"build": "rollup -c", "clean": "rimraf .nyc_output coverage index.*", "codecov": "nyc report -r lcovonly && codecov", "coverage": "opener ./coverage/lcov-report/index.html", "docs:build": "vuepress build docs", "docs:watch": "vuepress dev docs", "format": "npm run -s format:prettier -- --write", "format:prettier": "prettier docs/.vuepress/config.js src/**/*.js test/**/*.js rollup.config.js .vscode/*.json *.json .github/**/*.yml *.yml docs/**/*.md *.md", "lint": "eslint docs/.vuepress/config.js src test rollup.config.js", "postversion": "git push && git push --tags", "prebuild": "npm run -s clean", "preversion": "npm test && npm run -s build", "prewatch": "npm run -s clean", "test": "run-s \"format:prettier -- --check\" lint build test:mocha", "test:mocha": "nyc mocha --reporter dot \"test/*.js\"", "watch": "warun \"{src,test}/**/*.js\" -- npm run -s test:mocha"}, "sideEffects": false, "version": "3.0.0"}