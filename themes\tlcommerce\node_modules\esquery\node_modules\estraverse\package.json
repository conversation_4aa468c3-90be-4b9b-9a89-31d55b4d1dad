{"_args": [["estraverse@5.3.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "estraverse@5.3.0", "_id": "estraverse@5.3.0", "_inBundle": false, "_integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==", "_location": "/esquery/estraverse", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "estraverse@5.3.0", "name": "estraverse", "escapedName": "estraverse", "rawSpec": "5.3.0", "saveSpec": null, "fetchSpec": "5.3.0"}, "_requiredBy": ["/esquery"], "_resolved": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "_spec": "5.3.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "bugs": {"url": "https://github.com/estools/estraverse/issues"}, "description": "ECMAScript JS AST traversal functions", "devDependencies": {"babel-preset-env": "^1.6.1", "babel-register": "^6.3.13", "chai": "^2.1.1", "espree": "^1.11.0", "gulp": "^3.8.10", "gulp-bump": "^0.2.2", "gulp-filter": "^2.0.0", "gulp-git": "^1.0.1", "gulp-tag-version": "^1.3.0", "jshint": "^2.5.6", "mocha": "^2.1.0"}, "engines": {"node": ">=4.0"}, "homepage": "https://github.com/estools/estraverse", "license": "BSD-2-<PERSON><PERSON>", "main": "estraverse.js", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://github.com/Constellation"}], "name": "estraverse", "repository": {"type": "git", "url": "git+ssh://**************/estools/estraverse.git"}, "scripts": {"lint": "jshint estraverse.js", "test": "npm run-script lint && npm run-script unit-test", "unit-test": "mocha --compilers js:babel-register"}, "version": "5.3.0"}