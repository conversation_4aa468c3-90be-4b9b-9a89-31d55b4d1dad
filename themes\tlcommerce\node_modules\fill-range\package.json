{"_args": [["fill-range@7.0.1", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_from": "fill-range@7.0.1", "_id": "fill-range@7.0.1", "_inBundle": false, "_integrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==", "_location": "/fill-range", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "fill-range@7.0.1", "name": "fill-range", "escapedName": "fill-range", "rawSpec": "7.0.1", "saveSpec": null, "fetchSpec": "7.0.1"}, "_requiredBy": ["/braces"], "_resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz", "_spec": "7.0.1", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/fill-range/issues"}, "contributors": [{"name": "<PERSON>", "url": "edo.rivai.nl"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "paulmillr.com"}, {"name": "<PERSON><PERSON><PERSON>", "url": "www.rouvenwessling.de"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "dependencies": {"to-regex-range": "^5.0.1"}, "description": "Fill in a range of numbers or letters, optionally passing an increment or `step` to use, or create a regex-compatible range with `options.toRegex`", "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^6.1.1"}, "engines": {"node": ">=8"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/fill-range", "keywords": ["alpha", "alphabetical", "array", "bash", "brace", "expand", "expansion", "fill", "glob", "match", "matches", "matching", "number", "numerical", "range", "ranges", "regex", "sh"], "license": "MIT", "main": "index.js", "name": "fill-range", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/fill-range.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}}, "version": "7.0.1"}