{"_args": [["@vue/compiler-sfc@2.7.14", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_from": "@vue/compiler-sfc@2.7.14", "_id": "@vue/compiler-sfc@2.7.14", "_inBundle": false, "_integrity": "sha512-aNmNHyLPsw+sVvlQFQ2/8sjNuLtK54TC6cuKnVzAY93ks4ZBrvwQSnkkIh7bsbNhum5hJBS00wSDipQ937f5DA==", "_location": "/vue-cool-lightbox/@vue/compiler-sfc", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-sfc@2.7.14", "name": "@vue/compiler-sfc", "escapedName": "@vue%2fcompiler-sfc", "scope": "@vue", "rawSpec": "2.7.14", "saveSpec": null, "fetchSpec": "2.7.14"}, "_requiredBy": ["/vue-cool-lightbox/vue"], "_resolved": "https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-2.7.14.tgz", "_spec": "2.7.14", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "dependencies": {"@babel/parser": "^7.18.4", "postcss": "^8.4.14", "source-map": "^0.6.1"}, "description": "compiler-sfc for Vue 2", "devDependencies": {"@babel/types": "^7.19.4", "@types/estree": "^0.0.48", "@types/hash-sum": "^1.0.0", "@types/lru-cache": "^5.1.1", "@vue/consolidate": "^0.17.3", "de-indent": "^1.0.2", "estree-walker": "^2.0.2", "hash-sum": "^2.0.0", "less": "^4.1.3", "lru-cache": "^5.1.1", "magic-string": "^0.25.9", "merge-source-map": "^1.1.0", "postcss-modules": "^4.3.1", "postcss-selector-parser": "^6.0.10", "pug": "^3.0.2", "sass": "^1.52.3", "stylus": "^0.58.1"}, "files": ["dist"], "main": "dist/compiler-sfc.js", "name": "@vue/compiler-sfc", "types": "dist/compiler-sfc.d.ts", "version": "2.7.14"}