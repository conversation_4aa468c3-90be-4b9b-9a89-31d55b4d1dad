{"_args": [["ext@1.7.0", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "ext@1.7.0", "_id": "ext@1.7.0", "_inBundle": false, "_integrity": "sha512-6hxeJYaL110a9b5TEJSj0gojyHQAmA2ch5Os+ySCiA1QGdS697XWY1pzsrSjqA9LDEEgdB/KypIlR59RcLuHYw==", "_location": "/ext", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ext@1.7.0", "name": "ext", "escapedName": "ext", "rawSpec": "1.7.0", "saveSpec": null, "fetchSpec": "1.7.0"}, "_requiredBy": ["/es6-symbol"], "_resolved": "https://registry.npmjs.org/ext/-/ext-1.7.0.tgz", "_spec": "1.7.0", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.medikoo.com/"}, "bugs": {"url": "https://github.com/medikoo/es5-ext/issues"}, "dependencies": {"type": "^2.7.2"}, "description": "JavaScript utilities with respect to emerging standard", "devDependencies": {"chai": "^4.3.6", "eslint": "^8.23.0", "eslint-config-medikoo": "^4.1.2", "git-list-updated": "^1.2.1", "github-release-from-cc-changelog": "^2.3.0", "husky": "^4.3.8", "lint-staged": "^13.0.3", "mocha": "^6.2.3", "nyc": "^15.1.0", "prettier-elastic": "^2.2.1", "sinon": "^8.1.1", "timers-ext": "^0.1.7"}, "eslintConfig": {"extends": "medikoo/es3", "root": true, "overrides": [{"files": "global-this/implementation.js", "globals": {"__global__": true, "self": true, "window": true}, "rules": {"no-extend-native": "off", "strict": "off"}}, {"files": ["global-this/is-implemented.js", "global-this/index.js"], "globals": {"globalThis": true}}, {"files": "string_/camel-to-hyphen.js", "rules": {"id-length": "off"}}, {"files": "test/**/*.js", "env": {"mocha": true}}, {"files": ["test/promise/limit.js", "test/thenable_/finally.js"], "globals": {"Promise": true}}]}, "eslintIgnore": ["_es5-ext"], "homepage": "https://github.com/medikoo/es5-ext/tree/ext#readme", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "keywords": ["ecmascript", "es", "es6", "extensions", "ext", "addons", "lodash", "extras", "harmony", "javascript", "polyfill", "shim", "util", "utils", "utilities"], "license": "ISC", "lint-staged": {"*.js": ["eslint"], "*.{css,html,js,json,md,yaml,yml}": ["prettier -c"]}, "mocha": {"recursive": true}, "name": "ext", "nyc": {"all": true, "exclude": [".github", "_es5-ext", "coverage/**", "test/**", "*.config.js"], "reporter": ["lcov", "html", "text-summary"]}, "prettier": {"printWidth": 100, "tabWidth": 4, "overrides": [{"files": ["*.md", "*.yml"], "options": {"tabWidth": 2}}]}, "repository": {"type": "git", "url": "git+https://github.com/medikoo/es5-ext.git#ext"}, "scripts": {"coverage": "nyc npm test", "lint": "eslint .", "lint:updated": "pipe-git-updated --ext=js -- eslint --ignore-pattern '!*'", "prettier-check": "prettier -c --ignore-path .gitignore \"**/*.{css,html,js,json,md,yaml,yml}\"", "prettier-check:updated": "pipe-git-updated --ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier -c", "prettify": "prettier --write --ignore-path .gitignore '**/*.{css,html,js,json,md,yaml,yml}'", "prettify:updated": "pipe-git-updated ---base=main -ext=css --ext=html --ext=js --ext=json --ext=md --ext=yaml --ext=yml -- prettier --write", "test": "mocha"}, "version": "1.7.0"}