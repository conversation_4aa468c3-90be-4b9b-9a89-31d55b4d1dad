{"_args": [["vue-style-loader@4.1.3", "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce"]], "_development": true, "_from": "vue-style-loader@4.1.3", "_id": "vue-style-loader@4.1.3", "_inBundle": false, "_integrity": "sha512-sFuh0xfbtpRlKfm39ss/ikqs9AbKCoXZBpHeVZ8Tx650o0k0q/YCM7FRvigtxpACezfq6af+a7JeqVTWvncqDg==", "_location": "/vue-style-loader", "_phantomChildren": {"big.js": "5.2.2", "emojis-list": "3.0.0", "minimist": "1.2.8"}, "_requested": {"type": "version", "registry": true, "raw": "vue-style-loader@4.1.3", "name": "vue-style-loader", "escapedName": "vue-style-loader", "rawSpec": "4.1.3", "saveSpec": null, "fetchSpec": "4.1.3"}, "_requiredBy": ["/laravel-mix"], "_resolved": "https://registry.npmjs.org/vue-style-loader/-/vue-style-loader-4.1.3.tgz", "_spec": "4.1.3", "_where": "/opt/lampp/htdocs/tlcommerce/tlcommerce/themes/tlcommerce", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/vue-style-loader/issues"}, "dependencies": {"hash-sum": "^1.0.2", "loader-utils": "^1.0.2"}, "description": "Vue.js style loader module for webpack", "devDependencies": {"babel-core": "^6.26.0", "babel-jest": "^22.1.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.0", "conventional-changelog-cli": "^2.0.1", "jest": "^22.1.4"}, "homepage": "https://github.com/vuejs/vue-style-loader#readme", "license": "MIT", "name": "vue-style-loader", "repository": {"type": "git", "url": "git+ssh://**************/vuejs/vue-style-loader.git"}, "scripts": {"prepublishOnly": "conventional-changelog -p angular -r 2 -i CHANGELOG.md -s", "test": "jest"}, "version": "4.1.3"}